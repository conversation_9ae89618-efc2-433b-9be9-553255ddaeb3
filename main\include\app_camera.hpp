#pragma once

//#include <list>

#include "esp_camera.h"

#include "__base__.hpp"

extern "C" {
#include "camera_power_manager.h"
}



#define XCLK_FREQ_HZ 15000000

class AppCamera : public Frame
{
public:
    AppCamera(const pixformat_t pixel_fromat,
              const framesize_t frame_size,
              const uint8_t fb_count,
              QueueHandle_t queue_o = nullptr);
    ~AppCamera();

    void run();
    void stop();
    bool stopped;
	uint stop_count;
	TaskHandle_t camera_task_handle;

	// Camera configuration
	pixformat_t pixel_format;
	framesize_t frame_size;
	uint8_t fb_count;
	bool camera_initialized;

	// Initialize camera hardware
	esp_err_t init_camera();

	// Deinitialize camera hardware
	void deinit_camera();

	// Check if camera is initialized
	bool is_camera_initialized() const { return camera_initialized; }

	// Camera health check
	bool camera_health_check();
};
