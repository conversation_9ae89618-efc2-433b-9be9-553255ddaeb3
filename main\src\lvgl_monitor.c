#include "cpu_monitor.h"
#include "bsp/ameter_s3.h"
#include "lvgl_monitor.h"  // Include after BSP headers to avoid macro conflicts
#include "esp_log.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/semphr.h"

static const char* TAG = "LVGL_MON";

// LVGL monitoring state
static struct {
    bool initialized;
    uint32_t max_lock_time_ms;
    uint32_t total_locks;
    uint32_t blocking_count;
    uint32_t lock_start_time;
    bool is_locked;
    SemaphoreHandle_t mutex;
} lvgl_mon = {0};

esp_err_t lvgl_monitor_init(void) {
    if (lvgl_mon.initialized) {
        return ESP_OK;
    }

    // Create mutex
    lvgl_mon.mutex = xSemaphoreCreateMutex();
    if (!lvgl_mon.mutex) {
        ESP_LOGE(TAG, "Failed to create mutex");
        return ESP_ERR_NO_MEM;
    }

    // Initialize statistics
    lvgl_mon.max_lock_time_ms = 0;
    lvgl_mon.total_locks = 0;
    lvgl_mon.blocking_count = 0;
    lvgl_mon.is_locked = false;

    lvgl_mon.initialized = true;
    ESP_LOGI(TAG, "LVGL monitor initialized");
    return ESP_OK;
}

bool lvgl_monitor_lock(uint32_t timeout_ms) {
    if (!lvgl_mon.initialized) {
        // Fallback to original function if not initialized
        return bsp_display_lock(timeout_ms);
    }

    // Record lock attempt
    uint32_t start_time = xTaskGetTickCount();
    
    // Mark LVGL operation start for CPU monitor
    cpu_monitor_lvgl_start();

    // Try to acquire lock
    bool success = bsp_display_lock(timeout_ms);

    if (success) {
        if (xSemaphoreTake(lvgl_mon.mutex, pdMS_TO_TICKS(10)) == pdTRUE) {
            lvgl_mon.lock_start_time = start_time;
            lvgl_mon.is_locked = true;
            lvgl_mon.total_locks++;
            xSemaphoreGive(lvgl_mon.mutex);
        }
    } else {
        // Lock failed - mark end for CPU monitor
        cpu_monitor_lvgl_end();
        ESP_LOGW(TAG, "LVGL lock failed (timeout: %u ms)", timeout_ms);
    }

    return success;
}

void lvgl_monitor_unlock(void) {
    if (!lvgl_mon.initialized) {
        // Fallback to original function if not initialized
        bsp_display_unlock();
        return;
    }

    uint32_t end_time = xTaskGetTickCount();

    if (xSemaphoreTake(lvgl_mon.mutex, pdMS_TO_TICKS(10)) == pdTRUE) {
        if (lvgl_mon.is_locked) {
            uint32_t lock_duration = (end_time - lvgl_mon.lock_start_time) * portTICK_PERIOD_MS;
            
            // Update statistics
            if (lock_duration > lvgl_mon.max_lock_time_ms) {
                lvgl_mon.max_lock_time_ms = lock_duration;
            }
            
            // Check for blocking
            if (lock_duration > 100) {  // Consider 100ms as blocking threshold
                lvgl_mon.blocking_count++;
                ESP_LOGW(TAG, "LVGL lock held for %u ms (blocking detected)", lock_duration);
            }
            
            lvgl_mon.is_locked = false;
        }
        xSemaphoreGive(lvgl_mon.mutex);
    }

    // Mark LVGL operation end for CPU monitor
    cpu_monitor_lvgl_end();

    // Release the actual lock
    bsp_display_unlock();
}

bool lvgl_monitor_is_blocking(void) {
    if (!lvgl_mon.initialized || !lvgl_mon.is_locked) {
        return false;
    }

    uint32_t current_time = xTaskGetTickCount();
    uint32_t lock_duration = (current_time - lvgl_mon.lock_start_time) * portTICK_PERIOD_MS;
    
    return (lock_duration > 100);  // 100ms threshold
}

esp_err_t lvgl_monitor_get_stats(uint32_t* max_lock_time_ms, uint32_t* total_locks, uint32_t* blocking_count) {
    if (!lvgl_mon.initialized || !max_lock_time_ms || !total_locks || !blocking_count) {
        return ESP_ERR_INVALID_ARG;
    }

    if (xSemaphoreTake(lvgl_mon.mutex, pdMS_TO_TICKS(100)) == pdTRUE) {
        *max_lock_time_ms = lvgl_mon.max_lock_time_ms;
        *total_locks = lvgl_mon.total_locks;
        *blocking_count = lvgl_mon.blocking_count;
        xSemaphoreGive(lvgl_mon.mutex);
        return ESP_OK;
    }

    return ESP_ERR_TIMEOUT;
}
