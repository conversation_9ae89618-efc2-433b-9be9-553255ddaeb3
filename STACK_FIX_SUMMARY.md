# 堆疊不足問題修復總結

## 🔍 問題分析

根據 CPU 監控報告，發現以下任務存在堆疊不足警告：

### ⚠️ 堆疊警告任務
- **pcf8563** (RTC任務): 剩餘 1008 bytes ⚠️
- **ReadADC** (ADC讀取任務): 剩餘 1008 bytes ⚠️  
- **ipc1** (核心間通訊任務): 剩餘 536 bytes ⚠️

### ⚠️LOW 嚴重警告
- **ipc0** (核心間通訊任務): 剩餘 432 bytes ⚠️LOW - 有溢出風險

## 🔧 修復措施

### 1. 應用任務堆疊修復

**文件**: `main/app_main.cpp`

#### 修復前：
```c
StackType_t xStackRTC[ configMINIMAL_STACK_SIZE*4 ] __attribute__((section(".ext_ram.bss")));  // ~2048 bytes
StackType_t xStackADC[ 3072 ] __attribute__((section(".ext_ram.bss")));                      // 3072 bytes
```

#### 修復後：
```c
StackType_t xStackRTC[ 4096 ] __attribute__((section(".ext_ram.bss")));  // 4096 bytes (+100%)
StackType_t xStackADC[ 4096 ] __attribute__((section(".ext_ram.bss")));  // 4096 bytes (+33%)
```

#### 任務創建參數更新：
```c
// RTC 任務
xHandle_RTC = xTaskCreateStaticPinnedToCore((TaskFunction_t)pcf8563, "pcf8563", 4096, NULL, 5, xStackRTC, &TaskTCB_RTC, 1);

// ADC 任務  
xHandle_ADC = xTaskCreateStaticPinnedToCore((TaskFunction_t)ReadADC, "ReadADC", 4096, NULL, 3, xStackADC, &TaskTCB_ADC, 1);
```

### 2. 系統任務堆疊修復

**文件**: `sdkconfig`

#### 修復前：
```
CONFIG_ESP_IPC_TASK_STACK_SIZE=1280
```

#### 修復後：
```
CONFIG_ESP_IPC_TASK_STACK_SIZE=2048
```

**影響**: 增加 IPC 任務 (ipc0, ipc1) 堆疊大小 60%，從 1280 bytes 增加到 2048 bytes

## 📊 預期效果

### 堆疊空間改善：
- **pcf8563**: 1008 → ~3000+ bytes (安全)
- **ReadADC**: 1008 → ~3000+ bytes (安全)  
- **ipc0**: 432 → ~1500+ bytes (安全)
- **ipc1**: 536 → ~1500+ bytes (安全)

### 系統穩定性：
- ✅ 消除堆疊溢出風險
- ✅ 提高系統穩定性
- ✅ 防止隨機重啟和崩潰
- ✅ 使用 PSRAM 不影響內部 RAM

## 🎯 技術細節

### 記憶體分配策略：
- **PSRAM 分配**: 所有增加的堆疊空間都分配在 PSRAM 中 (`__attribute__((section(".ext_ram.bss")))`)
- **內部 RAM 保護**: 不影響珍貴的內部 RAM 資源
- **靜態分配**: 使用靜態任務創建確保堆疊空間固定

### 任務優先級維持：
- **pcf8563**: 優先級 5 (不變)
- **ReadADC**: 優先級 3 (不變)
- **IPC 任務**: 系統管理，優先級由 ESP-IDF 控制

## ✅ 驗證方法

編譯並燒錄後，檢查 CPU 監控報告：
```
I (xxxxx) CPU_MON: Task Status (Name | State | Priority | Stack Free | Stack Warning):
I (xxxxx) CPU_MON:   pcf8563      | BLK |  5 | 3000+    # 應該不再有 ⚠️
I (xxxxx) CPU_MON:   ReadADC      | BLK |  3 | 3000+    # 應該不再有 ⚠️  
I (xxxxx) CPU_MON:   ipc0         | SUS |  1 | 1500+    # 應該不再有 ⚠️LOW
I (xxxxx) CPU_MON:   ipc1         | SUS | 24 | 1500+    # 應該不再有 ⚠️
```

## 📝 注意事項

1. **PSRAM 使用**: 增加的堆疊空間使用 PSRAM，不會影響內部 RAM
2. **性能影響**: PSRAM 訪問速度略慢於內部 RAM，但對這些低頻任務影響微乎其微
3. **記憶體總量**: 總共增加約 3KB PSRAM 使用量，在 8MB PSRAM 中佔比極小
4. **系統穩定性**: 大幅提升系統穩定性，避免堆疊溢出導致的崩潰

## 🔄 後續監控

建議定期檢查 CPU 監控報告，確保：
- 所有任務堆疊使用量保持在安全範圍
- 沒有新的堆疊警告出現
- 系統運行穩定無異常重啟
