#pragma once

/**
 * @file memory_manager.h
 * @brief Enhanced memory management for ESP32-S3 with stability improvements
 */

#ifdef __cplusplus
extern "C" {
#endif

#include "esp_heap_caps.h"
#include "esp_log.h"
#include "freertos/FreeRTOS.h"
#include "freertos/semphr.h"
#include <stdbool.h>
#include <stdint.h>

// Memory allocation tracking
typedef struct {
    void* ptr;
    size_t size;
    const char* file;
    int line;
    uint32_t timestamp;
    bool is_system_alloc; // Flag to mark system allocations that shouldn't be reported as leaks
} mem_alloc_info_t;

// Memory statistics
typedef struct {
    size_t total_allocated;
    size_t peak_allocated;
    size_t current_allocated;
    size_t allocation_count;
    size_t free_count;
    size_t failed_allocations;
} mem_stats_t;

// Memory manager configuration
typedef struct {
    bool enable_tracking;
    bool enable_leak_detection;
    bool enable_corruption_check;
    uint32_t max_tracked_allocations;
} mem_config_t;

/**
 * @brief Initialize memory manager
 * @param config Memory manager configuration
 * @return ESP_OK on success
 */
esp_err_t mem_manager_init(const mem_config_t* config);

/**
 * @brief Safe memory allocation with tracking
 * @param size Size to allocate
 * @param caps Memory capabilities
 * @param file Source file name
 * @param line Source line number
 * @return Pointer to allocated memory or NULL
 */
void* mem_safe_malloc(size_t size, uint32_t caps, const char* file, int line);

/**
 * @brief Safe system memory allocation (won't be reported as leak)
 * @param size Size to allocate
 * @param caps Memory capabilities
 * @param file Source file name
 * @param line Source line number
 * @return Pointer to allocated memory or NULL
 */
void* mem_safe_system_malloc(size_t size, uint32_t caps, const char* file, int line);

/**
 * @brief Safe memory free with tracking
 * @param ptr Pointer to free
 * @param file Source file name
 * @param line Source line number
 */
void mem_safe_free(void* ptr, const char* file, int line);

/**
 * @brief Safe memory reallocation
 * @param ptr Original pointer
 * @param size New size
 * @param caps Memory capabilities
 * @param file Source file name
 * @param line Source line number
 * @return Pointer to reallocated memory or NULL
 */
void* mem_safe_realloc(void* ptr, size_t size, uint32_t caps, const char* file, int line);

/**
 * @brief Get memory statistics
 * @param stats Pointer to statistics structure
 */
void mem_get_stats(mem_stats_t* stats);

/**
 * @brief Check for memory leaks
 * @return Number of leaked allocations
 */
uint32_t mem_check_leaks(void);

/**
 * @brief Print memory usage report
 */
void mem_print_report(void);

/**
 * @brief Check memory corruption
 * @return true if corruption detected
 */
bool mem_check_corruption(void);

// Convenience macros
#define SAFE_MALLOC(size, caps) mem_safe_malloc(size, caps, __FILE__, __LINE__)
#define SAFE_SYSTEM_MALLOC(size, caps) mem_safe_system_malloc(size, caps, __FILE__, __LINE__)
#define SAFE_FREE(ptr) mem_safe_free(ptr, __FILE__, __LINE__)
#define SAFE_REALLOC(ptr, size, caps) mem_safe_realloc(ptr, size, caps, __FILE__, __LINE__)

// PSRAM-specific allocations
#define PSRAM_MALLOC(size) SAFE_MALLOC(size, MALLOC_CAP_SPIRAM)
#define INTERNAL_MALLOC(size) SAFE_MALLOC(size, MALLOC_CAP_INTERNAL)
#define INTERNAL_SYSTEM_MALLOC(size) SAFE_SYSTEM_MALLOC(size, MALLOC_CAP_INTERNAL)
#define DMA_MALLOC(size) SAFE_MALLOC(size, MALLOC_CAP_DMA)

#ifdef __cplusplus
}
#endif
