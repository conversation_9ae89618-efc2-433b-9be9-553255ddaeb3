# ESP-IDF 編譯指南

## 🔧 修正的配置文件

已修正以下問題：
1. ✅ 移除BSP配置中不存在的 `trans_size` 字段
2. ✅ 修正 `lvgl_config.h` 中的ESP_LOG使用問題
3. ✅ 修正重複的刷新率定義
4. ✅ 修正包含路徑問題

## 🚀 建議的編譯方法

### 方法1：使用VS Code ESP-IDF擴展 (推薦)

1. **打開VS Code**
2. **確保ESP-IDF擴展已安裝**
3. **使用命令面板**：
   - 按 `Ctrl+Shift+P`
   - 輸入 `ESP-IDF: Build your project`
   - 選擇並執行

### 方法2：使用ESP-IDF終端

1. **打開ESP-IDF終端**：
   - 在VS Code中按 `Ctrl+Shift+P`
   - 輸入 `ESP-IDF: Open ESP-IDF Terminal`
   - 選擇並執行

2. **在ESP-IDF終端中執行**：
   ```bash
   idf.py build
   ```

## 📋 PSRAM最大化配置總結

### 記憶體配置：
- **LVGL記憶體池**：48KB → **1MB** (2000%+ 增加)
- **Layer Buffer**：24KB → **256KB** (1000%+ 增加)
- **Display Buffer**：1/4螢幕 → **全螢幕** (400% 增加)
- **Memory Buffers**：16個 → **64個** (300% 增加)

### Task配置：
- **LVGL Task Stack**：16KB → **32KB** (100% 增加)
- **LVGL Task Priority**：3 → **4** (更高優先級)
- **ScreenChangeEvent Stack**：4KB → **8KB** (100% 增加)

### 預期效果：
- ✅ **完全消除記憶體不足問題**
- ✅ **極致流暢的Screen Changes**
- ✅ **詳細的PSRAM使用監控**
- ✅ **Stack Overflow檢測**

## 🎯 編譯成功後的監控輸出

編譯成功後，您將看到類似以下的監控輸出：

```
I (xxxx) CPU_MON: === CPU Monitor Report ===
I (xxxx) CPU_MON: Memory - Total: 7234567, Min: 6234567, Internal: 234567
I (xxxx) CPU_MON: PSRAM - Free: 6234567, Used: 1765433, Total: 8000000 (22.1% used)
I (xxxx) CPU_MON: Screen Changes: 5 total, 0 failed, max: 150 ms, avg: 120 ms
I (xxxx) CPU_MON: Task Status (Name | State | Priority | Stack Free | Stack Warning):
I (xxxx) CPU_MON:   taskLVGL     | RDY | 4  | 8192
I (xxxx) CPU_MON:   ScreenChangeEvent | BLK | 8  | 4096
```

Screen change時：
```
I (xxxx) SCR_MON: Screen change start: 61 -> 70 (Free heap: 7234567 bytes)
I (xxxx) UI_HELPERS: Screen change: speed=1500, delay=1500 (safe: speed=500, delay=1500)
I (xxxx) SCR_MON: Screen change success: 61 -> 70 (Duration: 150 ms, Free heap: 7234567 -> 7232456 bytes)
```

## ⚠️ 如果編譯仍有問題

請提供具體的編譯錯誤訊息，我將進一步修正配置。
