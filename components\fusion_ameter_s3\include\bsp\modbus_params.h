/*
 * SPDX-FileCopyrightText: 2016-2021 Espressif Systems (Shanghai) CO LTD
 *
 * SPDX-License-Identifier: Apache-2.0
 */

/*=====================================================================================
 * Description:
 *   The Modbus parameter structures used to define Modbus instances that
 *   can be addressed by Modbus protocol. Define these structures per your needs in
 *   your application. Below is just an example of possible parameters.
 *====================================================================================*/
#ifndef _DEVICE_PARAMS
#define _DEVICE_PARAMS

#include <stdint.h>



// This file defines structure of modbus parameters which reflect correspond modbus address space
// for each modbus register type (coils, discreet inputs, holding registers, input registers)
#pragma pack(push, 1)
typedef struct
{
	uint8_t discrete_input0:1;
	uint8_t discrete_input1:1;
	uint8_t discrete_input2:1;
	uint8_t discrete_input3:1;
	uint8_t discrete_input4:1;
	uint8_t discrete_input5:1;
	uint8_t discrete_input6:1;
	uint8_t discrete_input7:1;
	uint8_t discrete_input_port1;
	uint8_t discrete_input_port2;
} discrete_reg_params_t;
#pragma pack(pop)

#pragma pack(push, 1)
typedef struct
{
	uint8_t coils_port0;
	uint8_t coils_port1;
	uint8_t coils_port2;
} coil_reg_params_t;
#pragma pack(pop)

#pragma pack(push, 1)
typedef struct
{
	float input_data0; // 0
	float input_data1; // 2
	float input_data2; // 4
	float input_data3; // 6
	uint16_t data[150]; // 8 + 150 = 158
	float input_data4; // 158
	float input_data5;
	float input_data6;
	float input_data7;
	uint16_t data_block1[150];
} input_reg_params_t;
#pragma pack(pop)

////////////////////////// register on modbus
#pragma pack(push, 1)
typedef struct
{
	float holding_data0;
	float holding_data1;
	float holding_data2;
	uint32_t holding_data3;
	float holding_data4;
	float holding_data5;
	float holding_data6;
	float holding_data7;
	float holding_data8;
	float holding_data9;
	uint8_t holding_data10;
	uint8_t holding_data11;
	uint16_t holding_data12;
	uint8_t holding_data13;
	uint8_t holding_data14;
	uint8_t holding_data15;
	uint32_t holding_data16;
	uint32_t holding_data17;
	uint32_t holding_data18;
	uint32_t holding_data19;
	uint32_t holding_data20;
	uint32_t holding_data21;

	uint8_t holding_data22;
	uint8_t holding_data23;
	uint8_t holding_data24;
	uint8_t holding_data25;
	uint8_t holding_data26;
	uint8_t holding_data27;
	uint8_t holding_data28;
	uint8_t holding_data29;
	uint32_t holding_data30;

	float holding_data31;
	float holding_data32;
	float holding_data33;

	uint32_t holding_data34;

	uint16_t holding_data214;
	uint32_t holding_data215;
	uint32_t holding_data217;

	uint16_t holding_data299; //log
	uint16_t holding_data300; //log
    uint16_t holding_data301, holding_data302, holding_data303, holding_data304,
             holding_data305, holding_data306, holding_data307, holding_data308, holding_data309,
             holding_data310, holding_data311, holding_data312, holding_data313, holding_data314,
             holding_data315, holding_data316, holding_data317, holding_data318, holding_data319,
             holding_data320, holding_data321, holding_data322, holding_data323, holding_data324,
             holding_data325, holding_data326, holding_data327, holding_data328, holding_data329,
             holding_data330, holding_data331, holding_data332, holding_data333, holding_data334,
             holding_data335, holding_data336, holding_data337, holding_data338, holding_data339,
             holding_data340, holding_data341, holding_data342, holding_data343, holding_data344,
             holding_data345, holding_data346, holding_data347, holding_data348, holding_data349,
             holding_data350, holding_data351, holding_data352, holding_data353, holding_data354,
             holding_data355, holding_data356, holding_data357, holding_data358, holding_data359,
             holding_data360, holding_data361, holding_data362, holding_data363, holding_data364,
             holding_data365, holding_data366, holding_data367, holding_data368, holding_data369,
             holding_data370, holding_data371, holding_data372, holding_data373, holding_data374,
             holding_data375, holding_data376, holding_data377, holding_data378, holding_data379,
             holding_data380, holding_data381, holding_data382, holding_data383, holding_data384,
             holding_data385, holding_data386, holding_data387, holding_data388, holding_data389,
             holding_data390, holding_data391, holding_data392, holding_data393, holding_data394,
             holding_data395, holding_data396, holding_data397, holding_data398, holding_data399;

    uint16_t holding_data400; //log

	float holding_data99;
} holding_reg_params_t;
#pragma pack(pop)
/////////////////////////////////////

#pragma pack(push, 1)
typedef struct
{
	//uint16_t holding_data0; // firmware version
	uint16_t holding_data1; // firmware version
	uint16_t holding_data2;
	uint16_t holding_data3;
	uint16_t holding_data4;
	uint16_t holding_data5;
	uint16_t holding_data6;
	uint16_t holding_data7;
	uint16_t holding_data8;
	uint16_t holding_data9;
	uint16_t holding_data10;
	uint16_t holding_data11;
	uint16_t holding_data12;
	uint16_t holding_data13;
	uint16_t holding_data14;
	uint16_t holding_data15;
	uint16_t holding_data16;
	uint16_t holding_data17;
	uint16_t holding_data18;

	uint16_t holding_data19;
	uint16_t holding_data20;
	uint16_t holding_data21;
	uint16_t holding_data22;
	uint16_t holding_data23;
	uint16_t holding_data24;
	uint16_t holding_data25;
	uint16_t holding_data26;

	uint16_t holding_data27;
	uint16_t holding_data28;
	uint16_t holding_data29;
	uint16_t holding_data30;
	uint16_t holding_data31;
	uint16_t holding_data32;
	uint16_t holding_data33;
	uint16_t holding_data34;
	uint16_t holding_data35;
	uint16_t holding_data36;
	uint16_t holding_data37;
	uint16_t holding_data38;
	uint16_t holding_data39;
	uint16_t holding_data40;

	uint16_t holding_data41;
	uint16_t holding_data42;
	uint16_t holding_data43;
	uint16_t holding_data44;
	uint16_t holding_data45;
	uint16_t holding_data46;
	uint16_t holding_data47;
	uint16_t holding_data48;
	uint16_t holding_data49;
	uint16_t holding_data50;
	uint16_t holding_data51;
	uint16_t holding_data52;
	uint16_t holding_data53;
	uint16_t holding_data54;
	uint16_t holding_data55;
	uint16_t holding_data56;
	uint16_t holding_data57;
	uint16_t holding_data58;
	uint16_t holding_data59;
	uint16_t holding_data60;
	uint16_t holding_data61;
	uint16_t holding_data62;
	uint16_t holding_data63;
	uint16_t holding_data64;
	uint16_t holding_data65;
	uint16_t holding_data66;
	uint16_t holding_data67;
	uint16_t holding_data68;
	uint16_t holding_data69;
	uint16_t holding_data70;
	uint16_t holding_data71;
	uint16_t holding_data72;
	uint16_t holding_data73;
	uint16_t holding_data74;
	uint16_t holding_data75;
	uint16_t holding_data76;
	uint16_t holding_data77;
	uint16_t holding_data78;
	uint16_t holding_data79;
	uint16_t holding_data80;
	uint16_t holding_data81;
	uint16_t holding_data82;
	uint16_t holding_data83;
	uint16_t holding_data84;
	uint16_t holding_data85;
	uint16_t holding_data86;
	uint16_t holding_data87;
	uint16_t holding_data88;
	uint16_t holding_data89;
	uint16_t holding_data90;
	uint16_t holding_data91;
	uint16_t holding_data92;
	uint16_t holding_data93;
	uint16_t holding_data94;
	uint16_t holding_data95;
	uint16_t holding_data96;
	uint16_t holding_data97;
	uint16_t holding_data98;
	uint16_t holding_data99;

	uint16_t holding_data100; //log
	uint16_t holding_data101;
	uint16_t holding_data102;
	uint16_t holding_data103;
	uint16_t holding_data104;
	uint16_t holding_data105;
	uint16_t holding_data106;
	uint16_t holding_data107;
	uint16_t holding_data108;
	uint16_t holding_data109;
	uint16_t holding_data110;

	uint16_t holding_data111;
	uint16_t holding_data112;
	uint16_t holding_data113;
	uint16_t holding_data114;
	uint16_t holding_data115;
	uint16_t holding_data116;
	uint16_t holding_data117;
	uint16_t holding_data118;
	uint16_t holding_data119;
	uint16_t holding_data120;
	uint16_t holding_data121;
	uint16_t holding_data122;
	uint16_t holding_data123;
	uint16_t holding_data124;
	uint16_t holding_data125;
	uint16_t holding_data126;
	uint16_t holding_data127;
	uint16_t holding_data128;
	uint16_t holding_data129;
	uint16_t holding_data130;
	uint16_t holding_data131;
	uint16_t holding_data132;
	uint16_t holding_data133;
	uint16_t holding_data134;
	uint16_t holding_data135;
	uint16_t holding_data136;
	uint16_t holding_data137;
	uint16_t holding_data138;
	uint16_t holding_data139;
	uint16_t holding_data140;
	uint16_t holding_data141;
	uint16_t holding_data142;
	uint16_t holding_data143;
	uint16_t holding_data144;
	uint16_t holding_data145;
	uint16_t holding_data146;
	uint16_t holding_data147;
	uint16_t holding_data148;
	uint16_t holding_data149;
	uint16_t holding_data150;
	uint16_t holding_data151;
	uint16_t holding_data152;
	uint16_t holding_data153;
	uint16_t holding_data154;
	uint16_t holding_data155;
	uint16_t holding_data156;
	uint16_t holding_data157;
	uint16_t holding_data158;
	uint16_t holding_data159;
	uint16_t holding_data160;
	uint16_t holding_data161;
	uint16_t holding_data162;
	uint16_t holding_data163;
	uint16_t holding_data164;
	uint16_t holding_data165;
	uint16_t holding_data166;
	uint16_t holding_data167;
	uint16_t holding_data168;
	uint16_t holding_data169;
	uint16_t holding_data170;
	uint16_t holding_data171;
	uint16_t holding_data172;
	uint16_t holding_data173;
	uint16_t holding_data174;
	uint16_t holding_data175;
	uint16_t holding_data176;
	uint16_t holding_data177;
	uint16_t holding_data178;
	uint16_t holding_data179;
	uint16_t holding_data180;
	uint16_t holding_data181;
	uint16_t holding_data182;
	uint16_t holding_data183;
	uint16_t holding_data184;
	uint16_t holding_data185;
	uint16_t holding_data186;
	uint16_t holding_data187;
	uint16_t holding_data188;
	uint16_t holding_data189;
	uint16_t holding_data190;
	uint16_t holding_data191;
	uint16_t holding_data192;
	uint16_t holding_data193;
	uint16_t holding_data194;
	uint16_t holding_data195;
	uint16_t holding_data196;
	uint16_t holding_data197;
	uint16_t holding_data198;
	uint16_t holding_data199;
	uint16_t holding_data200;
	uint16_t holding_data201;
	uint16_t holding_data202;
	uint16_t holding_data203;
	uint16_t holding_data204;
	uint16_t holding_data205;
	uint16_t holding_data206;
	uint16_t holding_data207;
	uint16_t holding_data208;
	uint16_t holding_data209;
	uint16_t holding_data210;
	uint16_t holding_data211;
	uint16_t holding_data212;
	uint16_t holding_data213;
	uint16_t holding_data214;
	uint16_t holding_data215;
	uint16_t holding_data216;
	uint16_t holding_data217;
	uint16_t holding_data218;
	uint16_t holding_data219;
	uint16_t holding_data220;
	uint16_t holding_data221;
	uint16_t holding_data222;
	uint16_t holding_data223;
	uint16_t holding_data224;
	uint16_t holding_data225;
	uint16_t holding_data226;
	uint16_t holding_data227;
	uint16_t holding_data228;
	uint16_t holding_data229;
	uint16_t holding_data230;
	uint16_t holding_data231;
	uint16_t holding_data232;
	uint16_t holding_data233;
	uint16_t holding_data234;
	uint16_t holding_data235;
	uint16_t holding_data236;
	uint16_t holding_data237;
	uint16_t holding_data238;
	uint16_t holding_data239;
	uint16_t holding_data240;
	uint16_t holding_data241;
	uint16_t holding_data242;
	uint16_t holding_data243;
	uint16_t holding_data244;
	uint16_t holding_data245;
	uint16_t holding_data246;
	uint16_t holding_data247;
	uint16_t holding_data248;
	uint16_t holding_data249;
	uint16_t holding_data250;
	uint16_t holding_data251;
	uint16_t holding_data252;
	uint16_t holding_data253;
	uint16_t holding_data254;
	uint16_t holding_data255;
	uint16_t holding_data256;
	uint16_t holding_data257;
	uint16_t holding_data258;
	uint16_t holding_data259;
	uint16_t holding_data260;
	uint16_t holding_data261;
	uint16_t holding_data262;
	uint16_t holding_data263;
	uint16_t holding_data264;
	uint16_t holding_data265;
	uint16_t holding_data266;
	uint16_t holding_data267;
	uint16_t holding_data268;
	uint16_t holding_data269;
	uint16_t holding_data270;
	uint16_t holding_data271;
	uint16_t holding_data272;
	uint16_t holding_data273;
	uint16_t holding_data274;
	uint16_t holding_data275;
	uint16_t holding_data276;
	uint16_t holding_data277;
	uint16_t holding_data278;
	uint16_t holding_data279;
	uint16_t holding_data280;
	uint16_t holding_data281;
	uint16_t holding_data282;
	uint16_t holding_data283;
	uint16_t holding_data284;
	uint16_t holding_data285;
	uint16_t holding_data286;
	uint16_t holding_data287;
	uint16_t holding_data288;
	uint16_t holding_data289;
	uint16_t holding_data290;
	uint16_t holding_data291;
	uint16_t holding_data292;
	uint16_t holding_data293;
	uint16_t holding_data294;
	uint16_t holding_data295;
	uint16_t holding_data296;
	uint16_t holding_data297;
	uint16_t holding_data298;
	uint16_t holding_data299;
	uint16_t holding_data300;
	uint16_t holding_data301;
	uint16_t holding_data302;
	uint16_t holding_data303;
	uint16_t holding_data304;
	uint16_t holding_data305;
	uint16_t holding_data306;
	uint16_t holding_data307;
	uint16_t holding_data308;
	uint16_t holding_data309;
	uint16_t holding_data310;
	uint16_t holding_data311;
	uint16_t holding_data312;
	uint16_t holding_data313;
	uint16_t holding_data314;
	uint16_t holding_data315;
	uint16_t holding_data316;
	uint16_t holding_data317;
	uint16_t holding_data318;
	uint16_t holding_data319;
	uint16_t holding_data320;
	uint16_t holding_data321;
	uint16_t holding_data322;
	uint16_t holding_data323;
	uint16_t holding_data324;
	uint16_t holding_data325;
	uint16_t holding_data326;
	uint16_t holding_data327;
	uint16_t holding_data328;
	uint16_t holding_data329;
	uint16_t holding_data330;
	uint16_t holding_data331;
	uint16_t holding_data332;
	uint16_t holding_data333;
	uint16_t holding_data334;
	uint16_t holding_data335;
	uint16_t holding_data336;
	uint16_t holding_data337;
	uint16_t holding_data338;
	uint16_t holding_data339;
	uint16_t holding_data340;
	uint16_t holding_data341;
	uint16_t holding_data342;
	uint16_t holding_data343;
	uint16_t holding_data344;
	uint16_t holding_data345;
	uint16_t holding_data346;
	uint16_t holding_data347;
	uint16_t holding_data348;
	uint16_t holding_data349;
	uint16_t holding_data350;
	uint16_t holding_data351;
	uint16_t holding_data352;
	uint16_t holding_data353;
	uint16_t holding_data354;
	uint16_t holding_data355;
	uint16_t holding_data356;
	uint16_t holding_data357;
	uint16_t holding_data358;
	uint16_t holding_data359;
	uint16_t holding_data360;
	uint16_t holding_data361;
	uint16_t holding_data362;
	uint16_t holding_data363;
	uint16_t holding_data364;
	uint16_t holding_data365;
	uint16_t holding_data366;
	uint16_t holding_data367;
	uint16_t holding_data368;
	uint16_t holding_data369;
	uint16_t holding_data370;
	uint16_t holding_data371;
	uint16_t holding_data372;
	uint16_t holding_data373;
	uint16_t holding_data374;
	uint16_t holding_data375;
	uint16_t holding_data376;
	uint16_t holding_data377;
	uint16_t holding_data378;
	uint16_t holding_data379;
	uint16_t holding_data380;
	uint16_t holding_data381;
	uint16_t holding_data382;
	uint16_t holding_data383;
	uint16_t holding_data384;
	uint16_t holding_data385;
	uint16_t holding_data386;
	uint16_t holding_data387;
	uint16_t holding_data388;
	uint16_t holding_data389;
	uint16_t holding_data390;
	uint16_t holding_data391;
	uint16_t holding_data392;
	uint16_t holding_data393;
	uint16_t holding_data394;
	uint16_t holding_data395;
	uint16_t holding_data396;
	uint16_t holding_data397;
	uint16_t holding_data398;
	uint16_t holding_data399;
	uint16_t holding_data400;
	uint16_t holding_data401;
	uint16_t holding_data402;
	uint16_t holding_data403;
	uint16_t holding_data404;
	uint16_t holding_data405;
	uint16_t holding_data406;
	uint16_t holding_data407;
	uint16_t holding_data408;
	uint16_t holding_data409;
	uint16_t holding_data410;
	uint16_t holding_data411;
	uint16_t holding_data412;
	uint16_t holding_data413;
	uint16_t holding_data414;
	uint16_t holding_data415;
	uint16_t holding_data416;
	uint16_t holding_data417;
	uint16_t holding_data418;
	uint16_t holding_data419;
	uint16_t holding_data420;
	uint16_t holding_data421;
	uint16_t holding_data422;
	uint16_t holding_data423;
	uint16_t holding_data424;
	uint16_t holding_data425;
	uint16_t holding_data426;
	uint16_t holding_data427;
	uint16_t holding_data428;
	uint16_t holding_data429;
	uint16_t holding_data430;
	uint16_t holding_data431;
	uint16_t holding_data432;
	uint16_t holding_data433;
	uint16_t holding_data434;
	uint16_t holding_data435;
	uint16_t holding_data436;
	uint16_t holding_data437;
	uint16_t holding_data438;
	uint16_t holding_data439;
	uint16_t holding_data440;
	uint16_t holding_data441;
	uint16_t holding_data442;
	uint16_t holding_data443;
	uint16_t holding_data444;
	uint16_t holding_data445;
	uint16_t holding_data446;
	uint16_t holding_data447;
	uint16_t holding_data448;
	uint16_t holding_data449;
	uint16_t holding_data450;
	uint16_t holding_data451;
	uint16_t holding_data452;
	uint16_t holding_data453;
	uint16_t holding_data454;
	uint16_t holding_data455;
	uint16_t holding_data456;
	uint16_t holding_data457;
	uint16_t holding_data458;
	uint16_t holding_data459;
	uint16_t holding_data460;
	uint16_t holding_data461;
	uint16_t holding_data462;
	uint16_t holding_data463;
	uint16_t holding_data464;
	uint16_t holding_data465;
	uint16_t holding_data466;
	uint16_t holding_data467;
	uint16_t holding_data468;
	uint16_t holding_data469;
	uint16_t holding_data470;
	uint16_t holding_data471;
	uint16_t holding_data472;
	uint16_t holding_data473;
	uint16_t holding_data474;
	uint16_t holding_data475;
	uint16_t holding_data476;
	uint16_t holding_data477;
	uint16_t holding_data478;
	uint16_t holding_data479;
	uint16_t holding_data480;
	uint16_t holding_data481;
	uint16_t holding_data482;
	uint16_t holding_data483;
	uint16_t holding_data484;
	uint16_t holding_data485;
	uint16_t holding_data486;
	uint16_t holding_data487;
	uint16_t holding_data488;
	uint16_t holding_data489;
	uint16_t holding_data490;
	uint16_t holding_data491;
	uint16_t holding_data492;
	uint16_t holding_data493;
	uint16_t holding_data494;
	uint16_t holding_data495;
	uint16_t holding_data496;
	uint16_t holding_data497;
	uint16_t holding_data498;
	uint16_t holding_data499;
	uint16_t holding_data500;
	uint16_t holding_data501;
	uint16_t holding_data502;
	uint16_t holding_data503;
	uint16_t holding_data504;
	uint16_t holding_data505;
	uint16_t holding_data506;
	uint16_t holding_data507;
	uint16_t holding_data508;
	uint16_t holding_data509;
	uint16_t holding_data510;
	uint16_t holding_data511;
	uint16_t holding_data512;
	uint16_t holding_data513;
	uint16_t holding_data514;
	uint16_t holding_data515;
	uint16_t holding_data516;
	uint16_t holding_data517;
	uint16_t holding_data518;
	uint16_t holding_data519;
	uint16_t holding_data520;
	uint16_t holding_data521;
	uint16_t holding_data522;
	uint16_t holding_data523;
	uint16_t holding_data524;
	uint16_t holding_data525;
	uint16_t holding_data526;
	uint16_t holding_data527;
	uint16_t holding_data528;
	uint16_t holding_data529;
	uint16_t holding_data530;
	uint16_t holding_data531;
	uint16_t holding_data532;
	uint16_t holding_data533;
	uint16_t holding_data534;
	uint16_t holding_data535;
	uint16_t holding_data536;
	uint16_t holding_data537;
	uint16_t holding_data538;
	uint16_t holding_data539;
	uint16_t holding_data540;
	uint16_t holding_data541;
	uint16_t holding_data542;
	uint16_t holding_data543;
	uint16_t holding_data544;
	uint16_t holding_data545;
	uint16_t holding_data546;
	uint16_t holding_data547;
	uint16_t holding_data548;
	uint16_t holding_data549;
	uint16_t holding_data550;
	uint16_t holding_data551;
	uint16_t holding_data552;
	uint16_t holding_data553;
	uint16_t holding_data554;
	uint16_t holding_data555;
	uint16_t holding_data556;
	uint16_t holding_data557;
	uint16_t holding_data558;
	uint16_t holding_data559;
	uint16_t holding_data560;
	uint16_t holding_data561;
	uint16_t holding_data562;
	uint16_t holding_data563;
	uint16_t holding_data564;
	uint16_t holding_data565;
	uint16_t holding_data566;
	uint16_t holding_data567;
	uint16_t holding_data568;
	uint16_t holding_data569;
	uint16_t holding_data570;
	uint16_t holding_data571;
	uint16_t holding_data572;
	uint16_t holding_data573;
	uint16_t holding_data574;
	uint16_t holding_data575;
	uint16_t holding_data576;
	uint16_t holding_data577;
	uint16_t holding_data578;
	uint16_t holding_data579;
	uint16_t holding_data580;
	uint16_t holding_data581;
	uint16_t holding_data582;
	uint16_t holding_data583;
	uint16_t holding_data584;
	uint16_t holding_data585;
	uint16_t holding_data586;
	uint16_t holding_data587;
	uint16_t holding_data588;
	uint16_t holding_data589;
	uint16_t holding_data590;
	uint16_t holding_data591;
	uint16_t holding_data592;
	uint16_t holding_data593;
	uint16_t holding_data594;
	uint16_t holding_data595;
	uint16_t holding_data596;
	uint16_t holding_data597;
	uint16_t holding_data598;
	uint16_t holding_data599;
	uint16_t holding_data600;
	uint16_t holding_data601;
	uint16_t holding_data602;
	uint16_t holding_data603;
	uint16_t holding_data604;
	uint16_t holding_data605;
	uint16_t holding_data606;
	uint16_t holding_data607;
	uint16_t holding_data608;
	uint16_t holding_data609;
	uint16_t holding_data610;
	uint16_t holding_data611;
	uint16_t holding_data612;
	uint16_t holding_data613;
	uint16_t holding_data614;
	uint16_t holding_data615;
	uint16_t holding_data616;
	uint16_t holding_data617;
	uint16_t holding_data618;
	uint16_t holding_data619;
	uint16_t holding_data620;
	uint16_t holding_data621;
	uint16_t holding_data622;
	uint16_t holding_data623;
	uint16_t holding_data624;
	uint16_t holding_data625;
	uint16_t holding_data626;
	uint16_t holding_data627;
	uint16_t holding_data628;
	uint16_t holding_data629;
	uint16_t holding_data630;
	uint16_t holding_data631;
	uint16_t holding_data632;
	uint16_t holding_data633;
	uint16_t holding_data634;
	uint16_t holding_data635;
	uint16_t holding_data636;
	uint16_t holding_data637;
	uint16_t holding_data638;
	uint16_t holding_data639;
	uint16_t holding_data640;
	uint16_t holding_data641;
	uint16_t holding_data642;
	uint16_t holding_data643;
	uint16_t holding_data644;
	uint16_t holding_data645;
	uint16_t holding_data646;
	uint16_t holding_data647;
	uint16_t holding_data648;
	uint16_t holding_data649;
	uint16_t holding_data650;
	uint16_t holding_data651;
	uint16_t holding_data652;
	uint16_t holding_data653;
	uint16_t holding_data654;
	uint16_t holding_data655;
	uint16_t holding_data656;
	uint16_t holding_data657;
	uint16_t holding_data658;
	uint16_t holding_data659;
	uint16_t holding_data660;
	uint16_t holding_data661;
	uint16_t holding_data662;
	uint16_t holding_data663;
	uint16_t holding_data664;
	uint16_t holding_data665;
	uint16_t holding_data666;
	uint16_t holding_data667;
	uint16_t holding_data668;
	uint16_t holding_data669;
	uint16_t holding_data670;
	uint16_t holding_data671;
	uint16_t holding_data672;
	uint16_t holding_data673;
	uint16_t holding_data674;
	uint16_t holding_data675;
	uint16_t holding_data676;
	uint16_t holding_data677;
	uint16_t holding_data678;
	uint16_t holding_data679;
	uint16_t holding_data680;
	uint16_t holding_data681;
	uint16_t holding_data682;
	uint16_t holding_data683;
	uint16_t holding_data684;
	uint16_t holding_data685;
	uint16_t holding_data686;
	uint16_t holding_data687;
	uint16_t holding_data688;
	uint16_t holding_data689;
	uint16_t holding_data690;
	uint16_t holding_data691;
	uint16_t holding_data692;
	uint16_t holding_data693;
	uint16_t holding_data694;
	uint16_t holding_data695;
	uint16_t holding_data696;
	uint16_t holding_data697;
	uint16_t holding_data698;
	uint16_t holding_data699;
	uint16_t holding_data700;
	uint16_t holding_data701;
	uint16_t holding_data702;
	uint16_t holding_data703;
	uint16_t holding_data704;
	uint16_t holding_data705;
	uint16_t holding_data706;
	uint16_t holding_data707;
	uint16_t holding_data708;
	uint16_t holding_data709;
	uint16_t holding_data710;
	uint16_t holding_data711;
	uint16_t holding_data712;
	uint16_t holding_data713;
	uint16_t holding_data714;
	uint16_t holding_data715;
	uint16_t holding_data716;
	uint16_t holding_data717;
	uint16_t holding_data718;
	uint16_t holding_data719;
	uint16_t holding_data720;
	uint16_t holding_data721;
	uint16_t holding_data722;
	uint16_t holding_data723;
	uint16_t holding_data724;
	uint16_t holding_data725;
	uint16_t holding_data726;
	uint16_t holding_data727;
	uint16_t holding_data728;
	uint16_t holding_data729;
	uint16_t holding_data730;
	uint16_t holding_data731;
	uint16_t holding_data732;
	uint16_t holding_data733;
	uint16_t holding_data734;
	uint16_t holding_data735;
	uint16_t holding_data736;
	uint16_t holding_data737;
	uint16_t holding_data738;
	uint16_t holding_data739;
	uint16_t holding_data740;
	uint16_t holding_data741;
	uint16_t holding_data742;
	uint16_t holding_data743;
	uint16_t holding_data744;
	uint16_t holding_data745;
	uint16_t holding_data746;
	uint16_t holding_data747;
	uint16_t holding_data748;
	uint16_t holding_data749;
	uint16_t holding_data750;
	uint16_t holding_data751;
	uint16_t holding_data752;
	uint16_t holding_data753;
	uint16_t holding_data754;
	uint16_t holding_data755;
	uint16_t holding_data756;
	uint16_t holding_data757;
	uint16_t holding_data758;
	uint16_t holding_data759;
	uint16_t holding_data760;
	uint16_t holding_data761;
	uint16_t holding_data762;
	uint16_t holding_data763;
	uint16_t holding_data764;
	uint16_t holding_data765;
	uint16_t holding_data766;
	uint16_t holding_data767;
	uint16_t holding_data768;
	uint16_t holding_data769;
	uint16_t holding_data770;
	uint16_t holding_data771;
	uint16_t holding_data772;
	uint16_t holding_data773;
	uint16_t holding_data774;
	uint16_t holding_data775;
	uint16_t holding_data776;
	uint16_t holding_data777;
	uint16_t holding_data778;
	uint16_t holding_data779;
	uint16_t holding_data780;
	uint16_t holding_data781;
	uint16_t holding_data782;
	uint16_t holding_data783;
	uint16_t holding_data784;
	uint16_t holding_data785;
	uint16_t holding_data786;
	uint16_t holding_data787;
	uint16_t holding_data788;
	uint16_t holding_data789;
	uint16_t holding_data790;
	uint16_t holding_data791;
	uint16_t holding_data792;
	uint16_t holding_data793;
	uint16_t holding_data794;
	uint16_t holding_data795;
	uint16_t holding_data796;
	uint16_t holding_data797;
	uint16_t holding_data798;
	uint16_t holding_data799;
	uint16_t holding_data800;
	uint16_t holding_data801;
	uint16_t holding_data802;
	uint16_t holding_data803;
	uint16_t holding_data804;
	uint16_t holding_data805;
	uint16_t holding_data806;
	uint16_t holding_data807;
	uint16_t holding_data808;
	uint16_t holding_data809;
	uint16_t holding_data810;
	uint16_t holding_data811;
	uint16_t holding_data812;
	uint16_t holding_data813;
	uint16_t holding_data814;
	uint16_t holding_data815;
	uint16_t holding_data816;
	uint16_t holding_data817;
	uint16_t holding_data818;
	uint16_t holding_data819;
	uint16_t holding_data820;
	uint16_t holding_data821;
	uint16_t holding_data822;
	uint16_t holding_data823;
	uint16_t holding_data824;
	uint16_t holding_data825;
	uint16_t holding_data826;
	uint16_t holding_data827;
	uint16_t holding_data828;
	uint16_t holding_data829;
	uint16_t holding_data830;
	uint16_t holding_data831;
	uint16_t holding_data832;
	uint16_t holding_data833;
	uint16_t holding_data834;
	uint16_t holding_data835;
	uint16_t holding_data836;
	uint16_t holding_data837;
	uint16_t holding_data838;
	uint16_t holding_data839;
	uint16_t holding_data840;
	uint16_t holding_data841;
	uint16_t holding_data842;
	uint16_t holding_data843;
	uint16_t holding_data844;
	uint16_t holding_data845;
	uint16_t holding_data846;
	uint16_t holding_data847;
	uint16_t holding_data848;
	uint16_t holding_data849;
	uint16_t holding_data850;
	uint16_t holding_data851;
	uint16_t holding_data852;
	uint16_t holding_data853;
	uint16_t holding_data854;
	uint16_t holding_data855;
	uint16_t holding_data856;
	uint16_t holding_data857;
	uint16_t holding_data858;
	uint16_t holding_data859;
	uint16_t holding_data860;
	uint16_t holding_data861;
	uint16_t holding_data862;
	uint16_t holding_data863;
	uint16_t holding_data864;
	uint16_t holding_data865;
	uint16_t holding_data866;
	uint16_t holding_data867;
	uint16_t holding_data868;
	uint16_t holding_data869;
	uint16_t holding_data870;
	uint16_t holding_data871;
	uint16_t holding_data872;
	uint16_t holding_data873;
	uint16_t holding_data874;
	uint16_t holding_data875;
	uint16_t holding_data876;
	uint16_t holding_data877;
	uint16_t holding_data878;
	uint16_t holding_data879;
	uint16_t holding_data880;
	uint16_t holding_data881;
	uint16_t holding_data882;
	uint16_t holding_data883;
	uint16_t holding_data884;
	uint16_t holding_data885;
	uint16_t holding_data886;
	uint16_t holding_data887;
	uint16_t holding_data888;
	uint16_t holding_data889;
	uint16_t holding_data890;
	uint16_t holding_data891;
	uint16_t holding_data892;
	uint16_t holding_data893;
	uint16_t holding_data894;
	uint16_t holding_data895;
	uint16_t holding_data896;
	uint16_t holding_data897;
	uint16_t holding_data898;
	uint16_t holding_data899;
	uint16_t holding_data900;
	uint16_t holding_data901;
	uint16_t holding_data902;
	uint16_t holding_data903;
	uint16_t holding_data904;
	uint16_t holding_data905;
	uint16_t holding_data906;
	uint16_t holding_data907;
	uint16_t holding_data908;
	uint16_t holding_data909;
	uint16_t holding_data910;
	uint16_t holding_data911;
	uint16_t holding_data912;
	uint16_t holding_data913;
	uint16_t holding_data914;
	uint16_t holding_data915;
	uint16_t holding_data916;
	uint16_t holding_data917;
	uint16_t holding_data918;
	uint16_t holding_data919;
	uint16_t holding_data920;
	uint16_t holding_data921;
	uint16_t holding_data922;
	uint16_t holding_data923;
	uint16_t holding_data924;
	uint16_t holding_data925;
	uint16_t holding_data926;
	uint16_t holding_data927;
	uint16_t holding_data928;
	uint16_t holding_data929;
	uint16_t holding_data930;
	uint16_t holding_data931;
	uint16_t holding_data932;
	uint16_t holding_data933;
	uint16_t holding_data934;
	uint16_t holding_data935;
	uint16_t holding_data936;
	uint16_t holding_data937;
	uint16_t holding_data938;
	uint16_t holding_data939;
	uint16_t holding_data940;
	uint16_t holding_data941;
	uint16_t holding_data942;
	uint16_t holding_data943;
	uint16_t holding_data944;
	uint16_t holding_data945;
	uint16_t holding_data946;
	uint16_t holding_data947;
	uint16_t holding_data948;
	uint16_t holding_data949;
	uint16_t holding_data950;
	uint16_t holding_data951;
	uint16_t holding_data952;
	uint16_t holding_data953;
	uint16_t holding_data954;
	uint16_t holding_data955;
	uint16_t holding_data956;
	uint16_t holding_data957;
	uint16_t holding_data958;
	uint16_t holding_data959;
	uint16_t holding_data960;
	uint16_t holding_data961;
	uint16_t holding_data962;
	uint16_t holding_data963;
	uint16_t holding_data964;
	uint16_t holding_data965;
	uint16_t holding_data966;
	uint16_t holding_data967;
	uint16_t holding_data968;
	uint16_t holding_data969;
	uint16_t holding_data970;
	uint16_t holding_data971;
	uint16_t holding_data972;
	uint16_t holding_data973;
	uint16_t holding_data974;
	uint16_t holding_data975;
	uint16_t holding_data976;
	uint16_t holding_data977;
	uint16_t holding_data978;
	uint16_t holding_data979;
	uint16_t holding_data980;
	uint16_t holding_data981;
	uint16_t holding_data982;
	uint16_t holding_data983;
	uint16_t holding_data984;
	uint16_t holding_data985;
	uint16_t holding_data986;
	uint16_t holding_data987;
	uint16_t holding_data988;
	uint16_t holding_data989;
	uint16_t holding_data990;
	uint16_t holding_data991;
	uint16_t holding_data992;
	uint16_t holding_data993;
	uint16_t holding_data994;
	uint16_t holding_data995;
	uint16_t holding_data996;
	uint16_t holding_data997;
	uint16_t holding_data998;
	uint16_t holding_data999;
	uint16_t holding_data1000;
	uint16_t holding_data1001;
	uint16_t holding_data1002;
	uint16_t holding_data1003;
	uint16_t holding_data1004;
	uint16_t holding_data1005;
	uint16_t holding_data1006;
	uint16_t holding_data1007;
	uint16_t holding_data1008;
	uint16_t holding_data1009;
	uint16_t holding_data1010;
	uint16_t holding_data1011;
	uint16_t holding_data1012;
	uint16_t holding_data1013;
	uint16_t holding_data1014;
	uint16_t holding_data1015;
	uint16_t holding_data1016;
	uint16_t holding_data1017;
	uint16_t holding_data1018;
	uint16_t holding_data1019;
	uint16_t holding_data1020;
	uint16_t holding_data1021;
	uint16_t holding_data1022;
	uint16_t holding_data1023;
	uint16_t holding_data1024;
	uint16_t holding_data1025;
	uint16_t holding_data1026;
	uint16_t holding_data1027;
	uint16_t holding_data1028;
	uint16_t holding_data1029;
	uint16_t holding_data1030;
	uint16_t holding_data1031;
	uint16_t holding_data1032;
	uint16_t holding_data1033;
	uint16_t holding_data1034;
	uint16_t holding_data1035;
	uint16_t holding_data1036;
	uint16_t holding_data1037;
	uint16_t holding_data1038;
	uint16_t holding_data1039;
	uint16_t holding_data1040;
	uint16_t holding_data1041;
	uint16_t holding_data1042;
	uint16_t holding_data1043;
	uint16_t holding_data1044;
	uint16_t holding_data1045;
	uint16_t holding_data1046;
	uint16_t holding_data1047;
	uint16_t holding_data1048;
	uint16_t holding_data1049;
	uint16_t holding_data1050;
	uint16_t holding_data1051;
	uint16_t holding_data1052;
	uint16_t holding_data1053;
	uint16_t holding_data1054;
	uint16_t holding_data1055;
	uint16_t holding_data1056;
	uint16_t holding_data1057;
	uint16_t holding_data1058;
	uint16_t holding_data1059;
	uint16_t holding_data1060;
	uint16_t holding_data1061;
	uint16_t holding_data1062;
	uint16_t holding_data1063;
	uint16_t holding_data1064;
	uint16_t holding_data1065;
	uint16_t holding_data1066;
	uint16_t holding_data1067;
	uint16_t holding_data1068;
	uint16_t holding_data1069;
	uint16_t holding_data1070;
	uint16_t holding_data1071;
	uint16_t holding_data1072;
	uint16_t holding_data1073;
	uint16_t holding_data1074;
	uint16_t holding_data1075;
	uint16_t holding_data1076;
	uint16_t holding_data1077;
	uint16_t holding_data1078;
	uint16_t holding_data1079;
	uint16_t holding_data1080;
	uint16_t holding_data1081;
	uint16_t holding_data1082;
	uint16_t holding_data1083;
	uint16_t holding_data1084;
	uint16_t holding_data1085;
	uint16_t holding_data1086;
	uint16_t holding_data1087;
	uint16_t holding_data1088;
	uint16_t holding_data1089;
	uint16_t holding_data1090;
	uint16_t holding_data1091;
	uint16_t holding_data1092;
	uint16_t holding_data1093;
	uint16_t holding_data1094;
	uint16_t holding_data1095;
	uint16_t holding_data1096;
	uint16_t holding_data1097;
	uint16_t holding_data1098;
	uint16_t holding_data1099;
	uint16_t holding_data1100;
	uint16_t holding_data1101;
	uint16_t holding_data1102;
	uint16_t holding_data1103;
	uint16_t holding_data1104;
	uint16_t holding_data1105;
	uint16_t holding_data1106;
	uint16_t holding_data1107;
	uint16_t holding_data1108;
	uint16_t holding_data1109;
	uint16_t holding_data1110;
	uint16_t holding_data1111;
	uint16_t holding_data1112;
	uint16_t holding_data1113;
	uint16_t holding_data1114;
	uint16_t holding_data1115;
	uint16_t holding_data1116;
	uint16_t holding_data1117;
	uint16_t holding_data1118;
	uint16_t holding_data1119;
	uint16_t holding_data1120;
	uint16_t holding_data1121;
	uint16_t holding_data1122;
	uint16_t holding_data1123;
	uint16_t holding_data1124;
	uint16_t holding_data1125;
	uint16_t holding_data1126;
	uint16_t holding_data1127;
	uint16_t holding_data1128;
	uint16_t holding_data1129;
	uint16_t holding_data1130;
	uint16_t holding_data1131;
	uint16_t holding_data1132;
	uint16_t holding_data1133;
	uint16_t holding_data1134;
	uint16_t holding_data1135;
	uint16_t holding_data1136;
	uint16_t holding_data1137;
	uint16_t holding_data1138;
	uint16_t holding_data1139;
	uint16_t holding_data1140;
	uint16_t holding_data1141;
	uint16_t holding_data1142;
	uint16_t holding_data1143;
	uint16_t holding_data1144;
	uint16_t holding_data1145;
	uint16_t holding_data1146;
	uint16_t holding_data1147;
	uint16_t holding_data1148;
	uint16_t holding_data1149;
	uint16_t holding_data1150;
	uint16_t holding_data1151;
	uint16_t holding_data1152;
	uint16_t holding_data1153;
	uint16_t holding_data1154;
	uint16_t holding_data1155;
	uint16_t holding_data1156;
	uint16_t holding_data1157;
	uint16_t holding_data1158;
	uint16_t holding_data1159;
	uint16_t holding_data1160;
	uint16_t holding_data1161;
	uint16_t holding_data1162;
	uint16_t holding_data1163;
	uint16_t holding_data1164;
	uint16_t holding_data1165;
	uint16_t holding_data1166;
	uint16_t holding_data1167;
	uint16_t holding_data1168;
	uint16_t holding_data1169;
	uint16_t holding_data1170;
	uint16_t holding_data1171;
	uint16_t holding_data1172;
	uint16_t holding_data1173;
	uint16_t holding_data1174;
	uint16_t holding_data1175;
	uint16_t holding_data1176;
	uint16_t holding_data1177;
	uint16_t holding_data1178;
	uint16_t holding_data1179;
	uint16_t holding_data1180;
	uint16_t holding_data1181;
	uint16_t holding_data1182;
	uint16_t holding_data1183;
	uint16_t holding_data1184;
	uint16_t holding_data1185;
	uint16_t holding_data1186;
	uint16_t holding_data1187;
	uint16_t holding_data1188;
	uint16_t holding_data1189;
	uint16_t holding_data1190;
	uint16_t holding_data1191;
	uint16_t holding_data1192;
	uint16_t holding_data1193;
	uint16_t holding_data1194;
	uint16_t holding_data1195;
	uint16_t holding_data1196;
	uint16_t holding_data1197;
	uint16_t holding_data1198;
	uint16_t holding_data1199;
	uint16_t holding_data1200;
	uint16_t holding_data1201;
	uint16_t holding_data1202;
	uint16_t holding_data1203;
	uint16_t holding_data1204;
	uint16_t holding_data1205;
	uint16_t holding_data1206;
	uint16_t holding_data1207;
	uint16_t holding_data1208;
	uint16_t holding_data1209;
	uint16_t holding_data1210;
	uint16_t holding_data1211;
	uint16_t holding_data1212;
	uint16_t holding_data1213;
	uint16_t holding_data1214;
	uint16_t holding_data1215;
	uint16_t holding_data1216;
	uint16_t holding_data1217;
	uint16_t holding_data1218;
	uint16_t holding_data1219;
	uint16_t holding_data1220;
	uint16_t holding_data1221;
	uint16_t holding_data1222;
	uint16_t holding_data1223;
	uint16_t holding_data1224;
	uint16_t holding_data1225;
	uint16_t holding_data1226;
	uint16_t holding_data1227;
	uint16_t holding_data1228;
	uint16_t holding_data1229;
	uint16_t holding_data1230;
	uint16_t holding_data1231;
	uint16_t holding_data1232;
	uint16_t holding_data1233;
	uint16_t holding_data1234;
	uint16_t holding_data1235;
	uint16_t holding_data1236;
	uint16_t holding_data1237;
	uint16_t holding_data1238;
	uint16_t holding_data1239;
	uint16_t holding_data1240;
	uint16_t holding_data1241;
	uint16_t holding_data1242;
	uint16_t holding_data1243;
	uint16_t holding_data1244;
	uint16_t holding_data1245;
	uint16_t holding_data1246;
	uint16_t holding_data1247;
	uint16_t holding_data1248;
	uint16_t holding_data1249;
	uint16_t holding_data1250;
	uint16_t holding_data1251;
	uint16_t holding_data1252;
	uint16_t holding_data1253;
	uint16_t holding_data1254;
	uint16_t holding_data1255;
	uint16_t holding_data1256;
	uint16_t holding_data1257;
	uint16_t holding_data1258;
	uint16_t holding_data1259;
	uint16_t holding_data1260;
	uint16_t holding_data1261;
	uint16_t holding_data1262;
	uint16_t holding_data1263;
	uint16_t holding_data1264;
	uint16_t holding_data1265;
	uint16_t holding_data1266;
	uint16_t holding_data1267;
	uint16_t holding_data1268;
	uint16_t holding_data1269;
	uint16_t holding_data1270;
	uint16_t holding_data1271;
	uint16_t holding_data1272;
	uint16_t holding_data1273;
	uint16_t holding_data1274;
	uint16_t holding_data1275;
	uint16_t holding_data1276;
	uint16_t holding_data1277;
	uint16_t holding_data1278;
	uint16_t holding_data1279;
	uint16_t holding_data1280;
	uint16_t holding_data1281;
	uint16_t holding_data1282;
	uint16_t holding_data1283;
	uint16_t holding_data1284;
	uint16_t holding_data1285;
	uint16_t holding_data1286;
	uint16_t holding_data1287;
	uint16_t holding_data1288;
	uint16_t holding_data1289;
	uint16_t holding_data1290;
	uint16_t holding_data1291;
	uint16_t holding_data1292;
	uint16_t holding_data1293;
	uint16_t holding_data1294;
	uint16_t holding_data1295;
	uint16_t holding_data1296;
	uint16_t holding_data1297;
	uint16_t holding_data1298;
	uint16_t holding_data1299;
	uint16_t holding_data1300;
	uint16_t holding_data1301;
	uint16_t holding_data1302;
	uint16_t holding_data1303;
	uint16_t holding_data1304;
	uint16_t holding_data1305;
	uint16_t holding_data1306;
	uint16_t holding_data1307;
	uint16_t holding_data1308;
	uint16_t holding_data1309;
	uint16_t holding_data1310;
	uint16_t holding_data1311;
	uint16_t holding_data1312;
	uint16_t holding_data1313;
	uint16_t holding_data1314;
	uint16_t holding_data1315;
	uint16_t holding_data1316;
	uint16_t holding_data1317;
	uint16_t holding_data1318;
	uint16_t holding_data1319;
	uint16_t holding_data1320;
	uint16_t holding_data1321;
	uint16_t holding_data1322;
	uint16_t holding_data1323;
	uint16_t holding_data1324;
	uint16_t holding_data1325;
	uint16_t holding_data1326;
	uint16_t holding_data1327;
	uint16_t holding_data1328;
	uint16_t holding_data1329;
	uint16_t holding_data1330;
	uint16_t holding_data1331;
	uint16_t holding_data1332;
	uint16_t holding_data1333;
	uint16_t holding_data1334;
	uint16_t holding_data1335;
	uint16_t holding_data1336;
	uint16_t holding_data1337;
	uint16_t holding_data1338;
	uint16_t holding_data1339;
	uint16_t holding_data1340;
	uint16_t holding_data1341;
	uint16_t holding_data1342;
	uint16_t holding_data1343;
	uint16_t holding_data1344;
	uint16_t holding_data1345;
	uint16_t holding_data1346;
	uint16_t holding_data1347;
	uint16_t holding_data1348;
	uint16_t holding_data1349;
	uint16_t holding_data1350;
	uint16_t holding_data1351;
	uint16_t holding_data1352;
	uint16_t holding_data1353;
	uint16_t holding_data1354;
	uint16_t holding_data1355;
	uint16_t holding_data1356;
	uint16_t holding_data1357;
	uint16_t holding_data1358;
	uint16_t holding_data1359;
	uint16_t holding_data1360;
	uint16_t holding_data1361;
	uint16_t holding_data1362;
	uint16_t holding_data1363;
	uint16_t holding_data1364;
	uint16_t holding_data1365;
	uint16_t holding_data1366;
	uint16_t holding_data1367;
	uint16_t holding_data1368;
	uint16_t holding_data1369;
	uint16_t holding_data1370;
	uint16_t holding_data1371;
	uint16_t holding_data1372;
	uint16_t holding_data1373;
	uint16_t holding_data1374;
	uint16_t holding_data1375;
	uint16_t holding_data1376;
	uint16_t holding_data1377;
	uint16_t holding_data1378;
	uint16_t holding_data1379;
	uint16_t holding_data1380;
	uint16_t holding_data1381;
	uint16_t holding_data1382;
	uint16_t holding_data1383;
	uint16_t holding_data1384;
	uint16_t holding_data1385;
	uint16_t holding_data1386;
	uint16_t holding_data1387;
	uint16_t holding_data1388;
	uint16_t holding_data1389;
	uint16_t holding_data1390;
	uint16_t holding_data1391;
	uint16_t holding_data1392;
	uint16_t holding_data1393;
	uint16_t holding_data1394;
	uint16_t holding_data1395;
	uint16_t holding_data1396;
	uint16_t holding_data1397;
	uint16_t holding_data1398;
	uint16_t holding_data1399;
	uint16_t holding_data1400;
	uint16_t holding_data1401;
	uint16_t holding_data1402;
	uint16_t holding_data1403;
	uint16_t holding_data1404;
	uint16_t holding_data1405;
	uint16_t holding_data1406;
	uint16_t holding_data1407;
	uint16_t holding_data1408;
	uint16_t holding_data1409;
	uint16_t holding_data1410;
	uint16_t holding_data1411;
	uint16_t holding_data1412;
	uint16_t holding_data1413;
	uint16_t holding_data1414;
	uint16_t holding_data1415;
	uint16_t holding_data1416;
	uint16_t holding_data1417;
	uint16_t holding_data1418;
	uint16_t holding_data1419;
	uint16_t holding_data1420;
	uint16_t holding_data1421;
	uint16_t holding_data1422;
	uint16_t holding_data1423;
	uint16_t holding_data1424;
	uint16_t holding_data1425;
	uint16_t holding_data1426;
	uint16_t holding_data1427;
	uint16_t holding_data1428;
	uint16_t holding_data1429;
	uint16_t holding_data1430;
	uint16_t holding_data1431;
	uint16_t holding_data1432;
	uint16_t holding_data1433;
	uint16_t holding_data1434;
	uint16_t holding_data1435;
	uint16_t holding_data1436;
	uint16_t holding_data1437;
	uint16_t holding_data1438;
	uint16_t holding_data1439;
	uint16_t holding_data1440;
	uint16_t holding_data1441;
	uint16_t holding_data1442;
	uint16_t holding_data1443;
	uint16_t holding_data1444;
	uint16_t holding_data1445;
	uint16_t holding_data1446;
	uint16_t holding_data1447;
	uint16_t holding_data1448;
	uint16_t holding_data1449;
	uint16_t holding_data1450;
	uint16_t holding_data1451;
	uint16_t holding_data1452;
	uint16_t holding_data1453;
	uint16_t holding_data1454;
	uint16_t holding_data1455;
	uint16_t holding_data1456;
	uint16_t holding_data1457;
	uint16_t holding_data1458;
	uint16_t holding_data1459;
	uint16_t holding_data1460;
	uint16_t holding_data1461;
	uint16_t holding_data1462;
	uint16_t holding_data1463;
	uint16_t holding_data1464;
	uint16_t holding_data1465;
	uint16_t holding_data1466;
	uint16_t holding_data1467;
	uint16_t holding_data1468;
	uint16_t holding_data1469;
	uint16_t holding_data1470;
	uint16_t holding_data1471;
	uint16_t holding_data1472;
	uint16_t holding_data1473;
	uint16_t holding_data1474;
	uint16_t holding_data1475;
	uint16_t holding_data1476;
	uint16_t holding_data1477;
	uint16_t holding_data1478;
	uint16_t holding_data1479;
	uint16_t holding_data1480;
	uint16_t holding_data1481;
	uint16_t holding_data1482;
	uint16_t holding_data1483;
	uint16_t holding_data1484;
	uint16_t holding_data1485;
	uint16_t holding_data1486;
	uint16_t holding_data1487;
	uint16_t holding_data1488;
	uint16_t holding_data1489;
	uint16_t holding_data1490;
	uint16_t holding_data1491;
	uint16_t holding_data1492;
	uint16_t holding_data1493;
	uint16_t holding_data1494;
	uint16_t holding_data1495;
	uint16_t holding_data1496;
	uint16_t holding_data1497;
	uint16_t holding_data1498;
	uint16_t holding_data1499;
	uint16_t holding_data1500;

} holding_reg_params_slave_t;
#pragma pack(pop)

// Move large Modbus structures to PSRAM for memory optimization
extern holding_reg_params_t holding_reg_params;
extern holding_reg_params_slave_t holding_reg_params_slave __attribute__((section(".ext_ram.bss")));
extern input_reg_params_t input_reg_params __attribute__((section(".ext_ram.bss")));
extern coil_reg_params_t coil_reg_params;
extern discrete_reg_params_t discrete_reg_params;

#endif // !defined(_DEVICE_PARAMS)




