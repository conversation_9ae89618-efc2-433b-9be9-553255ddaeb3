// This file was generated by SquareLine Studio
// SquareLine Studio version: SquareLine Studio 1.4.2
// LVGL version: 8.3.6
// Project name: A_Meter_GUI_20240305

#include "../ui.h"
#include "core/lv_obj.h"

void ui_SC_1_2_screen_init(void)
{
    ui_SC_1_2 = lv_obj_create(NULL);
    lv_obj_clear_flag(ui_SC_1_2, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_img_src(ui_SC_1_2,     ui_img_background, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_Panel15 = lv_obj_create(ui_SC_1_2);
    lv_obj_set_width(ui_Panel15, 115);
    lv_obj_set_height(ui_Panel15, 41);
    lv_obj_set_x(ui_Panel15, 90);
    lv_obj_set_y(ui_Panel15, -96);
    lv_obj_set_align(ui_Panel15, LV_ALIGN_CENTER);
    lv_obj_clear_flag(ui_Panel15, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_radius(ui_Panel15, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui_Panel15, lv_color_hex(0x005BAC), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_Panel15, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_color(ui_Panel15, lv_color_hex(0x7ECEF4), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_main_stop(ui_Panel15, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_stop(ui_Panel15, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_border_color(ui_Panel15, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_border_opa(ui_Panel15, 255, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_Label4 = lv_label_create(ui_Panel15);
    lv_obj_set_width(ui_Label4, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(ui_Label4, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_align(ui_Label4, LV_ALIGN_CENTER);
    lv_label_set_text(ui_Label4, "LV");
    lv_obj_set_style_text_color(ui_Label4, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui_Label4, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui_Label4, &ui_font_WD_28, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_Panel16 = lv_obj_create(ui_SC_1_2);
    lv_obj_set_width(ui_Panel16, 159);
    lv_obj_set_height(ui_Panel16, 41);
    lv_obj_set_x(ui_Panel16, -47);
    lv_obj_set_y(ui_Panel16, -96);
    lv_obj_set_align(ui_Panel16, LV_ALIGN_CENTER);
    lv_obj_clear_flag(ui_Panel16, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_radius(ui_Panel16, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui_Panel16, lv_color_hex(0x005BAC), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_Panel16, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_color(ui_Panel16, lv_color_hex(0x7ECEF4), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_border_color(ui_Panel16, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_border_opa(ui_Panel16, 255, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_Label52 = lv_label_create(ui_Panel16);
    lv_obj_set_width(ui_Label52, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(ui_Label52, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_align(ui_Label52, LV_ALIGN_CENTER);
    lv_label_set_text(ui_Label52, "ID");
    lv_obj_set_style_text_color(ui_Label52, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui_Label52, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui_Label52, &ui_font_WD_28, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_PL_del_ID1 = ui_Face_ID_PL_create(ui_SC_1_2);
    lv_obj_set_x(ui_PL_del_ID1, 14);
    lv_obj_set_y(ui_PL_del_ID1, 7);
    lv_obj_set_flex_flow(ui_PL_del_ID1, LV_FLEX_FLOW_ROW_WRAP);
    lv_obj_set_flex_align(ui_PL_del_ID1, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_START, LV_FLEX_ALIGN_START);


    lv_label_set_text(ui_comp_get_child(ui_PL_del_ID1, UI_COMP_FACE_ID_PL_PL_FACE_ID_0_FACE_ID_0_ID_0_NAME0), "N/A");
    lv_label_set_text(ui_comp_get_child(ui_PL_del_ID1, UI_COMP_FACE_ID_PL_PL_FACE_ID_0_FACE_ID__LV_0_LAB_LV0), "N/A");
    lv_label_set_text(ui_comp_get_child(ui_PL_del_ID1, UI_COMP_FACE_ID_PL_PL_FACE_ID_1_FACE_ID_1_ID_0_NAME1), "N/A");
    lv_label_set_text(ui_comp_get_child(ui_PL_del_ID1, UI_COMP_FACE_ID_PL_PL_FACE_ID_1_FACE_ID__LV_1_LAB_LV1), "N/A");
    lv_label_set_text(ui_comp_get_child(ui_PL_del_ID1, UI_COMP_FACE_ID_PL_PL_FACE_ID_2_FACE_ID_2_ID_0_NAME2), "N/A");
    lv_label_set_text(ui_comp_get_child(ui_PL_del_ID1, UI_COMP_FACE_ID_PL_PL_FACE_ID_2_FACE_ID__LV_2_LAB_LV2), "N/A");
    lv_label_set_text(ui_comp_get_child(ui_PL_del_ID1, UI_COMP_FACE_ID_PL_PL_FACE_ID_3_FACE_ID_3_ID_0_NAME3), "N/A");
    lv_label_set_text(ui_comp_get_child(ui_PL_del_ID1, UI_COMP_FACE_ID_PL_PL_FACE_ID_3_FACE_ID__LV_3_LAB_LV3), "N/A");
    lv_label_set_text(ui_comp_get_child(ui_PL_del_ID1, UI_COMP_FACE_ID_PL_PL_FACE_ID_4_FACE_ID_4_ID_0_NAME4), "N/A");
    lv_label_set_text(ui_comp_get_child(ui_PL_del_ID1, UI_COMP_FACE_ID_PL_PL_FACE_ID_4_FACE_ID__LV_4_LAB_LV4), "N/A");
    lv_label_set_text(ui_comp_get_child(ui_PL_del_ID1, UI_COMP_FACE_ID_PL_PL_FACE_ID_5_FACE_ID_5_ID_0_NAME5), "N/A");
    lv_label_set_text(ui_comp_get_child(ui_PL_del_ID1, UI_COMP_FACE_ID_PL_PL_FACE_ID_5_FACE_ID__LV_5_LAB_LV5), "N/A");
    lv_label_set_text(ui_comp_get_child(ui_PL_del_ID1, UI_COMP_FACE_ID_PL_PL_FACE_ID_6_FACE_ID_6_ID_0_NAME6), "N/A");
    lv_label_set_text(ui_comp_get_child(ui_PL_del_ID1, UI_COMP_FACE_ID_PL_PL_FACE_ID_6_FACE_ID__LV_6_LAB_LV6), "N/A");

    ui_LV_confirm2 = lv_obj_create(ui_SC_1_2);
    lv_obj_set_width(ui_LV_confirm2, 229);
    lv_obj_set_height(ui_LV_confirm2, 168);
    lv_obj_set_x(ui_LV_confirm2, 18);
    lv_obj_set_y(ui_LV_confirm2, 10);
    lv_obj_set_align(ui_LV_confirm2, LV_ALIGN_CENTER);
    lv_obj_add_flag(ui_LV_confirm2, LV_OBJ_FLAG_HIDDEN);     /// Flags
    lv_obj_clear_flag(ui_LV_confirm2, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(ui_LV_confirm2, lv_color_hex(0xF9C968), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_LV_confirm2, 255, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_Roller_LV2 = lv_roller_create(ui_LV_confirm2);
    lv_roller_set_options(ui_Roller_LV2, "     1     \n     2     \n     3     \n     4     ", LV_ROLLER_MODE_NORMAL);
    lv_obj_set_height(ui_Roller_LV2, 155);
    lv_obj_set_width(ui_Roller_LV2, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_x(ui_Roller_LV2, -37);
    lv_obj_set_y(ui_Roller_LV2, 0);
    lv_obj_set_align(ui_Roller_LV2, LV_ALIGN_CENTER);
    lv_obj_set_style_text_font(ui_Roller_LV2, &ui_font_WD_48, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui_Roller_LV2, lv_color_hex(0x81F2DA), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_Roller_LV2, 255, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_LV_Cofirm_OK2 = lv_btn_create(ui_LV_confirm2);
    lv_obj_set_width(ui_LV_Cofirm_OK2, 72);
    lv_obj_set_height(ui_LV_Cofirm_OK2, 155);
    lv_obj_set_x(ui_LV_Cofirm_OK2, 74);
    lv_obj_set_y(ui_LV_Cofirm_OK2, 0);
    lv_obj_set_align(ui_LV_Cofirm_OK2, LV_ALIGN_CENTER);
    lv_obj_add_flag(ui_LV_Cofirm_OK2, LV_OBJ_FLAG_SCROLL_ON_FOCUS);     /// Flags
    lv_obj_clear_flag(ui_LV_Cofirm_OK2, LV_OBJ_FLAG_SCROLLABLE);      /// Flags

    ui_Lab_LV_Con2 = lv_label_create(ui_LV_Cofirm_OK2);
    lv_obj_set_width(ui_Lab_LV_Con2, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(ui_Lab_LV_Con2, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_align(ui_Lab_LV_Con2, LV_ALIGN_CENTER);
    lv_label_set_text(ui_Lab_LV_Con2, "OK");
    lv_obj_set_style_text_font(ui_Lab_LV_Con2, &ui_font_WD_48, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_Image168 = lv_img_create(ui_SC_1_2);
    lv_img_set_src(ui_Image168, &ui_img_b01_20_png);
    lv_obj_set_width(ui_Image168, LV_SIZE_CONTENT);   /// 10
    lv_obj_set_height(ui_Image168, LV_SIZE_CONTENT);    /// 10
    lv_obj_set_x(ui_Image168, -145);
    lv_obj_set_y(ui_Image168, 15);
    lv_obj_set_align(ui_Image168, LV_ALIGN_CENTER);
    lv_obj_add_flag(ui_Image168, LV_OBJ_FLAG_ADV_HITTEST);     /// Flags
    lv_obj_clear_flag(ui_Image168, LV_OBJ_FLAG_SCROLLABLE);      /// Flags

    ui_Image169 = lv_img_create(ui_SC_1_2);
    lv_img_set_src(ui_Image169, &ui_img_b02_10_png);
    lv_obj_set_width(ui_Image169, LV_SIZE_CONTENT);   /// 20
    lv_obj_set_height(ui_Image169, LV_SIZE_CONTENT);    /// 20
    lv_obj_set_x(ui_Image169, -145);
    lv_obj_set_y(ui_Image169, -15);
    lv_obj_set_align(ui_Image169, LV_ALIGN_CENTER);
    lv_obj_add_flag(ui_Image169, LV_OBJ_FLAG_ADV_HITTEST);     /// Flags
    lv_obj_clear_flag(ui_Image169, LV_OBJ_FLAG_SCROLLABLE);      /// Flags

    ui_Image170 = lv_img_create(ui_SC_1_2);
    lv_img_set_src(ui_Image170, &ui_img_b01_20_png);
    lv_obj_set_width(ui_Image170, LV_SIZE_CONTENT);   /// 20
    lv_obj_set_height(ui_Image170, LV_SIZE_CONTENT);    /// 20
    lv_obj_set_x(ui_Image170, -60);
    lv_obj_set_y(ui_Image170, 105);
    lv_obj_set_align(ui_Image170, LV_ALIGN_CENTER);
    lv_obj_add_flag(ui_Image170, LV_OBJ_FLAG_ADV_HITTEST);     /// Flags
    lv_obj_clear_flag(ui_Image170, LV_OBJ_FLAG_SCROLLABLE);      /// Flags

    ui_Image171 = lv_img_create(ui_SC_1_2);
    lv_img_set_src(ui_Image171, &ui_img_b02_10_png);
    lv_obj_set_width(ui_Image171, LV_SIZE_CONTENT);   /// 10
    lv_obj_set_height(ui_Image171, LV_SIZE_CONTENT);    /// 10
    lv_obj_set_x(ui_Image171, -30);
    lv_obj_set_y(ui_Image171, 105);
    lv_obj_set_align(ui_Image171, LV_ALIGN_CENTER);
    lv_obj_add_flag(ui_Image171, LV_OBJ_FLAG_ADV_HITTEST);     /// Flags
    lv_obj_clear_flag(ui_Image171, LV_OBJ_FLAG_SCROLLABLE);      /// Flags

    ui_Image167 = lv_img_create(ui_SC_1_2);
    lv_img_set_src(ui_Image167, &ui_img_b02_10_png);
    lv_obj_set_width(ui_Image167, LV_SIZE_CONTENT);   /// 10
    lv_obj_set_height(ui_Image167, LV_SIZE_CONTENT);    /// 10
    lv_obj_set_x(ui_Image167, 0);
    lv_obj_set_y(ui_Image167, 105);
    lv_obj_set_align(ui_Image167, LV_ALIGN_CENTER);
    lv_obj_add_flag(ui_Image167, LV_OBJ_FLAG_ADV_HITTEST);     /// Flags
    lv_obj_clear_flag(ui_Image167, LV_OBJ_FLAG_SCROLLABLE);      /// Flags

    ui_Image172 = lv_img_create(ui_SC_1_2);
    lv_img_set_src(ui_Image172, &ui_img_b02_10_png);
    lv_obj_set_width(ui_Image172, LV_SIZE_CONTENT);   /// 10
    lv_obj_set_height(ui_Image172, LV_SIZE_CONTENT);    /// 10
    lv_obj_set_x(ui_Image172, 30);
    lv_obj_set_y(ui_Image172, 105);
    lv_obj_set_align(ui_Image172, LV_ALIGN_CENTER);
    lv_obj_add_flag(ui_Image172, LV_OBJ_FLAG_ADV_HITTEST);     /// Flags
    lv_obj_clear_flag(ui_Image172, LV_OBJ_FLAG_SCROLLABLE);      /// Flags

    ui_Image173 = lv_img_create(ui_SC_1_2);
    lv_img_set_src(ui_Image173, &ui_img_b02_10_png);
    lv_obj_set_width(ui_Image173, LV_SIZE_CONTENT);   /// 10
    lv_obj_set_height(ui_Image173, LV_SIZE_CONTENT);    /// 10
    lv_obj_set_x(ui_Image173, 60);
    lv_obj_set_y(ui_Image173, 105);
    lv_obj_set_align(ui_Image173, LV_ALIGN_CENTER);
    lv_obj_add_flag(ui_Image173, LV_OBJ_FLAG_ADV_HITTEST);     /// Flags
    lv_obj_clear_flag(ui_Image173, LV_OBJ_FLAG_SCROLLABLE);      /// Flags

    ui_Image174 = lv_img_create(ui_SC_1_2);
    lv_img_set_src(ui_Image174, &ui_img_b02_10_png);
    lv_obj_set_width(ui_Image174, LV_SIZE_CONTENT);   /// 10
    lv_obj_set_height(ui_Image174, LV_SIZE_CONTENT);    /// 10
    lv_obj_set_x(ui_Image174, 90);
    lv_obj_set_y(ui_Image174, 105);
    lv_obj_set_align(ui_Image174, LV_ALIGN_CENTER);
    lv_obj_add_flag(ui_Image174, LV_OBJ_FLAG_ADV_HITTEST);     /// Flags
    lv_obj_clear_flag(ui_Image174, LV_OBJ_FLAG_SCROLLABLE);      /// Flags

    ui_PL_Admin_PWD = lv_obj_create(ui_SC_1_2);
    lv_obj_set_width(ui_PL_Admin_PWD, 320);
    lv_obj_set_height(ui_PL_Admin_PWD, 240);
    lv_obj_set_align(ui_PL_Admin_PWD, LV_ALIGN_CENTER);
    lv_obj_clear_flag(ui_PL_Admin_PWD, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_add_flag(ui_PL_Admin_PWD, LV_OBJ_FLAG_HIDDEN);
    lv_obj_set_style_radius(ui_PL_Admin_PWD, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui_PL_Admin_PWD, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_PL_Admin_PWD, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_img_src(ui_PL_Admin_PWD, ui_img_background, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_Label66 = lv_label_create(ui_PL_Admin_PWD);
    lv_obj_set_width(ui_Label66, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(ui_Label66, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_x(ui_Label66, 0);
    lv_obj_set_y(ui_Label66, -103);
    lv_obj_set_align(ui_Label66, LV_ALIGN_CENTER);
    lv_label_set_text(ui_Label66, "Login Password");
    lv_obj_set_style_text_color(ui_Label66, lv_color_hex(0x000000), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui_Label66, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui_Label66, &ui_font_WD_28, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_TextArea_Login_PWD = lv_textarea_create(ui_PL_Admin_PWD);
    lv_obj_set_width(ui_TextArea_Login_PWD, 240);
    lv_obj_set_height(ui_TextArea_Login_PWD, 30);
    lv_obj_set_x(ui_TextArea_Login_PWD, 0);
    lv_obj_set_y(ui_TextArea_Login_PWD, -73);
    lv_obj_set_align(ui_TextArea_Login_PWD, LV_ALIGN_CENTER);
    lv_textarea_set_max_length(ui_TextArea_Login_PWD, 6);
    lv_textarea_set_placeholder_text(ui_TextArea_Login_PWD, "PASSWORD....");
    lv_textarea_set_password_mode(ui_TextArea_Login_PWD, true);
    lv_obj_clear_flag(ui_TextArea_Login_PWD,
                      LV_OBJ_FLAG_SCROLLABLE | LV_OBJ_FLAG_SCROLL_ELASTIC | LV_OBJ_FLAG_SCROLL_MOMENTUM |
                      LV_OBJ_FLAG_SCROLL_CHAIN);     /// Flags
    lv_obj_set_style_text_color(ui_TextArea_Login_PWD, lv_color_hex(0x000000), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui_TextArea_Login_PWD, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui_TextArea_Login_PWD, &lv_font_montserrat_14, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui_TextArea_Login_PWD, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_TextArea_Login_PWD, 255, LV_PART_MAIN | LV_STATE_DEFAULT);



    ui_BTN_Lif6 = lv_imgbtn_create(ui_PL_Admin_PWD);
    lv_imgbtn_set_src(ui_BTN_Lif6, LV_IMGBTN_STATE_RELEASED, NULL, &ui_img_lift_arrow_png, NULL);
    lv_imgbtn_set_src(ui_BTN_Lif6, LV_IMGBTN_STATE_PRESSED, NULL, &ui_img_lift_arrow_1_png, NULL);
    lv_imgbtn_set_src(ui_BTN_Lif6, LV_IMGBTN_STATE_DISABLED, NULL, &ui__temporary_image, NULL);
    lv_imgbtn_set_src(ui_BTN_Lif6, LV_IMGBTN_STATE_CHECKED_PRESSED, NULL, &ui__temporary_image, NULL);
    lv_imgbtn_set_src(ui_BTN_Lif6, LV_IMGBTN_STATE_CHECKED_RELEASED, NULL, &ui__temporary_image, NULL);
    lv_imgbtn_set_src(ui_BTN_Lif6, LV_IMGBTN_STATE_CHECKED_DISABLED, NULL, &ui__temporary_image, NULL);
    lv_obj_set_height(ui_BTN_Lif6, 133);
    lv_obj_set_width(ui_BTN_Lif6, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_x(ui_BTN_Lif6, -98);
    lv_obj_set_y(ui_BTN_Lif6, 7);
    lv_obj_set_align(ui_BTN_Lif6, LV_ALIGN_CENTER);
    // Hide original roller controls
    lv_obj_add_flag(ui_BTN_Lif6, LV_OBJ_FLAG_HIDDEN);

    ui_Roller6 = lv_roller_create(ui_PL_Admin_PWD);
    lv_roller_set_options(ui_Roller6, "0\n1\n2\n3\n4\n5\n6\n7\n8\n9", LV_ROLLER_MODE_NORMAL);
    lv_obj_set_width(ui_Roller6, 84);
    lv_obj_set_height(ui_Roller6, 133);
    lv_obj_set_x(ui_Roller6, 0);
    lv_obj_set_y(ui_Roller6, 7);
    lv_obj_set_align(ui_Roller6, LV_ALIGN_CENTER);
    lv_obj_set_style_text_color(ui_Roller6, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui_Roller6, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    // Hide original roller
    lv_obj_add_flag(ui_Roller6, LV_OBJ_FLAG_HIDDEN);

    ui_BTN_Rig6 = lv_imgbtn_create(ui_PL_Admin_PWD);
    lv_imgbtn_set_src(ui_BTN_Rig6, LV_IMGBTN_STATE_RELEASED, NULL, &ui_img_right_arrow_png, NULL);
    lv_imgbtn_set_src(ui_BTN_Rig6, LV_IMGBTN_STATE_PRESSED, NULL, &ui_img_right_arrow_1_png, NULL);
    lv_imgbtn_set_src(ui_BTN_Rig6, LV_IMGBTN_STATE_DISABLED, NULL, &ui__temporary_image, NULL);
    lv_imgbtn_set_src(ui_BTN_Rig6, LV_IMGBTN_STATE_CHECKED_PRESSED, NULL, &ui__temporary_image, NULL);
    lv_imgbtn_set_src(ui_BTN_Rig6, LV_IMGBTN_STATE_CHECKED_RELEASED, NULL, &ui__temporary_image, NULL);
    lv_imgbtn_set_src(ui_BTN_Rig6, LV_IMGBTN_STATE_CHECKED_DISABLED, NULL, &ui__temporary_image, NULL);
    lv_obj_set_height(ui_BTN_Rig6, 133);
    lv_obj_set_width(ui_BTN_Rig6, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_x(ui_BTN_Rig6, 98);
    lv_obj_set_y(ui_BTN_Rig6, 7);
    lv_obj_set_align(ui_BTN_Rig6, LV_ALIGN_CENTER);
    // Hide original roller controls
    lv_obj_add_flag(ui_BTN_Rig6, LV_OBJ_FLAG_HIDDEN);

    // Create custom numeric keyboard for Login Password
    ui_CustomKeyboard_Login_PWD = lv_obj_create(ui_PL_Admin_PWD);
    lv_obj_set_width(ui_CustomKeyboard_Login_PWD, 320);
    lv_obj_set_height(ui_CustomKeyboard_Login_PWD, 175);
    lv_obj_set_x(ui_CustomKeyboard_Login_PWD, 0);
    lv_obj_set_y(ui_CustomKeyboard_Login_PWD, 30);
    lv_obj_set_align(ui_CustomKeyboard_Login_PWD, LV_ALIGN_CENTER);
    lv_obj_clear_flag(ui_CustomKeyboard_Login_PWD, LV_OBJ_FLAG_SCROLLABLE);
    lv_obj_set_style_bg_color(ui_CustomKeyboard_Login_PWD, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_CustomKeyboard_Login_PWD, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_border_width(ui_CustomKeyboard_Login_PWD, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_all(ui_CustomKeyboard_Login_PWD, 5, LV_PART_MAIN | LV_STATE_DEFAULT);

    // Row 1: 1 2 3 4 5
    for(int i = 0; i < 5; i++) {
        lv_obj_t * btn = lv_btn_create(ui_CustomKeyboard_Login_PWD);
        lv_obj_set_width(btn, 55);
        lv_obj_set_height(btn, 45);
        lv_obj_set_x(btn, -120 + i * 60);
        lv_obj_set_y(btn, -65);
        lv_obj_set_align(btn, LV_ALIGN_CENTER);
        lv_obj_set_style_bg_color(btn, lv_color_hex(0x4A90E2), LV_PART_MAIN | LV_STATE_DEFAULT);
        lv_obj_set_style_bg_opa(btn, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
        lv_obj_set_style_shadow_color(btn, lv_color_hex(0x000000), LV_PART_MAIN | LV_STATE_DEFAULT);
        lv_obj_set_style_shadow_opa(btn, 64, LV_PART_MAIN | LV_STATE_DEFAULT);
        lv_obj_set_style_shadow_width(btn, 2, LV_PART_MAIN | LV_STATE_DEFAULT);
        lv_obj_set_style_shadow_spread(btn, 1, LV_PART_MAIN | LV_STATE_DEFAULT);

        lv_obj_t * label = lv_label_create(btn);
        lv_obj_set_align(label, LV_ALIGN_CENTER);
        char btn_text[2];
        sprintf(btn_text, "%d", i + 1);
        lv_label_set_text(label, btn_text);
        lv_obj_set_style_text_color(label, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
        lv_obj_set_style_text_font(label, &lv_font_montserrat_16, LV_PART_MAIN | LV_STATE_DEFAULT);

        lv_obj_add_event_cb(btn, ui_event_CustomKeyboard_Login_PWD, LV_EVENT_CLICKED, NULL);
    }

    // Row 2: 6 7 8 9 0
    for(int i = 0; i < 5; i++) {
        lv_obj_t * btn = lv_btn_create(ui_CustomKeyboard_Login_PWD);
        lv_obj_set_width(btn, 55);
        lv_obj_set_height(btn, 45);
        lv_obj_set_x(btn, -120 + i * 60);
        lv_obj_set_y(btn, -10);
        lv_obj_set_align(btn, LV_ALIGN_CENTER);
        lv_obj_set_style_bg_color(btn, lv_color_hex(0x4A90E2), LV_PART_MAIN | LV_STATE_DEFAULT);
        lv_obj_set_style_bg_opa(btn, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
        lv_obj_set_style_shadow_color(btn, lv_color_hex(0x000000), LV_PART_MAIN | LV_STATE_DEFAULT);
        lv_obj_set_style_shadow_opa(btn, 64, LV_PART_MAIN | LV_STATE_DEFAULT);
        lv_obj_set_style_shadow_width(btn, 2, LV_PART_MAIN | LV_STATE_DEFAULT);
        lv_obj_set_style_shadow_spread(btn, 1, LV_PART_MAIN | LV_STATE_DEFAULT);

        lv_obj_t * label = lv_label_create(btn);
        lv_obj_set_align(label, LV_ALIGN_CENTER);
        char btn_text[2];
        if(i == 4) {
            sprintf(btn_text, "0");
        } else {
            sprintf(btn_text, "%d", i + 6);
        }
        lv_label_set_text(label, btn_text);
        lv_obj_set_style_text_color(label, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
        lv_obj_set_style_text_font(label, &lv_font_montserrat_16, LV_PART_MAIN | LV_STATE_DEFAULT);

        lv_obj_add_event_cb(btn, ui_event_CustomKeyboard_Login_PWD, LV_EVENT_CLICKED, NULL);
    }

    // Row 3: OK Cancel
    // OK Button
    lv_obj_t * btn_ok = lv_btn_create(ui_CustomKeyboard_Login_PWD);
    lv_obj_set_width(btn_ok, 120);
    lv_obj_set_height(btn_ok, 45);
    lv_obj_set_x(btn_ok, -60);
    lv_obj_set_y(btn_ok, 45);
    lv_obj_set_align(btn_ok, LV_ALIGN_CENTER);
    lv_obj_set_style_bg_color(btn_ok, lv_color_hex(0x6EB763), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(btn_ok, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_color(btn_ok, lv_color_hex(0x000000), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_opa(btn_ok, 64, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(btn_ok, 2, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_spread(btn_ok, 1, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * label_ok = lv_label_create(btn_ok);
    lv_obj_set_align(label_ok, LV_ALIGN_CENTER);
    lv_label_set_text(label_ok, "OK");
    lv_obj_set_style_text_color(label_ok, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(label_ok, &lv_font_montserrat_16, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_add_event_cb(btn_ok, ui_event_CustomKeyboard_Login_PWD, LV_EVENT_CLICKED, NULL);

    // Cancel Button
    lv_obj_t * btn_cancel = lv_btn_create(ui_CustomKeyboard_Login_PWD);
    lv_obj_set_width(btn_cancel, 120);
    lv_obj_set_height(btn_cancel, 45);
    lv_obj_set_x(btn_cancel, 60);
    lv_obj_set_y(btn_cancel, 45);
    lv_obj_set_align(btn_cancel, LV_ALIGN_CENTER);
    lv_obj_set_style_bg_color(btn_cancel, lv_color_hex(0xAC0000), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(btn_cancel, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_color(btn_cancel, lv_color_hex(0x000000), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_opa(btn_cancel, 64, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(btn_cancel, 2, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_spread(btn_cancel, 1, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * label_cancel = lv_label_create(btn_cancel);
    lv_obj_set_align(label_cancel, LV_ALIGN_CENTER);
    lv_label_set_text(label_cancel, "Cancel");
    lv_obj_set_style_text_color(label_cancel, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(label_cancel, &lv_font_montserrat_16, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_add_event_cb(btn_cancel, ui_event_CustomKeyboard_Login_PWD, LV_EVENT_CLICKED, NULL);

    // Hide original OK and Cancel buttons
    ui_BTN_login_PWD_OK2 = lv_btn_create(ui_PL_Admin_PWD);
    lv_obj_set_width(ui_BTN_login_PWD_OK2, 130);
    lv_obj_set_height(ui_BTN_login_PWD_OK2, 33);
    lv_obj_set_x(ui_BTN_login_PWD_OK2, -80);
    lv_obj_set_y(ui_BTN_login_PWD_OK2, 98);
    lv_obj_set_align(ui_BTN_login_PWD_OK2, LV_ALIGN_CENTER);
    lv_obj_add_flag(ui_BTN_login_PWD_OK2, LV_OBJ_FLAG_SCROLL_ON_FOCUS);     /// Flags
    lv_obj_clear_flag(ui_BTN_login_PWD_OK2, LV_OBJ_FLAG_SCROLLABLE);      /// Flags

    lv_obj_set_style_shadow_color(ui_BTN_login_PWD_OK2, lv_color_hex(0x000000), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_opa(ui_BTN_login_PWD_OK2, 64, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui_BTN_login_PWD_OK2, 1, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_spread(ui_BTN_login_PWD_OK2, 1, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_ofs_x(ui_BTN_login_PWD_OK2, 1, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_ofs_y(ui_BTN_login_PWD_OK2, 2, LV_PART_MAIN | LV_STATE_DEFAULT);
    // Hide original OK button
    lv_obj_add_flag(ui_BTN_login_PWD_OK2, LV_OBJ_FLAG_HIDDEN);

    ui_Label67 = lv_label_create(ui_BTN_login_PWD_OK2);
    lv_obj_set_width(ui_Label67, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(ui_Label67, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_align(ui_Label67, LV_ALIGN_CENTER);
    lv_label_set_text(ui_Label67, "OK");
    lv_obj_set_style_text_font(ui_Label67, &ui_font_WD_28, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_BTN_set_PWD_cancel2 = lv_btn_create(ui_PL_Admin_PWD);
    lv_obj_set_width(ui_BTN_set_PWD_cancel2, 130);
    lv_obj_set_height(ui_BTN_set_PWD_cancel2, 33);
    lv_obj_set_x(ui_BTN_set_PWD_cancel2, 80);
    lv_obj_set_y(ui_BTN_set_PWD_cancel2, 98);
    lv_obj_set_align(ui_BTN_set_PWD_cancel2, LV_ALIGN_CENTER);
    lv_obj_add_flag(ui_BTN_set_PWD_cancel2, LV_OBJ_FLAG_SCROLL_ON_FOCUS);     /// Flags
    lv_obj_clear_flag(ui_BTN_set_PWD_cancel2, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    // Hide original Cancel button
    lv_obj_add_flag(ui_BTN_set_PWD_cancel2, LV_OBJ_FLAG_HIDDEN);

    ui_Label68 = lv_label_create(ui_BTN_set_PWD_cancel2);
    lv_obj_set_width(ui_Label68, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(ui_Label68, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_align(ui_Label68, LV_ALIGN_CENTER);
    lv_label_set_text(ui_Label68, "Cancel");
    lv_obj_set_style_text_font(ui_Label68, &ui_font_WD_28, LV_PART_MAIN | LV_STATE_DEFAULT);
    
    ///////////////////////////////////////////////////////////////////////////////////////////////////////////////
    lv_obj_set_style_bg_color(ui_BTN_set_PWD_cancel2, lv_color_hex(0xAC0000), LV_PART_MAIN | LV_STATE_DEFAULT); //0xAC0000
    lv_obj_set_style_bg_opa(ui_BTN_set_PWD_cancel2, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_color(ui_BTN_set_PWD_cancel2, lv_color_hex(0xAD6A6A), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_dir(ui_BTN_set_PWD_cancel2, 2, LV_PART_MAIN | LV_STATE_DEFAULT);
    
    lv_obj_set_style_bg_color(ui_BTN_login_PWD_OK2, lv_color_hex(0x6EB763), LV_PART_MAIN | LV_STATE_DEFAULT); //0x6EB763
    lv_obj_set_style_bg_opa(ui_BTN_login_PWD_OK2, 255, 0);
    lv_obj_set_style_bg_grad_color(ui_BTN_login_PWD_OK2, lv_color_hex(0xD6E9C4), 0);
    lv_obj_set_style_bg_grad_dir(ui_PL_Admin_PWD, 2, LV_PART_MAIN | LV_STATE_DEFAULT);
    ///////////////////////////////////////////////////////////////////////////////////////////////////////////////

    lv_obj_add_event_cb(ui_comp_get_child(ui_PL_del_ID1, UI_COMP_FACE_ID_PL_PL_FACE_ID_0_FACE_ID_0),
                        ui_event_PL_del_ID1_PL_Face_ID_0_Face_ID_0, LV_EVENT_ALL, NULL);
    lv_obj_add_event_cb(ui_comp_get_child(ui_PL_del_ID1, UI_COMP_FACE_ID_PL_PL_FACE_ID_0_FACE_ID__LV_0),
                        ui_event_PL_del_ID1_PL_Face_ID_0_Face_ID__LV_0, LV_EVENT_ALL, NULL);
    lv_obj_add_event_cb(ui_comp_get_child(ui_PL_del_ID1, UI_COMP_FACE_ID_PL_PL_FACE_ID_1_FACE_ID_1),
                        ui_event_PL_del_ID1_PL_Face_ID_1_Face_ID_1, LV_EVENT_ALL, NULL);
    lv_obj_add_event_cb(ui_comp_get_child(ui_PL_del_ID1, UI_COMP_FACE_ID_PL_PL_FACE_ID_1_FACE_ID__LV_1),
                        ui_event_PL_del_ID1_PL_Face_ID_1_Face_ID__LV_1, LV_EVENT_ALL, NULL);
    lv_obj_add_event_cb(ui_comp_get_child(ui_PL_del_ID1, UI_COMP_FACE_ID_PL_PL_FACE_ID_2_FACE_ID_2),
                        ui_event_PL_del_ID1_PL_Face_ID_2_Face_ID_2, LV_EVENT_ALL, NULL);
    lv_obj_add_event_cb(ui_comp_get_child(ui_PL_del_ID1, UI_COMP_FACE_ID_PL_PL_FACE_ID_2_FACE_ID__LV_2),
                        ui_event_PL_del_ID1_PL_Face_ID_2_Face_ID__LV_2, LV_EVENT_ALL, NULL);
    lv_obj_add_event_cb(ui_comp_get_child(ui_PL_del_ID1, UI_COMP_FACE_ID_PL_PL_FACE_ID_3_FACE_ID_3),
                        ui_event_PL_del_ID1_PL_Face_ID_3_Face_ID_3, LV_EVENT_ALL, NULL);
    lv_obj_add_event_cb(ui_comp_get_child(ui_PL_del_ID1, UI_COMP_FACE_ID_PL_PL_FACE_ID_3_FACE_ID__LV_3),
                        ui_event_PL_del_ID1_PL_Face_ID_3_Face_ID__LV_3, LV_EVENT_ALL, NULL);
    lv_obj_add_event_cb(ui_comp_get_child(ui_PL_del_ID1, UI_COMP_FACE_ID_PL_PL_FACE_ID_4_FACE_ID_4),
                        ui_event_PL_del_ID1_PL_Face_ID_4_Face_ID_4, LV_EVENT_ALL, NULL);
    lv_obj_add_event_cb(ui_comp_get_child(ui_PL_del_ID1, UI_COMP_FACE_ID_PL_PL_FACE_ID_4_FACE_ID__LV_4),
                        ui_event_PL_del_ID1_PL_Face_ID_4_Face_ID__LV_4, LV_EVENT_ALL, NULL);
    lv_obj_add_event_cb(ui_comp_get_child(ui_PL_del_ID1, UI_COMP_FACE_ID_PL_PL_FACE_ID_5_FACE_ID_5),
                        ui_event_PL_del_ID1_PL_Face_ID_5_Face_ID_5, LV_EVENT_ALL, NULL);
    lv_obj_add_event_cb(ui_comp_get_child(ui_PL_del_ID1, UI_COMP_FACE_ID_PL_PL_FACE_ID_5_FACE_ID__LV_5),
                        ui_event_PL_del_ID1_PL_Face_ID_5_Face_ID__LV_5, LV_EVENT_ALL, NULL);
    lv_obj_add_event_cb(ui_comp_get_child(ui_PL_del_ID1, UI_COMP_FACE_ID_PL_PL_FACE_ID_6_FACE_ID_6),
                        ui_event_PL_del_ID1_PL_Face_ID_6_Face_ID_6, LV_EVENT_ALL, NULL);
    lv_obj_add_event_cb(ui_comp_get_child(ui_PL_del_ID1, UI_COMP_FACE_ID_PL_PL_FACE_ID_6_FACE_ID__LV_6),
                        ui_event_PL_del_ID1_PL_Face_ID_6_Face_ID__LV_6, LV_EVENT_ALL, NULL);


    lv_obj_add_event_cb(ui_LV_Cofirm_OK2, ui_event_LV_Cofirm_OK2, LV_EVENT_ALL, NULL);
    lv_obj_add_event_cb(ui_BTN_Lif6, ui_event_BTN_Lif6, LV_EVENT_ALL, NULL);
    lv_obj_add_event_cb(ui_BTN_Rig6, ui_event_BTN_Rig6, LV_EVENT_ALL, NULL);
    lv_obj_add_event_cb(ui_BTN_login_PWD_OK2, ui_event_BTN_login_PWD_OK2, LV_EVENT_ALL, NULL);
    lv_obj_add_event_cb(ui_BTN_set_PWD_cancel2, ui_event_BTN_set_PWD_cancel2, LV_EVENT_ALL, NULL);
    lv_obj_add_event_cb(ui_SC_1_2, ui_event_SC_1_2, LV_EVENT_ALL, NULL);

}
