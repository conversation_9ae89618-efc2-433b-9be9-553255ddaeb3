{"COMPONENT_KCONFIGS": "D:/ESPRESSIF/v5.4.1/esp-idf/components/efuse/Kconfig;D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_common/Kconfig;D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_hw_support/Kconfig;D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_security/Kconfig;D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_system/Kconfig;D:/ESPRESSIF/v5.4.1/esp-idf/components/freertos/Kconfig;D:/ESPRESSIF/v5.4.1/esp-idf/components/hal/Kconfig;D:/ESPRESSIF/v5.4.1/esp-idf/components/log/Kconfig;D:/ESPRESSIF/v5.4.1/esp-idf/components/newlib/Kconfig;D:/ESPRESSIF/v5.4.1/esp-idf/components/soc/Kconfig;D:/ESPRESSIF/v5.4.1/esp-idf/components/spi_flash/Kconfig", "COMPONENT_KCONFIGS_PROJBUILD": "D:/ESPRESSIF/v5.4.1/esp-idf/components/bootloader/Kconfig.projbuild;D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_app_format/Kconfig.projbuild;D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_rom/Kconfig.projbuild;D:/ESPRESSIF/v5.4.1/esp-idf/components/esptool_py/Kconfig.projbuild;D:/ESPRESSIF/v5.4.1/esp-idf/components/partition_table/Kconfig.projbuild", "COMPONENT_SDKCONFIG_RENAMES": "D:/ESPRESSIF/v5.4.1/esp-idf/components/bootloader/sdkconfig.rename;D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_hw_support/sdkconfig.rename;D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_hw_support/sdkconfig.rename.esp32s3;D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_system/sdkconfig.rename;D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_system/sdkconfig.rename.esp32s3;D:/ESPRESSIF/v5.4.1/esp-idf/components/esptool_py/sdkconfig.rename;D:/ESPRESSIF/v5.4.1/esp-idf/components/freertos/sdkconfig.rename;D:/ESPRESSIF/v5.4.1/esp-idf/components/hal/sdkconfig.rename;D:/ESPRESSIF/v5.4.1/esp-idf/components/newlib/sdkconfig.rename.esp32s3;D:/ESPRESSIF/v5.4.1/esp-idf/components/spi_flash/sdkconfig.rename", "IDF_TARGET": "esp32s3", "IDF_TOOLCHAIN": "gcc", "IDF_VERSION": "5.4.1", "IDF_ENV_FPGA": "", "IDF_PATH": "D:/ESPRESSIF/v5.4.1/esp-idf", "COMPONENT_KCONFIGS_SOURCE_FILE": "D:/ESP-Project/ameter00/build/bootloader/kconfigs.in", "COMPONENT_KCONFIGS_PROJBUILD_SOURCE_FILE": "D:/ESP-Project/ameter00/build/bootloader/kconfigs_projbuild.in"}