/*
 * SPDX-FileCopyrightText: 2022-2023 Espressif Systems (Shanghai) CO LTD
 *
 * SPDX-License-Identifier: Apache-2.0
 */

/**
 * @file
 * @brief ESP BSP: S3-EYE
 */

#pragma once

#include "sdkconfig.h"
#include "driver/gpio.h"
#include "driver/sdmmc_host.h"
#include "lvgl.h"
#include "esp_lvgl_port.h"
#include "esp_codec_dev.h"
#include "iot_button.h"


#include "driver/i2s_std.h"

#include "esp_lcd_touch.h"
#include "esp_lcd_touch_cst816s.h"
#include "esp_io_expander_tca95xx_16bit.h"


#define CAMERA_MODULE_NAME "AMETER-CAM"

/**************************************************************************************************
 * Ameter  pinout
 **************************************************************************************************/


#define BSP_LCD_SPI_CLK				(GPIO_NUM_0)
#define BSP_LCD_DC        		    (GPIO_NUM_1)
#define BSP_I2C0_SDA				(GPIO_NUM_2)
#define BSP_LCD_SPI_MOSI      		(GPIO_NUM_3)
#define BSP_LCD_SPI_CS      		(GPIO_NUM_4)
#define TOUCH_INT         			(GPIO_NUM_5)
#define BSP_I2C0_SCL           		(GPIO_NUM_6)
/* I2C */
#define BSP_I2C0_CLK_HZ			(400000)
#define BSP_I2C1_CLK_HZ			(400000)

#define BSP_LCD_BACKLIGHT			(GPIO_NUM_7)
#define BSP_ADC_AMP					(GPIO_NUM_8)
#define BSP_ADC_RPM					(GPIO_NUM_9)
#define BSP_ADC_VOLT				(GPIO_NUM_10)

/* Camera */
//#define BSP_CAMERA_D2    			(GPIO_NUM_11)
//#define BSP_CAMERA_D1     			(GPIO_NUM_12)
//#define BSP_CAMERA_D3      			(GPIO_NUM_13)
//#define BSP_CAMERA_D0      			(GPIO_NUM_14)
//#define BSP_CAMERA_D4     			(GPIO_NUM_15)
//#define BSP_CAMERA_PCLK    			(GPIO_NUM_16)
//#define BSP_CAMERA_D5   			(GPIO_NUM_17)
//#define BSP_CAMERA_D6      			(GPIO_NUM_18)


#define CAMERA_PIN_PWDN -1
#define CAMERA_PIN_RESET -1

#define CAMERA_PIN_VSYNC 41
#define CAMERA_PIN_HREF 46
#define CAMERA_PIN_PCLK 16 //16
#define CAMERA_PIN_XCLK 21  //21

//#define CAMERA_PIN_SIOD 38
//#define CAMERA_PIN_SIOC 39

#define CAMERA_PIN_D0 14
#define CAMERA_PIN_D1 12
#define CAMERA_PIN_D2 11
#define CAMERA_PIN_D3 13
#define CAMERA_PIN_D4 15
#define CAMERA_PIN_D5 17
#define CAMERA_PIN_D6 18
#define CAMERA_PIN_D7 42  //26 using adc 10



//USB
#define BSP_USB_DM     				(GPIO_NUM_19)
#define BSP_USB_DP      			(GPIO_NUM_20)

#define BSP_CAMERA_XCLK    			(GPIO_NUM_21) //MCLK
#define BSP_CAMERA_D7    			(GPIO_NUM_26)
#define BSP_I2C1_SDA				(GPIO_NUM_38)
#define BSP_I2C1_SCL				(GPIO_NUM_39)

#define BSP_EXT_INT					(GPIO_NUM_40)
//#define BSP_CAMERA_VSYNC  			(GPIO_NUM_41)

//#define BSP_PWM_AUDIO  				(GPIO_NUM_42)
//#define BSP_UART0_RTS  				(GPIO_NUM_45)
//#define BSP_CAMERA_HSYNC   			(GPIO_NUM_46)



#define BSP_LCD_RST           		(GPIO_NUM_NC)  // GPIO_NUM_NC  GPIO_NUM_47

#define BSP_SPEAKER					(GPIO_NUM_47)



//Mobus uart
#define BSP_MB_UART_PORT_NUM 		1
#define BSP_MB_UART_BAUD_RATE		115200
#define BSP_MB_UART_RXD				44
#define BSP_MB_UART_TXD				43
#define BSP_MB_UART_RTS				45
//#define BSP_MB_COMM_MODE_RTU		y
//# CONFIG_MB_COMM_MODE_ASCII is not set
#define BSP_MB_SLAVE_ADDR			1

//For master config :
//CONFIG_FMB_MASTER_TIMEOUT_MS_RESPOND = 2000
//CONFIG_MB_UART_PORT_NUM=2
//CONFIG_MB_UART_BAUD_RATE=115200
//CONFIG_MB_UART_RXD=4
//CONFIG_MB_UART_TXD=19
//CONFIG_MB_UART_RTS=18
//CONFIG_MB_COMM_MODE_RTU=y
///# CONFIG_MB_COMM_MODE_ASCII is not set

//TP-VDD  橘
//TP-RST 40 灰
//TP-INT 41 藍
//TP-SDA 42 紫
//TP-SCL 2 棕


/* Display */







//#define BSP_LCD_SPI_MOSI      (GPIO_NUM_47)
//#define BSP_LCD_SPI_CLK       (GPIO_NUM_21)
//#define BSP_LCD_SPI_CS        (GPIO_NUM_45)
//#define BSP_LCD_DC            (GPIO_NUM_48)
//#define BSP_LCD_RST           (GPIO_NUM_20)








#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief BSP display configuration structure
 *
 */
typedef struct {
    lvgl_port_cfg_t lvgl_port_cfg;
    uint32_t buffer_size;
    bool double_buffer;
    struct {
        unsigned int buff_dma: 1;
        unsigned int buff_spiram: 1;
    } flags;
} bsp_display_cfg_t;


//////////////////////////////////////////////////////////////////////////////////////////////////////////////////


//aaron
esp_err_t bsp_touch_init(esp_lcd_touch_handle_t *tp);



/**************************************************************************************************
 *
 * I2C interface
 *
 * There are two devices connected to I2C peripheral:
 *  - QMA7981 Inertial measurement unit
 *  - OV2640 Camera module
 **************************************************************************************************/
#define BSP_I2C_NUM     0
#define BSP_I2C_NUM1     1

/**
 * @brief Init I2C driver
 *
 * @return
 *      - ESP_OK                On success
 *      - ESP_ERR_INVALID_ARG   I2C parameter error
 *      - ESP_FAIL              I2C driver installation error
 *
 */
esp_err_t bsp_i2c_init(void);

/**
 * @brief Deinit I2C driver and free its resources
 *
 * @return
 *      - ESP_OK                On success
 *      - ESP_ERR_INVALID_ARG   I2C parameter error
 *
 */
esp_err_t bsp_i2c_deinit(void);

/**************************************************************************************************
 *
 * Camera interface
 *
 * ESP32-S3-EYE is shipped with OV2640 camera module.
 * As a camera driver, esp32-camera component is used.
 *
 * Example configuration:
 * \code{.c}
 * const camera_config_t camera_config = BSP_CAMERA_DEFAULT_CONFIG;
 * esp_err_t err = esp_camera_init(&camera_config);
 * \endcode
 **************************************************************************************************/
/**
 * @brief ESP32-S3-EYE camera default configuration
 *
 * In this configuration we select RGB565 color format and 240x240 image size - matching the display.
 * We use double-buffering for the best performance.
 * Since we don't want to waste internal SRAM, we allocate the framebuffers in external PSRAM.
 * By setting XCLK to 16MHz, we configure the esp32-camera driver to use EDMA when accessing the PSRAM.
 *
 * @attention I2C must be enabled by bsp_i2c_init(), before camera is initialized
 */
#define BSP_CAMERA_DEFAULT_CONFIG         \
    {                                     \
        .pin_pwdn = GPIO_NUM_NC,          \
        .pin_reset = GPIO_NUM_NC,         \
        .pin_xclk = BSP_CAMERA_XCLK,      \
        .pin_sccb_sda = GPIO_NUM_NC,      \
        .pin_sccb_scl = GPIO_NUM_NC,      \
        .pin_d7 = BSP_CAMERA_D7,          \
        .pin_d6 = BSP_CAMERA_D6,          \
        .pin_d5 = BSP_CAMERA_D5,          \
        .pin_d4 = BSP_CAMERA_D4,          \
        .pin_d3 = BSP_CAMERA_D3,          \
        .pin_d2 = BSP_CAMERA_D2,          \
        .pin_d1 = BSP_CAMERA_D1,          \
        .pin_d0 = BSP_CAMERA_D0,          \
        .pin_vsync = BSP_CAMERA_VSYNC,    \
        .pin_href = BSP_CAMERA_HSYNC,     \
        .pin_pclk = BSP_CAMERA_PCLK,      \
        .xclk_freq_hz = 16000000,         \
        .ledc_timer = LEDC_TIMER_0,       \
        .ledc_channel = LEDC_CHANNEL_0,   \
        .pixel_format = PIXFORMAT_RGB565, \
        .frame_size = FRAMESIZE_240X240,  \
        .jpeg_quality = 12,               \
        .fb_count = 2,                    \
        .fb_location = CAMERA_FB_IN_PSRAM,\
        .sccb_i2c_port = BSP_I2C_NUM,     \
    }

/**************************************************************************************************
 *
 * SPIFFS
 *
 * After mounting the SPIFFS, it can be accessed with stdio functions ie.:
 * \code{.c}
 * FILE* f = fopen(BSP_SPIFFS_MOUNT_POINT"/hello.txt", "w");
 * fprintf(f, "Hello World!\n");
 * fclose(f);
 * \endcode
 **************************************************************************************************/
#define BSP_SPIFFS_MOUNT_POINT      CONFIG_BSP_SPIFFS_MOUNT_POINT

/**
 * @brief Mount SPIFFS to virtual file system
 *
 * @return
 *      - ESP_OK on success
 *      - ESP_ERR_INVALID_STATE if esp_vfs_spiffs_register was already called
 *      - ESP_ERR_NO_MEM if memory can not be allocated
 *      - ESP_FAIL if partition can not be mounted
 *      - other error codes
 */
esp_err_t bsp_spiffs_mount(void);

/**
 * @brief Unmount SPIFFS from virtual file system
 *
 * @return
 *      - ESP_OK on success
 *      - ESP_ERR_INVALID_STATE if already unmounted
 */
esp_err_t bsp_spiffs_unmount(void);

/**************************************************************************************************
 *
 * uSD card
 *
 * After mounting the uSD card, it can be accessed with stdio functions ie.:
 * \code{.c}
 * FILE* f = fopen(BSP_MOUNT_POINT"/hello.txt", "w");
 * fprintf(f, "Hello %s!\n", bsp_sdcard->cid.name);
 * fclose(f);
 * \endcode
 **************************************************************************************************/
#define BSP_SD_MOUNT_POINT      CONFIG_BSP_SD_MOUNT_POINT
extern sdmmc_card_t *bsp_sdcard;

/**
 * @brief Mount microSD card to virtual file system
 *
 * @return
 *      - ESP_OK on success
 *      - ESP_ERR_INVALID_STATE if esp_vfs_fat_sdmmc_mount was already called
 *      - ESP_ERR_NO_MEM if memory cannot be allocated
 *      - ESP_FAIL if partition cannot be mounted
 *      - other error codes from SDMMC or SPI drivers, SDMMC protocol, or FATFS drivers
 */
esp_err_t bsp_sdcard_mount(void);

/**
 * @brief Unmount microSD card from virtual file system
 *
 * @return
 *      - ESP_OK on success
 *      - ESP_ERR_NOT_FOUND if the partition table does not contain FATFS partition with given label
 *      - ESP_ERR_INVALID_STATE if esp_vfs_fat_spiflash_mount was already called
 *      - ESP_ERR_NO_MEM if memory can not be allocated
 *      - ESP_FAIL if partition can not be mounted
 *      - other error codes from wear levelling library, SPI flash driver, or FATFS drivers
 */
esp_err_t bsp_sdcard_unmount(void);

/**************************************************************************************************
 *
 * LCD interface
 *
 * ESP32-S3-EYE is shipped with 1.3inch ST7789 display controller.
 * It features 16-bit colors and 240x240 resolution.
 *
 * LVGL is used as graphics library. LVGL is NOT thread safe, therefore the user must take LVGL mutex
 * by calling bsp_display_lock() before calling and LVGL API (lv_...) and then give the mutex with
 * bsp_display_unlock().
 **************************************************************************************************/
#define BSP_LCD_H_RES              (240)
#define BSP_LCD_V_RES              (320)
#define BSP_LCD_PIXEL_CLOCK_HZ     (80 * 1000 * 1000)
#define BSP_LCD_SPI_NUM            (SPI3_HOST)

#define BSP_LCD_DRAW_BUFF_SIZE     (BSP_LCD_H_RES * BSP_LCD_V_RES / 4)  // Quarter screen buffer to reduce tearing
#define BSP_LCD_DRAW_BUFF_DOUBLE   (1)  // Keep double buffer for smooth camera preview

// LCD panel configuration
#define BSP_LCD_COLOR_SPACE        (ESP_LCD_COLOR_SPACE_RGB)
#define BSP_LCD_BITS_PER_PIXEL     (16)
#define BSP_LCD_BIGENDIAN          (0)  // Little endian for RGB565

/**
 * @brief Initialize display
 *
 * This function initializes SPI, display controller and starts LVGL handling task.
 *
 * @return Pointer to LVGL display or NULL when error occured
 */
lv_disp_t *bsp_display_start(void);
void bsp_display_restart(void);

/**
 * @brief Initialize display
 *
 * This function initializes SPI, display controller and starts LVGL handling task.
 * LCD backlight must be enabled separately by calling bsp_display_brightness_set()
 *
 * @param cfg display configuration
 *
 * @return Pointer to LVGL display or NULL when error occured
 */
lv_disp_t *bsp_display_start_with_config(const bsp_display_cfg_t *cfg);

/**
 * @brief Get pointer to input device (touch, buttons, ...)
 *
 * @note The LVGL input device is initialized in bsp_display_start() function.
 *
 * @return Pointer to LVGL input device or NULL when not initialized
 */
lv_indev_t *bsp_display_get_input_dev(void);

/**
 * @brief Take LVGL mutex
 *
 * @param timeout_ms Timeout in [ms]. 0 will block indefinitely.
 * @return true  Mutex was taken
 * @return false Mutex was NOT taken
 */
bool bsp_display_lock(uint32_t timeout_ms);

/**
 * @brief Give LVGL mutex
 *
 */
void bsp_display_unlock(void);

/**
 * @brief Set display's brightness
 *
 * Brightness is controlled with PWM signal to a pin controlling backlight.
 * Display must be already initialized by calling bsp_display_start()
 *
 * @param[in] brightness_percent Brightness in [%]
 * @return
 *      - ESP_OK                On success
 *      - ESP_ERR_INVALID_ARG   Parameter error
 */
esp_err_t bsp_display_brightness_set(int brightness_percent);

/**
 * @brief Turn on display backlight
 *
 * Display must be already initialized by calling bsp_display_start()
 *
 * @return
 *      - ESP_OK                On success
 *      - ESP_ERR_INVALID_ARG   Parameter error
 */
esp_err_t bsp_display_backlight_on(void);

/**
 * @brief Turn off display backlight
 *
 * Display must be already initialized by calling bsp_display_start()
 *
 * @return
 *      - ESP_OK                On success
 *      - ESP_ERR_INVALID_ARG   Parameter error
 */
esp_err_t bsp_display_backlight_off(void);

/**
 * @brief Rotate screen
 *
 * Display must be already initialized by calling bsp_display_start()
 *
 * @param[in] disp Pointer to LVGL display
 * @param[in] rotation Angle of the display rotation
 */
void bsp_display_rotate(lv_disp_t *disp, lv_disp_rot_t rotation);






/**************************************************************************************************
 *
 * ADC interface
 *
 * There are multiple devices connected to ADC peripheral:
 *  - Buttons
 *
 * After initialization of ADC, use adc_handle when using ADC driver.
 **************************************************************************************************/

#define BSP_ADC_UNIT     ADC_UNIT_1

/**
 * @brief Initialize ADC
 *
 * The ADC can be initialized inside BSP, when needed.
 *
 * @param[out] adc_handle Returned ADC handle
 */
esp_err_t bsp_adc_initialize(void);


/**
 * @brief Get ADC handle
 *
 * @note This function is available only in IDF5 and higher
 *
 * @return ADC handle
 */
adc_oneshot_unit_handle_t bsp_adc_get_handle(void);



#ifdef __cplusplus
}
#endif
