#pragma once

/**
 * @file error_handler.h
 * @brief Enhanced error handling and recovery system
 */

#ifdef __cplusplus
extern "C" {
#endif

#include "esp_err.h"
#include "esp_log.h"
#include <stdbool.h>
#include <stdint.h>

// Error severity levels
typedef enum {
    ERROR_SEVERITY_INFO = 0,
    ERROR_SEVERITY_WARNING,
    ERROR_SEVERITY_ERROR,
    ERROR_SEVERITY_CRITICAL,
    ERROR_SEVERITY_FATAL
} error_severity_t;

// Error categories
typedef enum {
    ERROR_CATEGORY_MEMORY = 0,
    ERROR_CATEGORY_HARDWARE,
    ERROR_CATEGORY_COMMUNICATION,
    ERROR_CATEGORY_FILESYSTEM,
    ERROR_CATEGORY_TASK,
    ERROR_CATEGORY_SYSTEM,
    ERROR_CATEGORY_APPLICATION,
    ERROR_CATEGORY_MAX
} error_category_t;

// Error recovery actions
typedef enum {
    RECOVERY_ACTION_NONE = 0,
    RECOVERY_ACTION_RETRY,
    RECOVERY_ACTION_RESET_COMPONENT,
    RECOVERY_ACTION_RESTART_TASK,
    RECOVERY_ACTION_SYSTEM_RESTART,
    RECOVERY_ACTION_CUSTOM
} recovery_action_t;

// Error record structure
typedef struct {
    uint32_t error_id;
    error_severity_t severity;
    error_category_t category;
    esp_err_t error_code;
    const char* component;
    const char* function;
    const char* file;
    int line;
    uint32_t timestamp;
    const char* description;
    recovery_action_t recovery_action;
    uint32_t occurrence_count;
} error_record_t;

// Error handler configuration
typedef struct {
    bool enable_logging;
    bool enable_recovery;
    bool enable_statistics;
    uint32_t max_error_records;
    uint32_t max_retry_attempts;
    uint32_t retry_delay_ms;
} error_handler_config_t;

// Custom recovery function type
typedef esp_err_t (*recovery_function_t)(const error_record_t* error);

/**
 * @brief Initialize error handler
 * @param config Error handler configuration
 * @return ESP_OK on success
 */
esp_err_t error_handler_init(const error_handler_config_t* config);

/**
 * @brief Deinitialize error handler and free allocated memory
 * @return ESP_OK on success
 */
esp_err_t error_handler_deinit(void);

/**
 * @brief Report an error
 * @param severity Error severity
 * @param category Error category
 * @param error_code ESP error code
 * @param component Component name
 * @param function Function name
 * @param file Source file
 * @param line Source line
 * @param description Error description
 * @return ESP_OK on success
 */
esp_err_t error_handler_report(error_severity_t severity,
                              error_category_t category,
                              esp_err_t error_code,
                              const char* component,
                              const char* function,
                              const char* file,
                              int line,
                              const char* description);

/**
 * @brief Register custom recovery function
 * @param category Error category
 * @param recovery_func Recovery function
 * @return ESP_OK on success
 */
esp_err_t error_handler_register_recovery(error_category_t category, recovery_function_t recovery_func);

/**
 * @brief Get error statistics
 * @param category Error category (ERROR_CATEGORY_MAX for all)
 * @param count Output: number of errors
 * @param last_error Output: last error record
 * @return ESP_OK on success
 */
esp_err_t error_handler_get_stats(error_category_t category, uint32_t* count, error_record_t* last_error);

/**
 * @brief Clear error history
 * @param category Error category (ERROR_CATEGORY_MAX for all)
 * @return ESP_OK on success
 */
esp_err_t error_handler_clear_history(error_category_t category);

/**
 * @brief Print error report
 */
void error_handler_print_report(void);

/**
 * @brief Check if system should restart due to critical errors
 * @return true if restart is recommended
 */
bool error_handler_should_restart(void);

// Convenience macros
#define ERROR_REPORT(severity, category, code, description) \
    error_handler_report(severity, category, code, __FUNCTION__, __FUNCTION__, __FILE__, __LINE__, description)

#define ERROR_CHECK_REPORT(x, severity, category, description) do { \
    esp_err_t err_rc_ = (x); \
    if (err_rc_ != ESP_OK) { \
        error_handler_report(severity, category, err_rc_, __FUNCTION__, __FUNCTION__, __FILE__, __LINE__, description); \
    } \
} while(0)

#define CRITICAL_ERROR(category, code, description) \
    ERROR_REPORT(ERROR_SEVERITY_CRITICAL, category, code, description)

#define WARNING_ERROR(category, code, description) \
    ERROR_REPORT(ERROR_SEVERITY_WARNING, category, code, description)

#define FATAL_ERROR(category, code, description) do { \
    ERROR_REPORT(ERROR_SEVERITY_FATAL, category, code, description); \
    esp_restart(); \
} while(0)

#ifdef __cplusplus
}
#endif
