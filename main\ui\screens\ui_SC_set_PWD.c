// This file was generated by SquareLine Studio
// SquareLine Studio version: SquareLine Studio 1.4.2
// LVGL version: 8.3.6
// Project name: A_Meter_GUI_20240305

#include "../ui.h"
#include <stdio.h>

void ui_SC_set_PWD_screen_init(void)
{
    ui_SC_set_PWD = lv_obj_create(NULL);
    lv_obj_clear_flag(ui_SC_set_PWD, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_img_src(ui_SC_set_PWD, ui_img_background, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui_SC_set_PWD, &ui_font_WD_48, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_Label56 = lv_label_create(ui_SC_set_PWD);
    lv_obj_set_width(ui_Label56, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(ui_Label56, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_x(ui_Label56, 0);
    lv_obj_set_y(ui_Label56, -103);
    lv_obj_set_align(ui_Label56, LV_ALIGN_CENTER);
    lv_label_set_text(ui_Label56, "Set Password");
    lv_obj_set_style_text_color(ui_Label56, lv_color_hex(0x000000), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui_Label56, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui_Label56, &ui_font_WD_28, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_TextArea2 = lv_textarea_create(ui_SC_set_PWD);
    lv_obj_set_width(ui_TextArea2, 240);
    lv_obj_set_height(ui_TextArea2, 30);
    lv_obj_set_x(ui_TextArea2, 0);
    lv_obj_set_y(ui_TextArea2, -76);
    lv_obj_set_align(ui_TextArea2, LV_ALIGN_CENTER);
    lv_textarea_set_max_length(ui_TextArea2, 6);
    lv_textarea_set_placeholder_text(ui_TextArea2, "Placeholder...");
    lv_textarea_set_password_mode(ui_TextArea2, true);
    lv_obj_clear_flag(ui_TextArea2, LV_OBJ_FLAG_SCROLLABLE | LV_OBJ_FLAG_SCROLL_ELASTIC | LV_OBJ_FLAG_SCROLL_MOMENTUM |
                      LV_OBJ_FLAG_SCROLL_CHAIN);     /// Flags
    lv_obj_set_style_text_color(ui_TextArea2, lv_color_hex(0x000000), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui_TextArea2, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui_TextArea2, &lv_font_montserrat_14, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui_TextArea2, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_TextArea2, 255, LV_PART_MAIN | LV_STATE_DEFAULT);



    ui_Roller2 = lv_roller_create(ui_SC_set_PWD);
    lv_roller_set_options(ui_Roller2, "0\n1\n2\n3\n4\n5\n6\n7\n8\n9", LV_ROLLER_MODE_NORMAL);
    lv_obj_set_width(ui_Roller2, 84);
    lv_obj_set_height(ui_Roller2, 133);
    lv_obj_set_x(ui_Roller2, 0);
    lv_obj_set_y(ui_Roller2, 7);
    lv_obj_set_align(ui_Roller2, LV_ALIGN_CENTER);
    lv_obj_set_style_text_color(ui_Roller2, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui_Roller2, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_add_flag(ui_Roller2, LV_OBJ_FLAG_HIDDEN); // Hide old roller

    ui_BTN_Rig2 = lv_imgbtn_create(ui_SC_set_PWD);
    lv_imgbtn_set_src(ui_BTN_Rig2, LV_IMGBTN_STATE_RELEASED, NULL, &ui_img_right_arrow_png, NULL);
    lv_imgbtn_set_src(ui_BTN_Rig2, LV_IMGBTN_STATE_PRESSED, NULL, &ui_img_right_arrow_1_png, NULL);
    lv_imgbtn_set_src(ui_BTN_Rig2, LV_IMGBTN_STATE_DISABLED, NULL, &ui__temporary_image, NULL);
    lv_imgbtn_set_src(ui_BTN_Rig2, LV_IMGBTN_STATE_CHECKED_PRESSED, NULL, &ui__temporary_image, NULL);
    lv_imgbtn_set_src(ui_BTN_Rig2, LV_IMGBTN_STATE_CHECKED_RELEASED, NULL, &ui__temporary_image, NULL);
    lv_imgbtn_set_src(ui_BTN_Rig2, LV_IMGBTN_STATE_CHECKED_DISABLED, NULL, &ui__temporary_image, NULL);
    lv_obj_set_height(ui_BTN_Rig2, 133);
    lv_obj_set_width(ui_BTN_Rig2, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_x(ui_BTN_Rig2, 98);
    lv_obj_set_y(ui_BTN_Rig2, 7);
    lv_obj_set_align(ui_BTN_Rig2, LV_ALIGN_CENTER);
    lv_obj_add_flag(ui_BTN_Rig2, LV_OBJ_FLAG_HIDDEN); // Hide old button

    ui_BTN_Lif2 = lv_imgbtn_create(ui_SC_set_PWD);
    lv_imgbtn_set_src(ui_BTN_Lif2, LV_IMGBTN_STATE_RELEASED, NULL, &ui_img_lift_arrow_png, NULL);
    lv_imgbtn_set_src(ui_BTN_Lif2, LV_IMGBTN_STATE_PRESSED, NULL, &ui_img_lift_arrow_1_png, NULL);
    lv_imgbtn_set_src(ui_BTN_Lif2, LV_IMGBTN_STATE_DISABLED, NULL, &ui__temporary_image, NULL);
    lv_imgbtn_set_src(ui_BTN_Lif2, LV_IMGBTN_STATE_CHECKED_PRESSED, NULL, &ui__temporary_image, NULL);
    lv_imgbtn_set_src(ui_BTN_Lif2, LV_IMGBTN_STATE_CHECKED_RELEASED, NULL, &ui__temporary_image, NULL);
    lv_imgbtn_set_src(ui_BTN_Lif2, LV_IMGBTN_STATE_CHECKED_DISABLED, NULL, &ui__temporary_image, NULL);
    lv_obj_set_height(ui_BTN_Lif2, 133);
    lv_obj_set_width(ui_BTN_Lif2, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_x(ui_BTN_Lif2, -98);
    lv_obj_set_y(ui_BTN_Lif2, 7);
    lv_obj_set_align(ui_BTN_Lif2, LV_ALIGN_CENTER);
    lv_obj_add_flag(ui_BTN_Lif2, LV_OBJ_FLAG_HIDDEN); // Hide old button

    ui_BTN_set_PWD_OK = lv_btn_create(ui_SC_set_PWD);
    lv_obj_set_width(ui_BTN_set_PWD_OK, 130);
    lv_obj_set_height(ui_BTN_set_PWD_OK, 33);
    lv_obj_set_x(ui_BTN_set_PWD_OK, -80);
    lv_obj_set_y(ui_BTN_set_PWD_OK, 98);
    lv_obj_set_align(ui_BTN_set_PWD_OK, LV_ALIGN_CENTER);
    lv_obj_add_flag(ui_BTN_set_PWD_OK, LV_OBJ_FLAG_SCROLL_ON_FOCUS);     /// Flags
    lv_obj_clear_flag(ui_BTN_set_PWD_OK, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_add_flag(ui_BTN_set_PWD_OK, LV_OBJ_FLAG_HIDDEN); // Hide old OK button
    lv_obj_set_style_bg_color(ui_BTN_set_PWD_OK, lv_color_hex(0x6EB763), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_BTN_set_PWD_OK, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_color(ui_BTN_set_PWD_OK, lv_color_hex(0xD6E9C4), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_dir(ui_BTN_set_PWD_OK, LV_GRAD_DIR_HOR, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_color(ui_BTN_set_PWD_OK, lv_color_hex(0x000000), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_opa(ui_BTN_set_PWD_OK, 64, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui_BTN_set_PWD_OK, 1, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_spread(ui_BTN_set_PWD_OK, 1, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_ofs_x(ui_BTN_set_PWD_OK, 1, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_ofs_y(ui_BTN_set_PWD_OK, 2, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_Label58 = lv_label_create(ui_BTN_set_PWD_OK);
    lv_obj_set_width(ui_Label58, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(ui_Label58, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_align(ui_Label58, LV_ALIGN_CENTER);
    lv_label_set_text(ui_Label58, "OK");
    lv_obj_set_style_text_font(ui_Label58, &ui_font_WD_28, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_BTN_set_PWD_cancel = lv_btn_create(ui_SC_set_PWD);
    lv_obj_set_width(ui_BTN_set_PWD_cancel, 130);
    lv_obj_set_height(ui_BTN_set_PWD_cancel, 33);
    lv_obj_set_x(ui_BTN_set_PWD_cancel, 80);
    lv_obj_set_y(ui_BTN_set_PWD_cancel, 98);
    lv_obj_set_align(ui_BTN_set_PWD_cancel, LV_ALIGN_CENTER);
    lv_obj_add_flag(ui_BTN_set_PWD_cancel, LV_OBJ_FLAG_SCROLL_ON_FOCUS);     /// Flags
    lv_obj_clear_flag(ui_BTN_set_PWD_cancel, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_add_flag(ui_BTN_set_PWD_cancel, LV_OBJ_FLAG_HIDDEN); // Hide old Cancel button
    lv_obj_set_style_bg_color(ui_BTN_set_PWD_cancel, lv_color_hex(0xAC0000), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_BTN_set_PWD_cancel, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_color(ui_BTN_set_PWD_cancel, lv_color_hex(0xAD6A6A), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_dir(ui_BTN_set_PWD_cancel, LV_GRAD_DIR_HOR, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_Label64 = lv_label_create(ui_BTN_set_PWD_cancel);
    lv_obj_set_width(ui_Label64, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(ui_Label64, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_align(ui_Label64, LV_ALIGN_CENTER);
    lv_label_set_text(ui_Label64, "Cancel");
    lv_obj_set_style_text_font(ui_Label64, &ui_font_WD_28, LV_PART_MAIN | LV_STATE_DEFAULT);

    // Create custom numeric keyboard
    ui_CustomKeyboard_PWD = lv_obj_create(ui_SC_set_PWD);
    lv_obj_set_width(ui_CustomKeyboard_PWD, 320);
    lv_obj_set_height(ui_CustomKeyboard_PWD, 180);
    lv_obj_set_x(ui_CustomKeyboard_PWD, 0);
    lv_obj_set_y(ui_CustomKeyboard_PWD, 30);
    lv_obj_set_align(ui_CustomKeyboard_PWD, LV_ALIGN_CENTER);
    // Ensure keyboard is visible
    lv_obj_clear_flag(ui_CustomKeyboard_PWD, LV_OBJ_FLAG_HIDDEN);
    lv_obj_clear_flag(ui_CustomKeyboard_PWD, LV_OBJ_FLAG_SCROLLABLE);
    lv_obj_set_style_bg_color(ui_CustomKeyboard_PWD, lv_color_hex(0x2C2C2C), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_CustomKeyboard_PWD, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_border_width(ui_CustomKeyboard_PWD, 2, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_border_color(ui_CustomKeyboard_PWD, lv_color_hex(0x555555), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui_CustomKeyboard_PWD, 8, LV_PART_MAIN | LV_STATE_DEFAULT);

    // Create keyboard buttons - Row 1: 1 2 3 4 5
    for(int i = 0; i < 5; i++) {
        lv_obj_t * btn = lv_btn_create(ui_CustomKeyboard_PWD);
        lv_obj_set_width(btn, 50);
        lv_obj_set_height(btn, 40);
        lv_obj_set_x(btn, -120 + i * 60);
        lv_obj_set_y(btn, -60);
        lv_obj_set_align(btn, LV_ALIGN_CENTER);
        lv_obj_set_style_bg_color(btn, lv_color_hex(0x4A4A4A), LV_PART_MAIN | LV_STATE_DEFAULT);
        lv_obj_set_style_bg_color(btn, lv_color_hex(0x6A6A6A), LV_PART_MAIN | LV_STATE_PRESSED);
        lv_obj_set_style_radius(btn, 5, LV_PART_MAIN | LV_STATE_DEFAULT);

        lv_obj_t * label = lv_label_create(btn);
        lv_obj_set_align(label, LV_ALIGN_CENTER);
        char btn_text[2];
        sprintf(btn_text, "%d", i + 1);
        lv_label_set_text(label, btn_text);
        lv_obj_set_style_text_color(label, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
        lv_obj_set_style_text_font(label, &lv_font_montserrat_14, LV_PART_MAIN | LV_STATE_DEFAULT);

        lv_obj_add_event_cb(btn, ui_event_CustomKeyboard_PWD, LV_EVENT_CLICKED, NULL);
    }

    // Create keyboard buttons - Row 2: 6 7 8 9 0
    for(int i = 0; i < 5; i++) {
        lv_obj_t * btn = lv_btn_create(ui_CustomKeyboard_PWD);
        lv_obj_set_width(btn, 50);
        lv_obj_set_height(btn, 40);
        lv_obj_set_x(btn, -120 + i * 60);
        lv_obj_set_y(btn, -10);
        lv_obj_set_align(btn, LV_ALIGN_CENTER);
        lv_obj_set_style_bg_color(btn, lv_color_hex(0x4A4A4A), LV_PART_MAIN | LV_STATE_DEFAULT);
        lv_obj_set_style_bg_color(btn, lv_color_hex(0x6A6A6A), LV_PART_MAIN | LV_STATE_PRESSED);
        lv_obj_set_style_radius(btn, 5, LV_PART_MAIN | LV_STATE_DEFAULT);

        lv_obj_t * label = lv_label_create(btn);
        lv_obj_set_align(label, LV_ALIGN_CENTER);
        char btn_text[2];
        if(i < 4) {
            sprintf(btn_text, "%d", i + 6);
        } else {
            sprintf(btn_text, "0");
        }
        lv_label_set_text(label, btn_text);
        lv_obj_set_style_text_color(label, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
        lv_obj_set_style_text_font(label, &lv_font_montserrat_14, LV_PART_MAIN | LV_STATE_DEFAULT);

        lv_obj_add_event_cb(btn, ui_event_CustomKeyboard_PWD, LV_EVENT_CLICKED, NULL);
    }

    // Create keyboard buttons - Row 3: OK Cancel
    // OK button
    lv_obj_t * btn_ok = lv_btn_create(ui_CustomKeyboard_PWD);
    lv_obj_set_width(btn_ok, 120);
    lv_obj_set_height(btn_ok, 40);
    lv_obj_set_x(btn_ok, -60);
    lv_obj_set_y(btn_ok, 40);
    lv_obj_set_align(btn_ok, LV_ALIGN_CENTER);
    lv_obj_set_style_bg_color(btn_ok, lv_color_hex(0x6EB763), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(btn_ok, lv_color_hex(0x8EC783), LV_PART_MAIN | LV_STATE_PRESSED);
    lv_obj_set_style_radius(btn_ok, 5, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * label_ok = lv_label_create(btn_ok);
    lv_obj_set_align(label_ok, LV_ALIGN_CENTER);
    lv_label_set_text(label_ok, "OK");
    lv_obj_set_style_text_color(label_ok, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(label_ok, &lv_font_montserrat_14, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_add_event_cb(btn_ok, ui_event_BTN_set_PWD_OK, LV_EVENT_CLICKED, NULL);

    // Cancel button
    lv_obj_t * btn_cancel = lv_btn_create(ui_CustomKeyboard_PWD);
    lv_obj_set_width(btn_cancel, 120);
    lv_obj_set_height(btn_cancel, 40);
    lv_obj_set_x(btn_cancel, 60);
    lv_obj_set_y(btn_cancel, 40);
    lv_obj_set_align(btn_cancel, LV_ALIGN_CENTER);
    lv_obj_set_style_bg_color(btn_cancel, lv_color_hex(0xAC0000), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(btn_cancel, lv_color_hex(0xCC2020), LV_PART_MAIN | LV_STATE_PRESSED);
    lv_obj_set_style_radius(btn_cancel, 5, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * label_cancel = lv_label_create(btn_cancel);
    lv_obj_set_align(label_cancel, LV_ALIGN_CENTER);
    lv_label_set_text(label_cancel, "Cancel");
    lv_obj_set_style_text_color(label_cancel, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(label_cancel, &lv_font_montserrat_14, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_add_event_cb(btn_cancel, ui_event_BTN_set_PWD_cancel, LV_EVENT_CLICKED, NULL);

    lv_obj_add_event_cb(ui_BTN_Rig2, ui_event_BTN_Rig2, LV_EVENT_ALL, NULL);
    lv_obj_add_event_cb(ui_BTN_Lif2, ui_event_BTN_Lif2, LV_EVENT_ALL, NULL);
    lv_obj_add_event_cb(ui_BTN_set_PWD_OK, ui_event_BTN_set_PWD_OK, LV_EVENT_ALL, NULL);
    lv_obj_add_event_cb(ui_BTN_set_PWD_cancel, ui_event_BTN_set_PWD_cancel, LV_EVENT_ALL, NULL);

}
