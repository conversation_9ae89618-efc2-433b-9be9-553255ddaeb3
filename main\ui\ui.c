// This file was generated by SquareLine Studio
// SquareLine Studio version: SquareLine Studio 1.4.2
// LVGL version: 8.3.6
// Project name: A_Meter_GUI_20240305

#include "ui.h"
#include "ui_helpers.h"

#include "esp_log.h"
#include "widgets/lv_textarea.h"
#define TAG "LVGL UI"

#include "stdio.h"
#include <stdbool.h>

#include <time.h>

#include "bsp/modbus_params.h"  // for modbus parameters structures
#include "camera_power_manager.h"  // for camera power management

// External function for camera initialization
extern bool initialize_camera_hardware(void);

#pragma pack(push, 1)
typedef struct
{
    uint8_t valid;
    uint8_t faceid;
    uint8_t lv;
    char name[12];
} user_table_t;
#pragma pack(pop)

extern uint32_t cur_screen;
extern uint32_t last_screen;
extern uint32_t touched;
extern uint32_t default_screen;
extern int32_t board_init;
extern char pw[12];
extern char default_pw[12];
extern char license_code[12];

extern uint32_t modbus_mode;
extern uint32_t cur_user;
extern uint32_t login_user;
extern uint32_t valid_users;
extern uint32_t del_user;
extern struct tm time2;
extern user_table_t user_table[10];
extern uint32_t mb_slave_id;
extern uint32_t bound_value;

extern uint32_t tool_now;
extern uint32_t tool_now2;

extern uint8_t theme[16];

extern int screen_found;

char buf[12];

#define LEFT 0
#define RIGHT 1
#define TOP 2
#define BOTTOM 3


///////////////////// VARIABLES ////////////////////


// SCREEN: ui_SC_LOGO
void ui_SC_LOGO_screen_init(void);
void ui_event_SC_LOGO(lv_event_t * e);
lv_obj_t * ui_SC_LOGO;
lv_obj_t * ui_Lab_ID;
lv_obj_t * ui_Lab_Ver;
lv_obj_t * ui_Image2;
lv_obj_t * ui_Panel1;
lv_obj_t * ui_Label17;
lv_obj_t * ui_Label2;


// SCREEN: ui_SC_first_login
void ui_SC_first_login_screen_init(void);
lv_obj_t * ui_SC_first_login;
lv_obj_t * ui_Label1;
lv_obj_t * ui_TextArea1;
lv_obj_t * ui_Roller1;
void ui_event_BTN_Rig1(lv_event_t * e);
lv_obj_t * ui_BTN_Rig1;
void ui_event_BTN_Lif1(lv_event_t * e);
lv_obj_t * ui_BTN_Lif1;


// SCREEN: ui_SC_Time_set
void ui_SC_Time_set_screen_init(void);

lv_obj_t * ui_SC_Time_set;
lv_obj_t * ui_Label26;
lv_obj_t * ui_PL_set_date_and_time;
lv_obj_t * ui_Roller_set_Year;
lv_obj_t * ui_Roller_set_month;
lv_obj_t * ui_Roller_set_Day;
lv_obj_t * ui_Roller_set_Hour;
lv_obj_t * ui_Roller_set_minute;
void ui_event_BTN_set_time_conform(lv_event_t * e);
lv_obj_t * ui_BTN_set_time_conform;
lv_obj_t * ui_Label27;
void ui_event_BTN_set_time_cancel(lv_event_t * e);
lv_obj_t * ui_BTN_set_time_cancel;
lv_obj_t * ui_Label60;


// SCREEN: ui_SC_ModBus_set
void ui_SC_ModBus_set_screen_init(void);
lv_obj_t * ui_SC_ModBus_set;
lv_obj_t * ui_Label19;
lv_obj_t * ui_Label_baud;
void ui_event_BTN_ModBus_Master(lv_event_t * e);
lv_obj_t * ui_BTN_ModBus_Master;
lv_obj_t * ui_Label20;
void ui_event_BTN_ModBus_Slave(lv_event_t * e);
lv_obj_t * ui_BTN_ModBus_Slave;
lv_obj_t * ui_Label21;


// SCREEN: ui_SC_Face_Rec
void ui_SC_Face_Rec_screen_init(void);
void ui_event_SC_Face_Rec(lv_event_t * e);
lv_obj_t * ui_SC_Face_Rec;
lv_obj_t * ui_camera_img1;
void ui_event_BTN_exit1(lv_event_t * e);
lv_obj_t * ui_BTN_exit1;
lv_obj_t * ui_Label28;
void ui_event_BTN_Enroll(lv_event_t * e);
lv_obj_t * ui_BTN_Enroll;
lv_obj_t * ui_Label57;
lv_obj_t * ui_Image1;


// SCREEN: ui_SC_1_1
void ui_SC_1_1_screen_init(void);
void ui_event_SC_1_1(lv_event_t * e);
lv_obj_t * ui_SC_1_1;
lv_obj_t * ui_Image4;
lv_obj_t * ui_Image5;
lv_obj_t * ui_Image6;
lv_obj_t * ui_Image8;
lv_obj_t * ui_Image9;
lv_obj_t * ui_Image10;
lv_obj_t * ui_Image11;
void ui_event_BTN_login(lv_event_t * e);
lv_obj_t * ui_BTN_login;
lv_obj_t * ui_Label3;
lv_obj_t * ui_Image165;
lv_obj_t * ui_Image166;


// SCREEN: ui_SC_2_1
void ui_SC_2_1_screen_init(void);
void ui_event_SC_2_1(lv_event_t * e);
lv_obj_t * ui_SC_2_1;
lv_obj_t * ui_Image21;
lv_obj_t * ui_Image22;
lv_obj_t * ui_Image23;
lv_obj_t * ui_Image24;
lv_obj_t * ui_Image25;
lv_obj_t * ui_Image26;
lv_obj_t * ui_Image27;
lv_obj_t * ui_Image28;
lv_obj_t * ui_Image29;
lv_obj_t * ui_Image30;
lv_obj_t * ui_Image31;
lv_obj_t * ui_Image32;
lv_obj_t * ui_Label5;
lv_obj_t * ui_Label6;
lv_obj_t * ui_Lab_Tool_No;
lv_obj_t * ui_Lab_Standby_tool_No;
lv_obj_t * ui_Image42;
lv_obj_t * ui_Image175;


// SCREEN: ui_SC_2_2
void ui_SC_2_2_screen_init(void);
void ui_event_SC_2_2(lv_event_t * e);
lv_obj_t * ui_SC_2_2;
lv_obj_t * ui_Image12;
lv_obj_t * ui_Image33;
lv_obj_t * ui_Image34;
lv_obj_t * ui_Image35;
lv_obj_t * ui_Image36;
lv_obj_t * ui_Image37;
lv_obj_t * ui_Image38;
lv_obj_t * ui_Image39;
lv_obj_t * ui_Image40;
lv_obj_t * ui_Image41;
lv_obj_t * ui_Label7;
lv_obj_t * ui_Label8;
lv_obj_t * ui_Lab_Tool_No1;
lv_obj_t * ui_Lab_Standby_tool_No1;
lv_obj_t * ui_Lab_wheel;
lv_obj_t * ui_sc3_1_dash_img;
lv_obj_t * ui_sc3_1_pointer_img;
lv_obj_t * ui_Image176;
lv_obj_t * ui_Image177;


// SCREEN: ui_SC_2_3
void ui_SC_2_3_screen_init(void);
void ui_event_SC_2_3(lv_event_t * e);
lv_obj_t * ui_SC_2_3;
lv_obj_t * ui_Image44;
lv_obj_t * ui_Image45;
lv_obj_t * ui_Image46;
lv_obj_t * ui_Image47;
lv_obj_t * ui_Image48;
lv_obj_t * ui_Image49;
lv_obj_t * ui_Image50;
lv_obj_t * ui_Image51;
lv_obj_t * ui_Image52;
lv_obj_t * ui_Image53;
lv_obj_t * ui_Label9;
lv_obj_t * ui_Label10;
lv_obj_t * ui_Lab_Tool_No2;
lv_obj_t * ui_Lab_Standby_tool_No2;
lv_obj_t * ui_Lab_RPM;
lv_obj_t * ui_Label11;
lv_obj_t * ui_sc3_2_dash_img;
lv_obj_t * ui_sc3_2_pointer_img;
lv_obj_t * ui_Image178;
lv_obj_t * ui_Image179;


// SCREEN: ui_SC_2_4
void ui_SC_2_4_screen_init(void);
void ui_event_SC_2_4(lv_event_t * e);
lv_obj_t * ui_SC_2_4;
lv_obj_t * ui_Image56;
lv_obj_t * ui_Image57;
lv_obj_t * ui_Image58;
lv_obj_t * ui_Image59;
lv_obj_t * ui_Image60;
lv_obj_t * ui_Image61;
lv_obj_t * ui_Image62;
lv_obj_t * ui_Image63;
lv_obj_t * ui_Image64;
lv_obj_t * ui_Image65;
lv_obj_t * ui_Label12;
lv_obj_t * ui_Lab_wheel1;
lv_obj_t * ui_sc3_3_dash_img;
lv_obj_t * ui_sc3_3_pointer_img;
lv_obj_t * ui_Image180;
lv_obj_t * ui_Image181;


// SCREEN: ui_SC_2_5
void ui_SC_2_5_screen_init(void);
void ui_event_SC_2_5(lv_event_t * e);
lv_obj_t * ui_SC_2_5;
lv_obj_t * ui_Image68;
lv_obj_t * ui_Image69;
lv_obj_t * ui_Image70;
lv_obj_t * ui_Image71;
lv_obj_t * ui_Image72;
lv_obj_t * ui_Image73;
lv_obj_t * ui_Image74;
lv_obj_t * ui_Image75;
lv_obj_t * ui_Image76;
lv_obj_t * ui_Image77;
lv_obj_t * ui_Label13;
lv_obj_t * ui_Lab_RPM1;
lv_obj_t * ui_Label14;
lv_obj_t * ui_sc3_4_dash_img;
lv_obj_t * ui_sc3_4_pointer_img;
lv_obj_t * ui_Image182;
lv_obj_t * ui_Image183;


// SCREEN: ui_SC_2_6
void ui_SC_2_6_screen_init(void);
void ui_event_SC_2_6(lv_event_t * e);
lv_obj_t * ui_SC_2_6;
lv_obj_t * ui_Image80;
lv_obj_t * ui_Image81;
lv_obj_t * ui_Image82;
lv_obj_t * ui_Image83;
lv_obj_t * ui_Image84;
lv_obj_t * ui_Image85;
lv_obj_t * ui_Image86;
lv_obj_t * ui_Image87;
lv_obj_t * ui_Image88;
lv_obj_t * ui_Image89;
lv_obj_t * ui_Label15;
lv_obj_t * ui_Lab_Ampere;
lv_obj_t * ui_Image90;
lv_obj_t * ui_Label16;
lv_obj_t * ui_Image184;
lv_obj_t * ui_Image185;


// SCREEN: ui_SC_3_1
void ui_SC_3_1_screen_init(void);
void ui_event_SC_3_1(lv_event_t * e);
lv_obj_t * ui_SC_3_1;
lv_obj_t * ui_Image3;
lv_obj_t * ui_Image43;
lv_obj_t * ui_Image55;
lv_obj_t * ui_Image66;
lv_obj_t * ui_Image67;
lv_obj_t * ui_Image78;
lv_obj_t * ui_Image79;
lv_obj_t * ui_Image97;
lv_obj_t * ui_X_Asix;
lv_obj_t * ui_LAB_GC_ASIX;
void ui_event_BTN_GC_L(lv_event_t * e);
lv_obj_t * ui_BTN_GC_L;
lv_obj_t * ui_Image186;
lv_obj_t * ui_Image187;
void ui_event_BTN_GC_R(lv_event_t * e);
lv_obj_t * ui_BTN_GC_R;
lv_obj_t * ui_PL_GC_Alarm;
lv_obj_t * ui_Label41;
void ui_event_BTN_AlarmRst(lv_event_t * e);
lv_obj_t * ui_BTN_AlarmRst;
lv_obj_t * ui_Label42;
lv_obj_t * ui_PL_AlarmRst_PWD;
lv_obj_t * ui_Label49;
lv_obj_t * ui_TextArea4;
void ui_event_BTN_Lif5(lv_event_t * e);
lv_obj_t * ui_BTN_Lif5;
void ui_event_BTN_Rig5(lv_event_t * e);
lv_obj_t * ui_BTN_Rig5;
lv_obj_t * ui_Roller5;
lv_obj_t * ui_PL_AlarmRst_Ani;
lv_obj_t * ui_Label46;
lv_obj_t * ui_Label50;


// SCREEN: ui_SC_3_3
void ui_SC_3_3_screen_init(void);
void ui_event_SC_3_3(lv_event_t * e);
lv_obj_t * ui_SC_3_3;
lv_obj_t * ui_Image121;
lv_obj_t * ui_Image123;
lv_obj_t * ui_Image125;
lv_obj_t * ui_Image126;
lv_obj_t * ui_Image127;
lv_obj_t * ui_Image128;
lv_obj_t * ui_Image129;
lv_obj_t * ui_Image130;
lv_obj_t * ui_Panel7;
lv_obj_t * ui_Label32;
lv_obj_t * ui_Panel9;
lv_obj_t * ui_Label33;
lv_obj_t * ui_Panel10;
lv_obj_t * ui_Label34;
lv_obj_t * ui_Panel11;
lv_obj_t * ui_Label35;
lv_obj_t * ui_PL_X_Cab_info;
lv_obj_t * ui_LB_X_bias;
lv_obj_t * ui_PL_Y_Cab_info;
lv_obj_t * ui_LB_Y_bias;
lv_obj_t * ui_PL_Z_Cab_info;
lv_obj_t * ui_LB_Z_bias;
void ui_event_BTN_Cab(lv_event_t * e);
lv_obj_t * ui_BTN_Cab;
lv_obj_t * ui_LB_Cab;
lv_obj_t * ui_Image7;
lv_obj_t * ui_Image54;


// SCREEN: ui_SC_3_4
void ui_SC_3_4_screen_init(void);
void ui_event_SC_3_4(lv_event_t * e);
lv_obj_t * ui_SC_3_4;
lv_obj_t * ui_Image20;
lv_obj_t * ui_Image133;
lv_obj_t * ui_Image135;
lv_obj_t * ui_Image136;
lv_obj_t * ui_Image137;
lv_obj_t * ui_Image138;
lv_obj_t * ui_Image139;
lv_obj_t * ui_Image140;
lv_obj_t * ui_Image200;
lv_obj_t * ui_Image201;
lv_obj_t * ui_BTN_EvnClear;
lv_obj_t * ui_LB_EnvClear;
lv_obj_t * ui_Table_Log;
lv_obj_t * ui_Table_Title;
lv_obj_t * ui_PL_log;
lv_obj_t * ui_Label43;
lv_obj_t * ui_Label44;
lv_obj_t * ui_Label45;
lv_obj_t * ui_Lab_Log_Bound;
lv_obj_t * ui_Lab_Log_Max;
lv_obj_t * ui_Label48;
lv_obj_t * ui_Lab_Log_Date;
lv_obj_t * ui_Lab_Log_ID;
lv_obj_t * ui_Lab_log_Collision;
lv_obj_t * ui_Label_46;
lv_obj_t * ui_Lab_log_Min;
void ui_event_BTN_Log_Exit(lv_event_t * e);
lv_obj_t * ui_BTN_Log_Exit;
lv_obj_t * ui_Label47;
void ui_event_BNT_log_page_up(lv_event_t * e);
lv_obj_t * ui_BNT_log_page_up;
void ui_event_BNT_log_page_down(lv_event_t * e);
lv_obj_t * ui_BNT_log_page_down;
lv_obj_t * ui_Panel14;
void ui_event_BTN_EvnClear(lv_event_t * e);

// SCREEN: ui_SC_6_1
void ui_SC_6_1_screen_init(void);
void ui_event_SC_6_1(lv_event_t * e);
lv_obj_t * ui_SC_6_1;
lv_obj_t * ui_Image159;
lv_obj_t * ui_Image160;
lv_obj_t * ui_Image161;
lv_obj_t * ui_Image162;
lv_obj_t * ui_Image163;
lv_obj_t * ui_Label51;
lv_obj_t * ui_Panel22;
void ui_event_BTN_Set_Time(lv_event_t * e);
lv_obj_t * ui_BTN_Set_Time;
lv_obj_t * ui_LB_Set_Time;
void ui_event_BTN_Set_ModBusID(lv_event_t * e);
lv_obj_t * ui_BTN_Set_ModBusID;
lv_obj_t * ui_LB_Set_ModBusID;
void ui_event_BTN_Set_ModBus_Mode(lv_event_t * e);
lv_obj_t * ui_BTN_Set_ModBus_Mode;
lv_obj_t * ui_LB_Set_ModBus_Mode;
void ui_event_BTN_Set_PWD(lv_event_t * e);
lv_obj_t * ui_BTN_Set_PWD;
lv_obj_t * ui_LB_Set_PWD;
lv_obj_t * ui_Panel25;
lv_obj_t * ui_Admin_PWD;
void ui_event_BTN_Lif3(lv_event_t * e);
lv_obj_t * ui_BTN_Lif3;
lv_obj_t * ui_Roller3;
void ui_event_BTN_Rig3(lv_event_t * e);
lv_obj_t * ui_BTN_Rig3;
lv_obj_t * ui_Image147;
lv_obj_t * ui_Image148;


// SCREEN: ui_SC_set_ModBus_ID
void ui_SC_set_ModBus_ID_screen_init(void);
lv_obj_t * ui_SC_set_ModBus_ID;
lv_obj_t * ui_Label54;
lv_obj_t * ui_PL_set_date_and_time2;
lv_obj_t * ui_ModBusIDx100;
lv_obj_t * ui_ModBusIDx10;
lv_obj_t * ui_ModBusIDx1;
void ui_event_BTN_set_ModBus_OK(lv_event_t * e);
lv_obj_t * ui_BTN_set_ModBus_OK;
lv_obj_t * ui_Label55;
void ui_event_BTN_set_ID_cancel(lv_event_t * e);
lv_obj_t * ui_BTN_set_ID_cancel;
lv_obj_t * ui_Label65;


// SCREEN: ui_SC_set_PWD
void ui_SC_set_PWD_screen_init(void);
lv_obj_t * ui_SC_set_PWD;
lv_obj_t * ui_Label56;
lv_obj_t * ui_TextArea2;
lv_obj_t * ui_Roller2;
void ui_event_BTN_Rig2(lv_event_t * e);
lv_obj_t * ui_BTN_Rig2;
void ui_event_BTN_Lif2(lv_event_t * e);
lv_obj_t * ui_BTN_Lif2;
void ui_event_BTN_set_PWD_OK(lv_event_t * e);
lv_obj_t * ui_BTN_set_PWD_OK;
lv_obj_t * ui_Label58;
void ui_event_BTN_set_PWD_cancel(lv_event_t * e);
lv_obj_t * ui_BTN_set_PWD_cancel;
lv_obj_t * ui_Label64;


// SCREEN: ui_SC_1_2
void ui_SC_1_2_screen_init(void);
void ui_event_SC_1_2(lv_event_t * e);
lv_obj_t * ui_SC_1_2;
lv_obj_t * ui_Panel15;
lv_obj_t * ui_Label4;
lv_obj_t * ui_Panel16;
lv_obj_t * ui_Label52;
lv_obj_t * ui_PL_del_ID1;
void ui_event_PL_del_ID1_PL_Face_ID_0_Face_ID_0(lv_event_t * e);
void ui_event_PL_del_ID1_PL_Face_ID_0_Face_ID__LV_0(lv_event_t * e);
void ui_event_PL_del_ID1_PL_Face_ID_1_Face_ID_1(lv_event_t * e);
void ui_event_PL_del_ID1_PL_Face_ID_1_Face_ID__LV_1(lv_event_t * e);
void ui_event_PL_del_ID1_PL_Face_ID_2_Face_ID_2(lv_event_t * e);
void ui_event_PL_del_ID1_PL_Face_ID_2_Face_ID__LV_2(lv_event_t * e);
void ui_event_PL_del_ID1_PL_Face_ID_3_Face_ID_3(lv_event_t * e);
void ui_event_PL_del_ID1_PL_Face_ID_3_Face_ID__LV_3(lv_event_t * e);
void ui_event_PL_del_ID1_PL_Face_ID_4_Face_ID_4(lv_event_t * e);
void ui_event_PL_del_ID1_PL_Face_ID_4_Face_ID__LV_4(lv_event_t * e);
void ui_event_PL_del_ID1_PL_Face_ID_5_Face_ID_5(lv_event_t * e);
void ui_event_PL_del_ID1_PL_Face_ID_5_Face_ID__LV_5(lv_event_t * e);
void ui_event_PL_del_ID1_PL_Face_ID_6_Face_ID_6(lv_event_t * e);
void ui_event_PL_del_ID1_PL_Face_ID_6_Face_ID__LV_6(lv_event_t * e);

lv_obj_t * ui_LV_confirm2;
lv_obj_t * ui_Roller_LV2;
void ui_event_LV_Cofirm_OK2(lv_event_t * e);
lv_obj_t * ui_LV_Cofirm_OK2;
lv_obj_t * ui_Lab_LV_Con2;
lv_obj_t * ui_Image168;
lv_obj_t * ui_Image169;
lv_obj_t * ui_Image170;
lv_obj_t * ui_Image171;
lv_obj_t * ui_Image167;
lv_obj_t * ui_Image172;
lv_obj_t * ui_Image173;
lv_obj_t * ui_Image174;
lv_obj_t * ui_PL_Admin_PWD;
lv_obj_t * ui_Label66;
lv_obj_t * ui_TextArea_Login_PWD;
void ui_event_BTN_Lif6(lv_event_t * e);
lv_obj_t * ui_BTN_Lif6;
lv_obj_t * ui_Roller6;
void ui_event_BTN_Rig6(lv_event_t * e);
lv_obj_t * ui_BTN_Rig6;
void ui_event_BTN_login_PWD_OK2(lv_event_t * e);
lv_obj_t * ui_BTN_login_PWD_OK2;
lv_obj_t * ui_Label67;
void ui_event_BTN_set_PWD_cancel2(lv_event_t * e);
lv_obj_t * ui_BTN_set_PWD_cancel2;
lv_obj_t * ui_Label68;


// SCREEN: ui_SC_3_2
void ui_SC_3_2_screen_init(void);
void ui_event_SC_3_2(lv_event_t * e);
lv_obj_t * ui_SC_3_2;
lv_obj_t * ui_Image99;
lv_obj_t * ui_Image103;
lv_obj_t * ui_Image104;
lv_obj_t * ui_Image105;
lv_obj_t * ui_Image106;
lv_obj_t * ui_Image107;
lv_obj_t * ui_Image108;
lv_obj_t * ui_Image109;
lv_obj_t * ui_LAB_GC_ASIX1;
lv_obj_t * ui_Set_X_bound1;
lv_obj_t * ui_Panel2;
lv_obj_t * ui_Label18;
void ui_event_PL_X_Up_bound1(lv_event_t * e);
lv_obj_t * ui_PL_X_Up_bound1;
lv_obj_t * ui_X_UP_bon_Value1;
lv_obj_t * ui_Panel3;
lv_obj_t * ui_Label22;
void ui_event_PL_X_low_bound1(lv_event_t * e);
lv_obj_t * ui_PL_X_low_bound1;
lv_obj_t * ui_X_low_bon_Value1;
lv_obj_t * ui_Panel4;
lv_obj_t * ui_Label23;
lv_obj_t * ui_Panel_1;
lv_obj_t * ui_Label24;
lv_obj_t * ui_PL_X_Value_OK1;
lv_obj_t * ui_Panel5;
lv_obj_t * ui_Set_X_bon_x2;
lv_obj_t * ui_Set_X_bon_x3;
lv_obj_t * ui_Set_X_bon_x4;
lv_obj_t * ui_Set_X_bon_x5;
void ui_event_BTN_X_bon_Value_OK1(lv_event_t * e);
lv_obj_t * ui_BTN_X_bon_Value_OK1;
lv_obj_t * ui_LB_Cab3;
lv_obj_t * ui_Image110;
lv_obj_t * ui_Image111;
void ui_event_BTN_GC_L1(lv_event_t * e);
lv_obj_t * ui_BTN_GC_L1;
void ui_event_BTN_GC_R1(lv_event_t * e);
lv_obj_t * ui_BTN_GC_R1;


// SCREEN: ui_SC_4_1
void ui_SC_4_1_screen_init(void);
void ui_event_SC_4_1(lv_event_t * e);
lv_obj_t * ui_SC_4_1;
lv_obj_t * ui_Image101;
lv_obj_t * ui_Image102;
lv_obj_t * ui_Image114;
lv_obj_t * ui_Image115;
lv_obj_t * ui_Image116;
lv_obj_t * ui_Image117;
lv_obj_t * ui_Image122;
lv_obj_t * ui_Image124;
lv_obj_t * ui_Panel6;
lv_obj_t * ui_Label25;
lv_obj_t * ui_Label29;
lv_obj_t * ui_LBS_TOOL_SET;
lv_obj_t * ui_Panel8;
lv_obj_t * ui_RO_TOOL_SET1_X10000;
lv_obj_t * ui_RO_TOOL_SET1_X1000;
lv_obj_t * ui_RO_TOOL_SET1_X100;
lv_obj_t * ui_RO_TOOL_SET1_X10;
lv_obj_t * ui_RO_TOOL_SET1_X1;
lv_obj_t * ui_Label30;
lv_obj_t * ui_LAB_TOOL_NO;
lv_obj_t * ui_BTN_TOOL_SET1;
lv_obj_t * ui_Label31;


// SCREEN: ui_SC_4_2
void ui_SC_4_2_screen_init(void);
void ui_event_SC_4_2(lv_event_t * e);
lv_obj_t * ui_SC_4_2;
lv_obj_t * ui_Image98;
lv_obj_t * ui_Image100;
lv_obj_t * ui_Image112;
lv_obj_t * ui_Image113;
lv_obj_t * ui_Image118;
lv_obj_t * ui_Image119;
lv_obj_t * ui_Image120;
lv_obj_t * ui_Image131;
lv_obj_t * ui_Panel12;
lv_obj_t * ui_Label36;
lv_obj_t * ui_Label37;
lv_obj_t * ui_LBS_TOOL_SET1;
lv_obj_t * ui_Panel13;
lv_obj_t * ui_RO_TOOL_SET2_X10000;
lv_obj_t * ui_RO_TOOL_SET2_X1000;
lv_obj_t * ui_RO_TOOL_SET2_X100;
lv_obj_t * ui_RO_TOOL_SET2_X10;
lv_obj_t * ui_RO_TOOL_SET2_X1;
lv_obj_t * ui_BTN_TOOL_SET2;
lv_obj_t * ui_Label39;


// SCREEN: ui_SC_5_1
void ui_SC_5_1_screen_init(void);
void ui_event_SC_5_1(lv_event_t * e);
lv_obj_t * ui_SC_5_1;
lv_obj_t * ui_Image134;
lv_obj_t * ui_Image141;
lv_obj_t * ui_Image142;
lv_obj_t * ui_Image143;
lv_obj_t * ui_Image144;
lv_obj_t * ui_Image145;
lv_obj_t * ui_Image146;
lv_obj_t * ui_Label38;
lv_obj_t * ui_Image132;
lv_obj_t * ui_Label40;
lv_obj_t * ui_LAB_exp_date;
lv_obj_t * ui_Image149;
lv_obj_t * ui_Image150;
lv_obj_t * ui_img_lic_ok;
lv_obj_t * ui_img_lic_exp;
void ui_event_BTN_Lic_renew(lv_event_t * e);
lv_obj_t * ui_BTN_Lic_renew;
lv_obj_t * ui_Label74;
lv_obj_t * ui_img_dongle_err;


// SCREEN: ui_SC_ID_EDIT
void ui_SC_ID_EDIT_screen_init(void);
lv_obj_t * ui_SC_ID_EDIT;
lv_obj_t * ui_Label59;
lv_obj_t * ui_Label53;
lv_obj_t * ui_Keyboard1;
lv_obj_t * ui_TA_ID_Name;
lv_obj_t * ui_TA_LV;

void ui_event_BTN_edit_save(lv_event_t * e);
lv_obj_t * ui_BTN_edit_save;
lv_obj_t * ui_Label61;
void ui_event_BTN_exit3(lv_event_t * e);
lv_obj_t * ui_BTN_exit3;
lv_obj_t * ui_Label62;
void ui_event_BTN_edit_NL(lv_event_t * e);
lv_obj_t * ui_BTN_edit_NL;
lv_obj_t * ui_Label73;
lv_obj_t * ui_Select_Edit_NoL;
void ui_event_BTN_Edit_Name(lv_event_t * e);
lv_obj_t * ui_BTN_Edit_Name;
lv_obj_t * ui_Label76;
void ui_event_BTN_Edit_LV(lv_event_t * e);
lv_obj_t * ui_BTN_Edit_LV;
lv_obj_t * ui_Label77;
lv_obj_t * ui_Label78;
// CUSTOM VARIABLES


// SCREEN: ui_SC_Face_Login
void ui_SC_Face_Login_screen_init(void);
void ui_event_SC_Face_Login(lv_event_t * e);
lv_obj_t * ui_SC_Face_Login;
lv_obj_t * ui_camera_img2;
void ui_event_BTN_exit2(lv_event_t * e);
lv_obj_t * ui_BTN_exit2;
lv_obj_t * ui_Label63;
lv_obj_t * ui_Image13;


// SCREEN: ui_SC_Key_input
void ui_SC_Key_input_screen_init(void);
lv_obj_t * ui_SC_Key_input;
lv_obj_t * ui_Label68;
lv_obj_t * ui_Keyboard2;
lv_obj_t * ui_TA_Lic_code;
void ui_event_BTN_Lic_save(lv_event_t * e);
lv_obj_t * ui_BTN_Lic_save;
lv_obj_t * ui_Label71;
void ui_event_BTN_exit4(lv_event_t * e);
lv_obj_t * ui_BTN_exit4;
lv_obj_t * ui_Label72;
lv_obj_t * ui_Label70;
lv_obj_t * ui____initial_actions0;
const lv_img_dsc_t * ui_imgset_b01_[1] = {&ui_img_b01_20_png};
const lv_img_dsc_t * ui_imgset_b02_[1] = {&ui_img_b02_10_png};
const lv_img_dsc_t * ui_imgset_b03_[1] = {&ui_img_b03_30_png};
const lv_img_dsc_t * ui_imgset_b04_[1] = {&ui_img_b04_30_png};
const lv_img_dsc_t * ui_imgset_b05_[1] = {&ui_img_b05_30_png};
const lv_img_dsc_t * ui_imgset_b06_[1] = {&ui_img_b06_30_png};
const lv_img_dsc_t * ui_imgset_b07_[1] = {&ui_img_b07_30_png};
const lv_img_dsc_t * ui_imgset_b10_[1] = {&ui_img_b10_14050_png};
const lv_img_dsc_t * ui_imgset_bu[1] = {&ui_img_bu25_png};
const lv_img_dsc_t * ui_imgset_i21_[1] = {&ui_img_i21_90_png};
const lv_img_dsc_t * ui_imgset_i17_[1] = {&ui_img_i17_90_png};
const lv_img_dsc_t * ui_imgset_i[1] = {&ui_img_i19_png};


const lv_img_dsc_t * ui_imgset_lift_arrow_[1] = {&ui_img_lift_arrow_1_png};
const lv_img_dsc_t * ui_imgset_load_[3] = {&ui_img_load_110_png, &ui_img_load_44_png, &ui_img_load_80_png};
const lv_img_dsc_t * ui_imgset_p[4] = {&ui_img_p1_png, &ui_img_p2_png, &ui_img_p3_png, &ui_img_p4_png};
const lv_img_dsc_t * ui_imgset_right_arrow_[1] = {&ui_img_right_arrow_1_png};
const lv_img_dsc_t * ui_imgset_rpm_[3] = {&ui_img_rpm_110_png, &ui_img_rpm_44_png, &ui_img_rpm_80_png};
const lv_img_dsc_t * ui_imgset_tool1_[3] = {&ui_img_tool1_110_png, &ui_img_tool1_44_png, &ui_img_tool1_80_png};
const lv_img_dsc_t * ui_imgset_tool2_[1] = {&ui_img_tool2_80_png};
const lv_img_dsc_t * ui_imgset_user_[1] = {&ui_img_user_140_png};

const lv_img_dsc_t * ui_imgset_i19_[1] = {&ui_img_i19_250120_png};

lv_img_dsc_t * ui_img_background;

///////////////////// TEST LVGL SETTINGS ////////////////////
#if LV_COLOR_DEPTH != 16
    #error "LV_COLOR_DEPTH should be 16bit to match SquareLine Studio's settings"
#endif
#if LV_COLOR_16_SWAP !=1
    #error "LV_COLOR_16_SWAP should be 1 to match SquareLine Studio's settings"
#endif

///////////////////// ANIMATIONS ////////////////////
void go_screen(int screen,int move,int time){
	// Allow longer delays for boot screen timing
	// For normal operation, keep delays short for stability
	if (time > 5000) {
		time = 100;  // Limit non-boot screens to 100ms delay
	}
	cur_screen = screen;
		switch(screen){
		case 11:
			_ui_screen_change(&ui_SC_1_1, move, 100, time, &ui_SC_1_1_screen_init);
			break;
		case 12:
			_ui_screen_change(&ui_SC_1_2, move, 100, time, &ui_SC_1_2_screen_init);
			break;
    	case 21:
    		_ui_screen_change(&ui_SC_2_1, move, 100, time, &ui_SC_2_1_screen_init);
    		break;
    	case 22:
    		_ui_screen_change(&ui_SC_2_2, move, 100, time, &ui_SC_2_2_screen_init);
    		break;
    	case 23:
    		_ui_screen_change(&ui_SC_2_3, move, 100, time, &ui_SC_2_3_screen_init);
    		break;
    	case 24:
    		_ui_screen_change(&ui_SC_2_4, move, 100, time, &ui_SC_2_4_screen_init);
    		break;
    	case 25:
    		_ui_screen_change(&ui_SC_2_5, move, 100, time, &ui_SC_2_5_screen_init);
    		cur_screen = 25;
    		break;
    	case 26:
    		_ui_screen_change(&ui_SC_2_6, move, 100, time, &ui_SC_2_6_screen_init);
    		cur_screen = 26;
    		break;
    	case 31:
    		_ui_screen_change(&ui_SC_3_1, move, 100, time, &ui_SC_3_1_screen_init);
    		cur_screen = 31;
    		break;
    	case 32:
    		_ui_screen_change(&ui_SC_3_2, move, 100, time, &ui_SC_3_2_screen_init);
    		cur_screen = 320;
    		break;
    	case 33:
    		_ui_screen_change(&ui_SC_3_3, move, 100, time, &ui_SC_3_3_screen_init);
    		cur_screen = 33;
    		break;
    	case 34:
    		_ui_screen_change(&ui_SC_3_4, move, 100, time, &ui_SC_3_4_screen_init);
    		cur_screen = 34;
    		break;
    	case 41:
    		cur_screen = 41;
    		_ui_screen_change(&ui_SC_4_1, move, 100, time, &ui_SC_4_1_screen_init);
    		break;
    	case 42:
    		cur_screen = 42;
    		_ui_screen_change(&ui_SC_4_2, move, 100, time, &ui_SC_4_2_screen_init);
    		break;
    	case 51:
    		cur_screen = 51;
    		_ui_screen_change(&ui_SC_5_1, move, 100, time, &ui_SC_5_1_screen_init);
    		break;
		case 61:
			cur_screen = 61;
			_ui_screen_change(&ui_SC_6_1, move, 100, time, &ui_SC_6_1_screen_init);
			break;
		case 70:
			cur_screen = 70;
			_ui_screen_change(&ui_SC_Face_Login, move, 100, time, &ui_SC_Face_Login_screen_init);
			break;
		case 71:
			cur_screen = 71;
			_ui_screen_change(&ui_SC_Face_Rec, move, 100, time, &ui_SC_Face_Rec_screen_init);
			break;

		default:
			//cur_screen = 61;
			//_ui_screen_change(&ui_SC_6_1, move, 400, time, &ui_SC_6_1_screen_init);
			break;

		}
}
void search_down(int i,int little, bool loop){

	screen_found=-1;
	while(1){
		if( (theme[i-1]&0x1) > 0 && little<=1 ) screen_found=i*10+1;
		else if( (theme[i-1]&0x2) > 0 && little<=2) screen_found=i*10+2;
		else if( (theme[i-1]&0x4) > 0 && little<=3) screen_found=i*10+3;
		else if( (theme[i-1]&0x8) > 0 && little<=4) screen_found=i*10+4;
		else if( (theme[i-1]&0x10) > 0 && little<=5) screen_found=i*10+5;
		else if( (theme[i-1]&0x20) > 0 && little<=6) screen_found=i*10+6;
		else if( (theme[i-1]&0x40) > 0 && little<=7) screen_found=i*10+7;
		else if( (theme[i-1]&0x80) > 0 && little<=8) screen_found=i*10+8;
		else screen_found=-1;

		if(loop==false || screen_found!=-1)
			break;
		else{
			loop=false;
			little=0;
		}
	}

}

void search_up(int i,int little, bool loop){

	screen_found=-1;
	while(1){
		if( (theme[i-1]&0x80) > 0 && little>=8) screen_found=i*10+8;
		else if( (theme[i-1]&0x40) > 0 && little>=7) screen_found=i*10+7;
		else if( (theme[i-1]&0x20) > 0 && little>=6) screen_found=i*10+6;
		else if( (theme[i-1]&0x10) > 0 && little>=5) screen_found=i*10+5;
		else if( (theme[i-1]&0x8) > 0 && little>=4) screen_found=i*10+4;
		else if( (theme[i-1]&0x4) > 0 && little>=3) screen_found=i*10+3;
		else if( (theme[i-1]&0x2) > 0 && little>=2) screen_found=i*10+2;
		else if( (theme[i-1]&0x1) > 0 && little>=1 ) screen_found=i*10+1;
		else screen_found=-1;

		if(loop==false || screen_found!=-1)
			break;
		else{
			loop=false;
			little=9;
		}
	}

}

void find_screen(int big,int little, int direction){

	if(direction){ //right
		if(theme[5]!=0  && big>=6 ){
			search_down(6,little, false);
		}
		else if(theme[4]!=0  && big>=5 ){
			search_down(5,little, false);
		}
		else if(theme[3]!=0 && big>=4 ){
			search_down(4,little, false);
		}
		else if(theme[2]!=0 && big>=3 ){
			search_down(3,little, false);
		}
		else if(theme[1]!=0 && big>=2 ){
			search_down(2,little, false);
		}
		else if(theme[0]!=0 && big>=1 ){
			search_down(1,little, false);
		}
		else
			screen_found = -1;
	}
	else{ //left
		if(theme[0]!=0 && big<=1 ){
			search_down(1,little, false);
		}
		else if(theme[1]!=0 && big<=2 ){
			search_down(2,little, false);
		}
		else if(theme[2]!=0 && big<=3 ){
			search_down(3,little, false);
		}
		else if(theme[3]!=0 && big<=4 ){
			search_down(4,little, false);
		}
		else if(theme[4]!=0  && big<=5 ){
			search_down(5,little, false);
		}
		else if(theme[5]!=0  && big<=6 ){
			search_down(6,little, false);
		}
		else
			screen_found = -1;
	}

    //cur_screen = screen_found;
    return;
}
///////////////////// FUNCTIONS ////////////////////
// Forward declaration
static void boot_timer_callback(lv_timer_t * timer);

// Global variables for boot timing
static bool camera_init_started = false;
static bool camera_init_completed = false;
static uint32_t boot_start_time = 0;

void ui_event_SC_LOGO(lv_event_t * e)
{
    lv_event_code_t event_code = lv_event_get_code(e);
    lv_obj_t * target = lv_event_get_target(e);
    if(event_code == LV_EVENT_SCREEN_LOADED) { //aaron
		// Start camera initialization and boot timing
		boot_start_time = lv_tick_get();
		camera_init_started = true;
		camera_init_completed = false;

		// Trigger camera initialization in background
		// This will be handled by a timer that checks both conditions
		lv_timer_create(boot_timer_callback, 100, NULL);  // Check every 100ms
    }
}

// Boot timer callback to handle camera init + 5 second wait
static void boot_timer_callback(lv_timer_t * timer)
{
    uint32_t elapsed_time = lv_tick_get() - boot_start_time;
    bool five_seconds_passed = (elapsed_time >= 5000);

    // Trigger camera boot initialization sequence if not started
    if (!camera_init_completed) {
        static bool init_triggered = false;
        if (!init_triggered) {
            // Check if camera object is available before initializing
            extern bool initialize_camera_hardware(void);
            extern bool is_camera_object_available(void);

            if (is_camera_object_available()) {
                init_triggered = true;
                ESP_LOGI("BOOT", "Starting camera boot initialization sequence");

                // Step 1: Camera boot initialization (Reset sequence + PD Low)
                camera_power_boot_init();

                // Step 2: Initialize camera hardware
                if (initialize_camera_hardware()) {
                    camera_init_completed = true;
                    ESP_LOGI("BOOT", "Camera hardware initialization completed");
                } else {
                    ESP_LOGE("BOOT", "Camera hardware initialization failed");
                    // Try again next time
                    init_triggered = false;
                }
            } else {
                // Camera object not ready yet, wait
                ESP_LOGD("BOOT", "Waiting for camera object to be created...");
            }
        }
    }

    // Proceed to next screen when both conditions are met
    if (five_seconds_passed && camera_init_completed) {
        // Stop the timer
        lv_timer_del(timer);

        // Put camera to sleep after initialization
        camera_power_off();

        // Proceed to next screen
        if( valid_users == 999 ){  //valid_users == 0
            cur_screen = 10;
            _ui_screen_change(&ui_SC_first_login, LV_SCR_LOAD_ANIM_MOVE_LEFT, 100, 0, &ui_SC_first_login_screen_init);
        }
        else{
            go_screen(default_screen, LV_SCR_LOAD_ANIM_MOVE_LEFT, 0);
        }
    }
}
void ui_event_BTN_Rig1(lv_event_t * e)  //ui_SC_first_login
{
    lv_event_code_t event_code = lv_event_get_code(e);
    lv_obj_t * target = lv_event_get_target(e);
    if(event_code == LV_EVENT_CLICKED) {
        //_ui_flag_modify(ui_Roller1, LV_OBJ_FLAG_HIDDEN, _UI_MODIFY_FLAG_TOGGLE);
    	lv_roller_get_selected_str(ui_Roller1, pw, 2);
    	lv_textarea_add_char(ui_TextArea1, *pw );
    	strcpy(pw,lv_textarea_get_text(ui_TextArea1));
    }
}
void ui_event_BTN_Lif1(lv_event_t * e) //ui_SC_first_login
{
    lv_event_code_t event_code = lv_event_get_code(e);
    lv_obj_t * target = lv_event_get_target(e);
    if(event_code == LV_EVENT_CLICKED) {
    	//lv_textarea_add_text(ui_TextArea1, "d");
    	lv_textarea_del_char(ui_TextArea1);
    }
}

void ui_event_BTN_set_time_conform(lv_event_t * e)
{
    lv_event_code_t event_code = lv_event_get_code(e);
    lv_obj_t * target = lv_event_get_target(e);
    if(event_code == LV_EVENT_CLICKED) {
    	time2.tm_year = lv_roller_get_selected(ui_Roller_set_Year)+124;  //+1900
    	time2.tm_mon = lv_roller_get_selected(ui_Roller_set_month)+1;  //+1
    	time2.tm_mday = lv_roller_get_selected(ui_Roller_set_Day)+1; // +1
    	time2.tm_hour = lv_roller_get_selected(ui_Roller_set_Hour);
    	time2.tm_min = lv_roller_get_selected(ui_Roller_set_minute);
    	cur_screen = 61;
        _ui_screen_change(&ui_SC_6_1, LV_SCR_LOAD_ANIM_FADE_ON, 100, 0, &ui_SC_6_1_screen_init);
    }
}
void ui_event_BTN_set_time_cancel(lv_event_t * e)
{
    lv_event_code_t event_code = lv_event_get_code(e);
    lv_obj_t * target = lv_event_get_target(e);
    if(event_code == LV_EVENT_CLICKED) {
        _ui_screen_change(&ui_SC_6_1, LV_SCR_LOAD_ANIM_FADE_ON, 100, 0, &ui_SC_6_1_screen_init);
    }
}
void ui_event_BTN_ModBus_Master(lv_event_t * e)
{
    lv_event_code_t event_code = lv_event_get_code(e);
    lv_obj_t * target = lv_event_get_target(e);
    if(event_code == LV_EVENT_CLICKED) {
    	modbus_mode=0;
    	cur_screen = 61;
        _ui_screen_change(&ui_SC_6_1, LV_SCR_LOAD_ANIM_FADE_ON, 100, 400, &ui_SC_6_1_screen_init);
    }
}
void ui_event_BTN_ModBus_Slave(lv_event_t * e)
{
    lv_event_code_t event_code = lv_event_get_code(e);
    lv_obj_t * target = lv_event_get_target(e);
    if(event_code == LV_EVENT_CLICKED) {
		modbus_mode=1;
    	cur_screen = 61;
        _ui_screen_change(&ui_SC_6_1, LV_SCR_LOAD_ANIM_FADE_ON, 100, 400, &ui_SC_6_1_screen_init);
    }
}
void ui_event_SC_Face_Rec(lv_event_t * e)
{
    lv_event_code_t event_code = lv_event_get_code(e);
    lv_obj_t * target = lv_event_get_target(e);
    if(event_code == LV_EVENT_SCREEN_LOADED) {
        _ui_flag_modify(ui_Image1, LV_OBJ_FLAG_HIDDEN, _UI_MODIFY_FLAG_ADD);
    }
}
void ui_event_BTN_exit1(lv_event_t * e)
{
    lv_event_code_t event_code = lv_event_get_code(e);

    if(event_code == LV_EVENT_CLICKED) {
    	if(cur_screen == 70){
    		cur_screen = 11;
    		_ui_screen_change(&ui_SC_1_1, LV_SCR_LOAD_ANIM_FADE_ON, 100, 400, &ui_SC_1_1_screen_init);
    	}
    	else{
    		cur_screen = 12;
    		_ui_screen_change(&ui_SC_1_2, LV_SCR_LOAD_ANIM_FADE_ON, 100, 400, &ui_SC_1_2_screen_init);
    	}
    }
}
void ui_event_BTN_Enroll(lv_event_t * e)
{
    lv_event_code_t event_code = lv_event_get_code(e);
    lv_obj_t * target = lv_event_get_target(e);
    if(event_code == LV_EVENT_CLICKED) {
    	//if(login_user==99){
    		cur_screen = 72;
    		//lv_obj_set_style_bg_color(ui_BTN_exit1, lv_color_hex(0xAF40E1), LV_PART_MAIN | LV_STATE_DEFAULT);
    		_ui_screen_change(&ui_SC_Face_Rec, LV_SCR_LOAD_ANIM_FADE_ON, 100, 0, &ui_SC_Face_Rec_screen_init);
    	//}
    	//else{
        //	cur_screen = 1;
        //    _ui_screen_change(&ui_SC_LOGO, LV_SCR_LOAD_ANIM_FADE_ON, 800, 0, &ui_SC_LOGO_screen_init);
    	//}
    }
}
void ui_event_SC_1_1(lv_event_t * e)
{
    lv_event_code_t event_code = lv_event_get_code(e);

    if(event_code == LV_EVENT_GESTURE &&  lv_indev_get_gesture_dir(lv_indev_get_act()) == LV_DIR_LEFT) {
    	lv_indev_wait_release(lv_indev_get_act());
    	find_screen(2,1,LEFT);  //LEFT 0 RIGHT 1 TOP 2 BOTTOM 3
    	//go_screen(21,LV_SCR_LOAD_ANIM_MOVE_LEFT,10);
    	go_screen(screen_found,LV_SCR_LOAD_ANIM_MOVE_LEFT,10);

    }
    if(event_code == LV_EVENT_GESTURE &&  lv_indev_get_gesture_dir(lv_indev_get_act()) == LV_DIR_TOP) {
        lv_indev_wait_release(lv_indev_get_act());
        search_down(1,2, true);
        //go_screen(12,LV_SCR_LOAD_ANIM_MOVE_TOP,10);
        go_screen(screen_found,LV_SCR_LOAD_ANIM_MOVE_TOP,10);
    }
    if(event_code == LV_EVENT_GESTURE &&  lv_indev_get_gesture_dir(lv_indev_get_act()) == LV_DIR_BOTTOM) {
        lv_indev_wait_release(lv_indev_get_act());
        search_up(1,2, true);
        //go_screen(12,LV_SCR_LOAD_ANIM_MOVE_BOTTOM,10);
        go_screen(screen_found,LV_SCR_LOAD_ANIM_MOVE_BOTTOM,10);
    }
}
void ui_event_BTN_login(lv_event_t * e)
{
    lv_event_code_t event_code = lv_event_get_code(e);

    if(event_code == LV_EVENT_CLICKED) {
    	if(login_user==99){
    		cur_screen = 70;
    		//lv_obj_set_style_bg_color(ui_BTN_exit1, lv_color_hex(0xAF40E1), LV_PART_MAIN | LV_STATE_DEFAULT);
    		//_ui_screen_change(&ui_SC_Face_Rec, LV_SCR_LOAD_ANIM_FADE_ON, 1500, 0, &ui_SC_Face_Rec_screen_init);
    		_ui_screen_change(&ui_SC_Face_Login, LV_SCR_LOAD_ANIM_FADE_ON, 100, 0, &ui_SC_Face_Login_screen_init);
    	}
    	else{
        	cur_screen = 1;
            _ui_screen_change(&ui_SC_LOGO, LV_SCR_LOAD_ANIM_FADE_ON, 100, 50, &ui_SC_LOGO_screen_init);
            lv_label_set_text(ui_Label3, "Login");
			lv_obj_set_style_bg_color(ui_BTN_login, lv_color_hex(0x42DD5D), LV_PART_MAIN | LV_STATE_DEFAULT);
			lv_obj_set_style_bg_opa(ui_BTN_login, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
			lv_obj_set_style_bg_grad_color(ui_BTN_login, lv_color_hex(0x92F25C), LV_PART_MAIN | LV_STATE_DEFAULT);
			lv_obj_set_style_bg_main_stop(ui_BTN_login, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
			lv_obj_set_style_bg_grad_stop(ui_BTN_login, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
			lv_obj_set_style_bg_grad_dir(ui_BTN_login, LV_GRAD_DIR_HOR, LV_PART_MAIN | LV_STATE_DEFAULT);

			login_user=99;
			cur_user=0;
    	}
    }
}
void ui_event_SC_2_1(lv_event_t * e)
{
    lv_event_code_t event_code = lv_event_get_code(e);

    if(event_code == LV_EVENT_GESTURE &&  lv_indev_get_gesture_dir(lv_indev_get_act()) == LV_DIR_RIGHT) {
        lv_indev_wait_release(lv_indev_get_act());
        find_screen(1,1,RIGHT);  //LEFT 0 RIGHT 1 TOP 2 BOTTOM 3
        go_screen(screen_found,LV_SCR_LOAD_ANIM_MOVE_RIGHT,10);
    }
    if(event_code == LV_EVENT_GESTURE &&  lv_indev_get_gesture_dir(lv_indev_get_act()) == LV_DIR_TOP) {
        lv_indev_wait_release(lv_indev_get_act());
        search_down(2,2, true);
        go_screen(screen_found,LV_SCR_LOAD_ANIM_MOVE_TOP,10);
    }
    if(event_code == LV_EVENT_GESTURE &&  lv_indev_get_gesture_dir(lv_indev_get_act()) == LV_DIR_BOTTOM) {
        lv_indev_wait_release(lv_indev_get_act());
        search_up(2,6, true);
        go_screen(screen_found,LV_SCR_LOAD_ANIM_MOVE_BOTTOM,10);
    }
    if(event_code == LV_EVENT_GESTURE &&  lv_indev_get_gesture_dir(lv_indev_get_act()) == LV_DIR_LEFT) {
        lv_indev_wait_release(lv_indev_get_act());
    	find_screen(3,1,LEFT);  //LEFT 0 RIGHT 1 TOP 2 BOTTOM 3
    	go_screen(screen_found,LV_SCR_LOAD_ANIM_MOVE_LEFT,10);
    }
}
void ui_event_SC_2_2(lv_event_t * e)
{
    lv_event_code_t event_code = lv_event_get_code(e);

    if(event_code == LV_EVENT_GESTURE &&  lv_indev_get_gesture_dir(lv_indev_get_act()) == LV_DIR_RIGHT) {
        lv_indev_wait_release(lv_indev_get_act());
        find_screen(1,1,RIGHT);  //LEFT 0 RIGHT 1 TOP 2 BOTTOM 3
        go_screen(screen_found,LV_SCR_LOAD_ANIM_MOVE_RIGHT,10);
    }
    if(event_code == LV_EVENT_GESTURE &&  lv_indev_get_gesture_dir(lv_indev_get_act()) == LV_DIR_BOTTOM) {
        lv_indev_wait_release(lv_indev_get_act());
        search_up(2,1, true);
        go_screen(screen_found,LV_SCR_LOAD_ANIM_MOVE_BOTTOM,10);
    }
    if(event_code == LV_EVENT_GESTURE &&  lv_indev_get_gesture_dir(lv_indev_get_act()) == LV_DIR_TOP) {
        lv_indev_wait_release(lv_indev_get_act());
        search_down(2,3, true);
        go_screen(screen_found,LV_SCR_LOAD_ANIM_MOVE_TOP,10);
    }
    if(event_code == LV_EVENT_GESTURE &&  lv_indev_get_gesture_dir(lv_indev_get_act()) == LV_DIR_LEFT) {
        lv_indev_wait_release(lv_indev_get_act());
    	find_screen(3,1,LEFT);  //LEFT 0 RIGHT 1 TOP 2 BOTTOM 3
    	go_screen(screen_found,LV_SCR_LOAD_ANIM_MOVE_LEFT,10);
    }
}
void ui_event_SC_2_3(lv_event_t * e)
{
    lv_event_code_t event_code = lv_event_get_code(e);

    if(event_code == LV_EVENT_GESTURE &&  lv_indev_get_gesture_dir(lv_indev_get_act()) == LV_DIR_RIGHT) {
        lv_indev_wait_release(lv_indev_get_act());
        find_screen(1,1,RIGHT);  //LEFT 0 RIGHT 1 TOP 2 BOTTOM 3
        go_screen(screen_found,LV_SCR_LOAD_ANIM_MOVE_RIGHT,10);
    }
    if(event_code == LV_EVENT_GESTURE &&  lv_indev_get_gesture_dir(lv_indev_get_act()) == LV_DIR_BOTTOM) {
        lv_indev_wait_release(lv_indev_get_act());
        search_up(2,2, true);
        go_screen(screen_found,LV_SCR_LOAD_ANIM_MOVE_BOTTOM,10);
    }
    if(event_code == LV_EVENT_GESTURE &&  lv_indev_get_gesture_dir(lv_indev_get_act()) == LV_DIR_TOP) {
        lv_indev_wait_release(lv_indev_get_act());
        search_down(2,4, true);
        go_screen(screen_found,LV_SCR_LOAD_ANIM_MOVE_TOP,10);
    }
    if(event_code == LV_EVENT_GESTURE &&  lv_indev_get_gesture_dir(lv_indev_get_act()) == LV_DIR_LEFT) {
        lv_indev_wait_release(lv_indev_get_act());
    	find_screen(3,1,LEFT);  //LEFT 0 RIGHT 1 TOP 2 BOTTOM 3
    	go_screen(screen_found,LV_SCR_LOAD_ANIM_MOVE_LEFT,10);
    }
}
void ui_event_SC_2_4(lv_event_t * e)
{
    lv_event_code_t event_code = lv_event_get_code(e);

    if(event_code == LV_EVENT_GESTURE &&  lv_indev_get_gesture_dir(lv_indev_get_act()) == LV_DIR_RIGHT) {
        lv_indev_wait_release(lv_indev_get_act());
        find_screen(1,1,RIGHT);  //LEFT 0 RIGHT 1 TOP 2 BOTTOM 3
        go_screen(screen_found,LV_SCR_LOAD_ANIM_MOVE_RIGHT,10);
    }
    if(event_code == LV_EVENT_GESTURE &&  lv_indev_get_gesture_dir(lv_indev_get_act()) == LV_DIR_BOTTOM) {
        lv_indev_wait_release(lv_indev_get_act());
        search_up(2,3, true);
        go_screen(screen_found,LV_SCR_LOAD_ANIM_MOVE_BOTTOM,10);
    }
    if(event_code == LV_EVENT_GESTURE &&  lv_indev_get_gesture_dir(lv_indev_get_act()) == LV_DIR_TOP) {
        lv_indev_wait_release(lv_indev_get_act());
        search_down(2,5, true);
        go_screen(screen_found,LV_SCR_LOAD_ANIM_MOVE_TOP,10);
    }
    if(event_code == LV_EVENT_GESTURE &&  lv_indev_get_gesture_dir(lv_indev_get_act()) == LV_DIR_LEFT) {
        lv_indev_wait_release(lv_indev_get_act());
    	find_screen(3,1,LEFT);  //LEFT 0 RIGHT 1 TOP 2 BOTTOM 3
    	go_screen(screen_found,LV_SCR_LOAD_ANIM_MOVE_LEFT,10);
    }
}
void ui_event_SC_2_5(lv_event_t * e)
{
    lv_event_code_t event_code = lv_event_get_code(e);

    if(event_code == LV_EVENT_GESTURE &&  lv_indev_get_gesture_dir(lv_indev_get_act()) == LV_DIR_RIGHT) {
        lv_indev_wait_release(lv_indev_get_act());
        find_screen(1,1,RIGHT);  //LEFT 0 RIGHT 1 TOP 2 BOTTOM 3
        go_screen(screen_found,LV_SCR_LOAD_ANIM_MOVE_RIGHT,10);
    }
    if(event_code == LV_EVENT_GESTURE &&  lv_indev_get_gesture_dir(lv_indev_get_act()) == LV_DIR_BOTTOM) {
        lv_indev_wait_release(lv_indev_get_act());
        search_up(2,4, true);
        go_screen(screen_found,LV_SCR_LOAD_ANIM_MOVE_BOTTOM,10);
    }
    if(event_code == LV_EVENT_GESTURE &&  lv_indev_get_gesture_dir(lv_indev_get_act()) == LV_DIR_TOP) {
        lv_indev_wait_release(lv_indev_get_act());
        search_down(2,6, true);
        go_screen(screen_found,LV_SCR_LOAD_ANIM_MOVE_TOP,10);
    }
    if(event_code == LV_EVENT_GESTURE &&  lv_indev_get_gesture_dir(lv_indev_get_act()) == LV_DIR_LEFT) {
        lv_indev_wait_release(lv_indev_get_act());
    	find_screen(3,1,LEFT);  //LEFT 0 RIGHT 1 TOP 2 BOTTOM 3
    	go_screen(screen_found,LV_SCR_LOAD_ANIM_MOVE_LEFT,10);
    }
}
void ui_event_SC_2_6(lv_event_t * e)
{
    lv_event_code_t event_code = lv_event_get_code(e);

    if(event_code == LV_EVENT_GESTURE &&  lv_indev_get_gesture_dir(lv_indev_get_act()) == LV_DIR_RIGHT) {
        lv_indev_wait_release(lv_indev_get_act());
        find_screen(1,1,RIGHT);  //LEFT 0 RIGHT 1 TOP 2 BOTTOM 3
        go_screen(screen_found,LV_SCR_LOAD_ANIM_MOVE_RIGHT,10);
    }
    if(event_code == LV_EVENT_GESTURE &&  lv_indev_get_gesture_dir(lv_indev_get_act()) == LV_DIR_BOTTOM) {
        lv_indev_wait_release(lv_indev_get_act());
        search_up(2,5, true);
        go_screen(screen_found,LV_SCR_LOAD_ANIM_MOVE_BOTTOM,10);
    }
    if(event_code == LV_EVENT_GESTURE &&  lv_indev_get_gesture_dir(lv_indev_get_act()) == LV_DIR_TOP) {
        lv_indev_wait_release(lv_indev_get_act());
        search_down(2,1, true);
        go_screen(screen_found,LV_SCR_LOAD_ANIM_MOVE_TOP,10);
    }
    if(event_code == LV_EVENT_GESTURE &&  lv_indev_get_gesture_dir(lv_indev_get_act()) == LV_DIR_LEFT) {
        lv_indev_wait_release(lv_indev_get_act());
    	find_screen(3,1,LEFT);  //LEFT 0 RIGHT 1 TOP 2 BOTTOM 3
    	go_screen(screen_found,LV_SCR_LOAD_ANIM_MOVE_LEFT,10);
    }
}
void ui_event_SC_3_1(lv_event_t * e)
{
    lv_event_code_t event_code = lv_event_get_code(e);

    if(cur_screen != 314){
    	if(event_code == LV_EVENT_GESTURE &&  lv_indev_get_gesture_dir(lv_indev_get_act()) == LV_DIR_RIGHT) {
    		lv_indev_wait_release(lv_indev_get_act());
            find_screen(2,1,RIGHT);  //LEFT 0 RIGHT 1 TOP 2 BOTTOM 3
            go_screen(screen_found,LV_SCR_LOAD_ANIM_MOVE_RIGHT,10);
    	}
    	if(event_code == LV_EVENT_GESTURE &&  lv_indev_get_gesture_dir(lv_indev_get_act()) == LV_DIR_BOTTOM) {
    		lv_indev_wait_release(lv_indev_get_act());
            search_up(3,4, true);
            go_screen(screen_found,LV_SCR_LOAD_ANIM_MOVE_BOTTOM,10);
    	}
    	if(event_code == LV_EVENT_GESTURE &&  lv_indev_get_gesture_dir(lv_indev_get_act()) == LV_DIR_TOP) {
    		lv_indev_wait_release(lv_indev_get_act());
            search_down(3,2, true);
            go_screen(screen_found,LV_SCR_LOAD_ANIM_MOVE_TOP,10);
    		//cur_screen = 320;
    	}
    	if(event_code == LV_EVENT_GESTURE &&  lv_indev_get_gesture_dir(lv_indev_get_act()) == LV_DIR_LEFT) {
    		lv_indev_wait_release(lv_indev_get_act());
        	find_screen(4,1,LEFT);  //LEFT 0 RIGHT 1 TOP 2 BOTTOM 3
        	go_screen(screen_found,LV_SCR_LOAD_ANIM_MOVE_LEFT,10);
    	}
    }
}
void ui_event_BTN_GC_L(lv_event_t * e)
{
    lv_event_code_t event_code = lv_event_get_code(e);

    if(event_code == LV_EVENT_CLICKED) {
    	if(cur_screen == 31){
    		cur_screen = 312;
    		lv_label_set_text(ui_LAB_GC_ASIX, "Z Asix");
    	}
    	else if(cur_screen == 311){
    		cur_screen = 31;
    		lv_label_set_text(ui_LAB_GC_ASIX, "X Asix");
    	}
    	else if(cur_screen == 312){
    		cur_screen = 311;
    		lv_label_set_text(ui_LAB_GC_ASIX, "Y Asix");
    	}
    }
}
void ui_event_BTN_GC_R(lv_event_t * e)
{
    lv_event_code_t event_code = lv_event_get_code(e);

    if(event_code == LV_EVENT_CLICKED) {
    	if(cur_screen == 31){
    		cur_screen = 311;
    		lv_label_set_text(ui_LAB_GC_ASIX, "Y Asix");
    	}
    	else if(cur_screen == 311){
    		cur_screen = 312;
    		lv_label_set_text(ui_LAB_GC_ASIX, "Z Asix");
    	}
    	else if(cur_screen == 312){
    		cur_screen = 31;
    		lv_label_set_text(ui_LAB_GC_ASIX, "X Asix");
    	}
    }
}
void ui_event_BTN_AlarmRst(lv_event_t * e)
{
    lv_event_code_t event_code = lv_event_get_code(e);

    if(event_code == LV_EVENT_CLICKED) {
    	cur_screen = 314;
        _ui_flag_modify(ui_PL_AlarmRst_PWD, LV_OBJ_FLAG_HIDDEN, _UI_MODIFY_FLAG_REMOVE);
    }
}
void ui_event_BTN_Lif5(lv_event_t * e)
{
    lv_event_code_t event_code = lv_event_get_code(e);

    if(event_code == LV_EVENT_CLICKED) {
    	//lv_textarea_add_text(ui_TextArea1, "d");
    	lv_textarea_del_char(ui_TextArea4);
    }
}
void ui_event_BTN_Rig5(lv_event_t * e)
{
    lv_event_code_t event_code = lv_event_get_code(e);

    if(event_code == LV_EVENT_CLICKED) {
    	lv_roller_get_selected_str(ui_Roller5, pw, 2);
    	lv_textarea_add_char(ui_TextArea4, *pw );
    	strcpy(pw,lv_textarea_get_text(ui_TextArea4));
    }
}

void ui_event_SC_3_3(lv_event_t * e)
{
    lv_event_code_t event_code = lv_event_get_code(e);

    if(event_code == LV_EVENT_GESTURE &&  lv_indev_get_gesture_dir(lv_indev_get_act()) == LV_DIR_RIGHT) {
        lv_indev_wait_release(lv_indev_get_act());
        find_screen(2,1,RIGHT);  //LEFT 0 RIGHT 1 TOP 2 BOTTOM 3
        go_screen(screen_found,LV_SCR_LOAD_ANIM_MOVE_RIGHT,10);
    }
    if(event_code == LV_EVENT_GESTURE &&  lv_indev_get_gesture_dir(lv_indev_get_act()) == LV_DIR_BOTTOM) {
        lv_indev_wait_release(lv_indev_get_act());
        search_up(3,2, true);
        go_screen(screen_found,LV_SCR_LOAD_ANIM_MOVE_BOTTOM,10);
        //cur_screen = 320;
    }
    if(event_code == LV_EVENT_GESTURE &&  lv_indev_get_gesture_dir(lv_indev_get_act()) == LV_DIR_TOP) {
        lv_indev_wait_release(lv_indev_get_act());
        search_down(3,4, true);
        go_screen(screen_found,LV_SCR_LOAD_ANIM_MOVE_TOP,10);
    }
    if(event_code == LV_EVENT_GESTURE &&  lv_indev_get_gesture_dir(lv_indev_get_act()) == LV_DIR_LEFT) {
        lv_indev_wait_release(lv_indev_get_act());
    	find_screen(4,1,LEFT);  //LEFT 0 RIGHT 1 TOP 2 BOTTOM 3
    	go_screen(screen_found,LV_SCR_LOAD_ANIM_MOVE_LEFT,10);
    }
}
void ui_event_BTN_Cab(lv_event_t * e)
{
    lv_event_code_t event_code = lv_event_get_code(e);
    lv_obj_t * target = lv_event_get_target(e);
    if(event_code == LV_EVENT_CLICKED) {
        cur_screen = 331;
    }
}
void ui_event_SC_3_4(lv_event_t * e)
{
    lv_event_code_t event_code = lv_event_get_code(e);

    if(event_code == LV_EVENT_GESTURE &&  lv_indev_get_gesture_dir(lv_indev_get_act()) == LV_DIR_RIGHT) {
        lv_indev_wait_release(lv_indev_get_act());
        find_screen(2,1,RIGHT);  //LEFT 0 RIGHT 1 TOP 2 BOTTOM 3
        go_screen(screen_found,LV_SCR_LOAD_ANIM_MOVE_RIGHT,10);
    }
    if(event_code == LV_EVENT_GESTURE &&  lv_indev_get_gesture_dir(lv_indev_get_act()) == LV_DIR_BOTTOM) {
        lv_indev_wait_release(lv_indev_get_act());
        search_up(3,3, true);
        go_screen(screen_found,LV_SCR_LOAD_ANIM_MOVE_BOTTOM,10);
    }
    if(event_code == LV_EVENT_GESTURE &&  lv_indev_get_gesture_dir(lv_indev_get_act()) == LV_DIR_TOP) {
        lv_indev_wait_release(lv_indev_get_act());
        search_down(3,1, true);
        go_screen(screen_found,LV_SCR_LOAD_ANIM_MOVE_TOP,10);
    }
    if(event_code == LV_EVENT_GESTURE &&  lv_indev_get_gesture_dir(lv_indev_get_act()) == LV_DIR_LEFT) {
        lv_indev_wait_release(lv_indev_get_act());
    	find_screen(4,1,LEFT);  //LEFT 0 RIGHT 1 TOP 2 BOTTOM 3
    	go_screen(screen_found,LV_SCR_LOAD_ANIM_MOVE_LEFT,10);    }
}
void ui_event_BTN_EvnClear(lv_event_t * e)
{
    lv_event_code_t event_code = lv_event_get_code(e);

    if(event_code == LV_EVENT_CLICKED) {
    	cur_screen = 341;
    }
}
void ui_event_BTN_Log_Exit(lv_event_t * e)
{
    lv_event_code_t event_code = lv_event_get_code(e);

    if(event_code == LV_EVENT_CLICKED) {
        _ui_flag_modify(ui_PL_log, LV_OBJ_FLAG_HIDDEN, _UI_MODIFY_FLAG_ADD);
    }
}
void ui_event_BNT_log_page_up(lv_event_t * e)
{
    lv_event_code_t event_code = lv_event_get_code(e);

    if(event_code == LV_EVENT_CLICKED) {
        _ui_state_modify(ui_BTN_Log_Exit, LV_STATE_DISABLED, _UI_MODIFY_STATE_TOGGLE);
    }
}
void ui_event_BNT_log_page_down(lv_event_t * e)
{
    lv_event_code_t event_code = lv_event_get_code(e);

    if(event_code == LV_EVENT_CLICKED) {
        _ui_state_modify(ui_BTN_Log_Exit, LV_STATE_DISABLED, _UI_MODIFY_STATE_TOGGLE);
    }
}
void ui_event_SC_6_1(lv_event_t * e)
{
    lv_event_code_t event_code = lv_event_get_code(e);

    if(event_code == LV_EVENT_GESTURE &&  lv_indev_get_gesture_dir(lv_indev_get_act()) == LV_DIR_RIGHT) {
        lv_indev_wait_release(lv_indev_get_act());
        find_screen(5,1,RIGHT);  //LEFT 0 RIGHT 1 TOP 2 BOTTOM 3
        go_screen(screen_found,LV_SCR_LOAD_ANIM_MOVE_RIGHT,10);
    }
}
void ui_event_BTN_Set_Time(lv_event_t * e)
{
    lv_event_code_t event_code = lv_event_get_code(e);
    if(event_code == LV_EVENT_CLICKED) {
    	cur_screen = 64;
        _ui_screen_change(&ui_SC_Time_set, LV_SCR_LOAD_ANIM_FADE_ON, 100, 0, &ui_SC_Time_set_screen_init);
    }
}
void ui_event_BTN_Set_ModBusID(lv_event_t * e)
{
    lv_event_code_t event_code = lv_event_get_code(e);

    if(event_code == LV_EVENT_CLICKED) {
    	cur_screen = 62;
        _ui_screen_change(&ui_SC_set_ModBus_ID, LV_SCR_LOAD_ANIM_FADE_ON, 100, 0, &ui_SC_set_ModBus_ID_screen_init);
    }
}
void ui_event_BTN_Set_ModBus_Mode(lv_event_t * e)
{
    lv_event_code_t event_code = lv_event_get_code(e);

    if(event_code == LV_EVENT_CLICKED) {
    	cur_screen = 65;
        _ui_screen_change(&ui_SC_ModBus_set, LV_SCR_LOAD_ANIM_FADE_ON, 100, 0, &ui_SC_ModBus_set_screen_init);
    }
}
void ui_event_BTN_Set_PWD(lv_event_t * e)
{
    lv_event_code_t event_code = lv_event_get_code(e);

    if(event_code == LV_EVENT_CLICKED) {
    	cur_screen = 63;
        _ui_screen_change(&ui_SC_set_PWD, LV_SCR_LOAD_ANIM_FADE_ON, 100, 0, &ui_SC_set_PWD_screen_init);
    }
}

void ui_event_BTN_Rig3(lv_event_t * e)
{
    lv_event_code_t event_code = lv_event_get_code(e);

    if(event_code == LV_EVENT_CLICKED) {
    	lv_roller_get_selected_str(ui_Roller3, pw, 2);
    	lv_textarea_add_char(ui_Admin_PWD, *pw );
    	strcpy(pw,lv_textarea_get_text(ui_Admin_PWD));
    }
}
void ui_event_BTN_Lif3(lv_event_t * e)
{
    lv_event_code_t event_code = lv_event_get_code(e);

    if(event_code == LV_EVENT_CLICKED) {
    	//lv_textarea_add_text(ui_TextArea1, "d");
    	lv_textarea_del_char(ui_Admin_PWD);
    }
}

void ui_event_BTN_set_ModBus_OK(lv_event_t * e)
{
    lv_event_code_t event_code = lv_event_get_code(e);

    if(event_code == LV_EVENT_CLICKED) {
    	mb_slave_id = lv_roller_get_selected(ui_ModBusIDx1);
    	mb_slave_id = 10*lv_roller_get_selected(ui_ModBusIDx10) + mb_slave_id;
    	mb_slave_id = 100*lv_roller_get_selected(ui_ModBusIDx100) + mb_slave_id;
    	if(mb_slave_id < 1) mb_slave_id=1;
    	if(mb_slave_id > 247) mb_slave_id=247;
    	cur_screen = 61;
        _ui_screen_change(&ui_SC_6_1, LV_SCR_LOAD_ANIM_FADE_ON, 100, 500, &ui_SC_6_1_screen_init);
    }
}
void ui_event_BTN_set_ID_cancel(lv_event_t * e)
{
    lv_event_code_t event_code = lv_event_get_code(e);
    lv_obj_t * target = lv_event_get_target(e);
    if(event_code == LV_EVENT_CLICKED) {
		cur_screen = 61;
        _ui_screen_change(&ui_SC_6_1, LV_SCR_LOAD_ANIM_FADE_ON, 100, 100, &ui_SC_6_1_screen_init);
    }
}
void ui_event_BTN_Rig2(lv_event_t * e)
{
    lv_event_code_t event_code = lv_event_get_code(e);

    if(event_code == LV_EVENT_CLICKED) {
    	lv_roller_get_selected_str(ui_Roller2, pw, 2);
    	lv_textarea_add_char(ui_TextArea2, *pw );
    	strcpy(pw,lv_textarea_get_text(ui_TextArea2));
    }
}
void ui_event_BTN_Lif2(lv_event_t * e)
{
    lv_event_code_t event_code = lv_event_get_code(e);

    if(event_code == LV_EVENT_CLICKED) {
    	lv_textarea_del_char(ui_TextArea2);
    }
}
void ui_event_BTN_set_PWD_OK(lv_event_t * e)
{
    lv_event_code_t event_code = lv_event_get_code(e);

    if(event_code == LV_EVENT_CLICKED) {
    	if( strlen(pw)==6 ){
        	cur_screen = 61;
            _ui_screen_change(&ui_SC_6_1, LV_SCR_LOAD_ANIM_FADE_ON, 100, 0, &ui_SC_6_1_screen_init);
    	}
    }
}
void ui_event_BTN_set_PWD_cancel(lv_event_t * e)
{
    lv_event_code_t event_code = lv_event_get_code(e);
    lv_obj_t * target = lv_event_get_target(e);
    if(event_code == LV_EVENT_CLICKED) {
		cur_screen = 61;
        _ui_screen_change(&ui_SC_6_1, LV_SCR_LOAD_ANIM_FADE_ON, 100, 0, &ui_SC_6_1_screen_init);
        strcpy(pw,"");
    }
}
void ui_event_SC_1_2(lv_event_t * e)
{
    lv_event_code_t event_code = lv_event_get_code(e);

    if(event_code == LV_EVENT_GESTURE &&  lv_indev_get_gesture_dir(lv_indev_get_act()) == LV_DIR_TOP) {
        lv_indev_wait_release(lv_indev_get_act());
        search_down(1,1, true);
        if(cur_screen != 71 && cur_screen != 77 && cur_screen != 127)
        	go_screen(screen_found,LV_SCR_LOAD_ANIM_MOVE_TOP,10);
    }
    if(event_code == LV_EVENT_GESTURE &&  lv_indev_get_gesture_dir(lv_indev_get_act()) == LV_DIR_BOTTOM) {
        lv_indev_wait_release(lv_indev_get_act());
        search_up(1,1, true);
        if(cur_screen != 71 && cur_screen != 77 && cur_screen != 127)
        	go_screen(screen_found,LV_SCR_LOAD_ANIM_MOVE_BOTTOM,10);
    }
    if(event_code == LV_EVENT_GESTURE &&  lv_indev_get_gesture_dir(lv_indev_get_act()) == LV_DIR_LEFT) {
        lv_indev_wait_release(lv_indev_get_act());
        find_screen(2,1,LEFT);  //LEFT 0 RIGHT 1 TOP 2 BOTTOM 3
        if(cur_screen != 71 && cur_screen != 77 && cur_screen != 127)
        	go_screen(screen_found,LV_SCR_LOAD_ANIM_MOVE_LEFT,10);
    }
}
void ui_event_PL_del_ID1_PL_Face_ID_0_Face_ID_0(lv_event_t * e)
{
    lv_event_code_t event_code = lv_event_get_code(e);

    if(event_code == LV_EVENT_CLICKED) {
    	cur_user = 0;
		if( user_table[0].valid !=1 ){
			cur_screen = 71;
			strcpy(pw,"");
			lv_obj_clear_flag(ui_PL_Admin_PWD, LV_OBJ_FLAG_HIDDEN);
			//_ui_screen_change(&ui_SC_Face_Rec, LV_SCR_LOAD_ANIM_FADE_ON, 1100, 300, &ui_SC_Face_Rec_screen_init);
		}
		else{
			//ESP_LOGI(TAG, "Updating NVS user 0 valid=%d lv=%d faceid=%d name=%s", user_table[0].valid, user_table[0].lv, user_table[0].faceid, user_table[0].name );
			cur_screen = 77;
		}
    }
}
void ui_event_PL_del_ID1_PL_Face_ID_0_Face_ID__LV_0(lv_event_t * e)
{
    lv_event_code_t event_code = lv_event_get_code(e);

    if(event_code == LV_EVENT_CLICKED) {
    	cur_user = 0;
        //_ui_flag_modify(ui_LV_confirm2, LV_OBJ_FLAG_HIDDEN, _UI_MODIFY_FLAG_TOGGLE);
        lv_obj_clear_flag(ui_PL_Admin_PWD, LV_OBJ_FLAG_HIDDEN);
        cur_screen = 127;
    }
}
void ui_event_PL_del_ID1_PL_Face_ID_1_Face_ID_1(lv_event_t * e)
{
    lv_event_code_t event_code = lv_event_get_code(e);

    if(event_code == LV_EVENT_CLICKED) {
		cur_user = 1;
		if( user_table[1].valid !=1 ){
			cur_screen = 71;
			strcpy(pw,"");
			lv_obj_clear_flag(ui_PL_Admin_PWD, LV_OBJ_FLAG_HIDDEN);
			//_ui_screen_change(&ui_SC_Face_Rec, LV_SCR_LOAD_ANIM_FADE_ON, 1100, 300, &ui_SC_Face_Rec_screen_init);
		}
		else{
			cur_screen = 77;
		}
    }
}
void ui_event_PL_del_ID1_PL_Face_ID_1_Face_ID__LV_1(lv_event_t * e)
{
    lv_event_code_t event_code = lv_event_get_code(e);

    if(event_code == LV_EVENT_CLICKED) {
    	cur_user = 1;
        //_ui_flag_modify(ui_LV_confirm2, LV_OBJ_FLAG_HIDDEN, _UI_MODIFY_FLAG_TOGGLE);
        lv_obj_clear_flag(ui_PL_Admin_PWD, LV_OBJ_FLAG_HIDDEN);
        cur_screen = 127;
    }
}
void ui_event_PL_del_ID1_PL_Face_ID_2_Face_ID_2(lv_event_t * e)
{
    lv_event_code_t event_code = lv_event_get_code(e);

    if(event_code == LV_EVENT_CLICKED) {
    	cur_user = 2;
		if( user_table[2].valid !=1 ){
			cur_screen = 71;
			strcpy(pw,"");
			lv_obj_clear_flag(ui_PL_Admin_PWD, LV_OBJ_FLAG_HIDDEN);
			//_ui_screen_change(&ui_SC_Face_Rec, LV_SCR_LOAD_ANIM_FADE_ON, 1100, 300, &ui_SC_Face_Rec_screen_init);
		}
		else{
			cur_screen = 77;
		}
    }
}
void ui_event_PL_del_ID1_PL_Face_ID_2_Face_ID__LV_2(lv_event_t * e)
{
    lv_event_code_t event_code = lv_event_get_code(e);

    if(event_code == LV_EVENT_CLICKED) {
    	cur_user = 2;
        //_ui_flag_modify(ui_LV_confirm2, LV_OBJ_FLAG_HIDDEN, _UI_MODIFY_FLAG_TOGGLE);
        lv_obj_clear_flag(ui_PL_Admin_PWD, LV_OBJ_FLAG_HIDDEN);
        cur_screen = 127;
    }
}
void ui_event_PL_del_ID1_PL_Face_ID_3_Face_ID_3(lv_event_t * e)
{
    lv_event_code_t event_code = lv_event_get_code(e);

    if(event_code == LV_EVENT_CLICKED) {
    	cur_user = 3;
		if( user_table[3].valid !=1 ){
			cur_screen = 71;
			strcpy(pw,"");
			lv_obj_clear_flag(ui_PL_Admin_PWD, LV_OBJ_FLAG_HIDDEN);
			//_ui_screen_change(&ui_SC_Face_Rec, LV_SCR_LOAD_ANIM_FADE_ON, 1100, 300, &ui_SC_Face_Rec_screen_init);
		}
		else{
			cur_screen = 77;
		}
    }
}
void ui_event_PL_del_ID1_PL_Face_ID_3_Face_ID__LV_3(lv_event_t * e)
{
    lv_event_code_t event_code = lv_event_get_code(e);

    if(event_code == LV_EVENT_CLICKED) {
    	cur_user = 3;
        //_ui_flag_modify(ui_LV_confirm2, LV_OBJ_FLAG_HIDDEN, _UI_MODIFY_FLAG_TOGGLE);
        lv_obj_clear_flag(ui_PL_Admin_PWD, LV_OBJ_FLAG_HIDDEN);
        cur_screen = 127;
    }
}
void ui_event_PL_del_ID1_PL_Face_ID_4_Face_ID_4(lv_event_t * e)
{
    lv_event_code_t event_code = lv_event_get_code(e);

    if(event_code == LV_EVENT_CLICKED) {
    	cur_user = 4;
		if( user_table[4].valid !=1 ){
			cur_screen = 71;
			strcpy(pw,"");
			lv_obj_clear_flag(ui_PL_Admin_PWD, LV_OBJ_FLAG_HIDDEN);
			//_ui_screen_change(&ui_SC_Face_Rec, LV_SCR_LOAD_ANIM_FADE_ON, 1100, 300, &ui_SC_Face_Rec_screen_init);
		}
		else{
			cur_screen = 77;
		}
    }
}
void ui_event_PL_del_ID1_PL_Face_ID_4_Face_ID__LV_4(lv_event_t * e)
{
    lv_event_code_t event_code = lv_event_get_code(e);

    if(event_code == LV_EVENT_CLICKED) {
    	cur_user = 4;
        //_ui_flag_modify(ui_LV_confirm2, LV_OBJ_FLAG_HIDDEN, _UI_MODIFY_FLAG_TOGGLE);
        lv_obj_clear_flag(ui_PL_Admin_PWD, LV_OBJ_FLAG_HIDDEN);
        cur_screen = 127;
    }
}
void ui_event_PL_del_ID1_PL_Face_ID_5_Face_ID_5(lv_event_t * e)
{
    lv_event_code_t event_code = lv_event_get_code(e);

    if(event_code == LV_EVENT_CLICKED) {
    	cur_user = 5;
		if( user_table[5].valid !=1 ){
			cur_screen = 71;
			strcpy(pw,"");
			lv_obj_clear_flag(ui_PL_Admin_PWD, LV_OBJ_FLAG_HIDDEN);
			//_ui_screen_change(&ui_SC_Face_Rec, LV_SCR_LOAD_ANIM_FADE_ON, 1100, 300, &ui_SC_Face_Rec_screen_init);
		}
		else{
			cur_screen = 77;
		}
    }
}
void ui_event_PL_del_ID1_PL_Face_ID_5_Face_ID__LV_5(lv_event_t * e)
{
    lv_event_code_t event_code = lv_event_get_code(e);

    if(event_code == LV_EVENT_CLICKED) {
    	cur_user = 5;
       //_ui_flag_modify(ui_LV_confirm2, LV_OBJ_FLAG_HIDDEN, _UI_MODIFY_FLAG_TOGGLE);
        lv_obj_clear_flag(ui_PL_Admin_PWD, LV_OBJ_FLAG_HIDDEN);
        cur_screen = 127;
    }
}
void ui_event_PL_del_ID1_PL_Face_ID_6_Face_ID_6(lv_event_t * e)
{
    lv_event_code_t event_code = lv_event_get_code(e);

    if(event_code == LV_EVENT_CLICKED) {
    	cur_user = 6;
		if( user_table[6].valid !=1 ){
			cur_screen = 71;
			strcpy(pw,"");
			lv_obj_clear_flag(ui_PL_Admin_PWD, LV_OBJ_FLAG_HIDDEN);
			//_ui_screen_change(&ui_SC_Face_Rec, LV_SCR_LOAD_ANIM_FADE_ON, 1100, 300, &ui_SC_Face_Rec_screen_init);
		}
		else{
			cur_screen = 77;
		}
    }
}
void ui_event_PL_del_ID1_PL_Face_ID_6_Face_ID__LV_6(lv_event_t * e)
{
    lv_event_code_t event_code = lv_event_get_code(e);

    if(event_code == LV_EVENT_CLICKED) {
    	cur_user = 6;
        //_ui_flag_modify(ui_LV_confirm2, LV_OBJ_FLAG_HIDDEN, _UI_MODIFY_FLAG_TOGGLE);
        lv_obj_clear_flag(ui_PL_Admin_PWD, LV_OBJ_FLAG_HIDDEN);
        cur_screen = 127;
    }
}

void ui_event_LV_Cofirm_OK2(lv_event_t * e)
{
    lv_event_code_t event_code = lv_event_get_code(e);
    lv_obj_t * target = lv_event_get_target(e);
    if(event_code == LV_EVENT_CLICKED) {
    	user_table[cur_user].lv = lv_roller_get_selected(ui_Roller_LV2)+1;
    	lv_label_set_text_fmt(ui_comp_get_child(ui_PL_del_ID1, 5*cur_user+5 ),"%d",user_table[cur_user].lv);
    	_ui_flag_modify(ui_LV_confirm2, LV_OBJ_FLAG_HIDDEN, _UI_MODIFY_FLAG_TOGGLE);
    	cur_screen = 12;
    }
}
void ui_event_BTN_Lif6(lv_event_t * e)
{
    lv_event_code_t event_code = lv_event_get_code(e);
    lv_obj_t * target = lv_event_get_target(e);
    if(event_code == LV_EVENT_CLICKED) {
        //_ui_flag_modify(ui_Roller6, LV_OBJ_FLAG_HIDDEN, _UI_MODIFY_FLAG_TOGGLE);
        lv_textarea_del_char(ui_TextArea_Login_PWD);
    }
}
void ui_event_BTN_Rig6(lv_event_t * e)
{
    lv_event_code_t event_code = lv_event_get_code(e);
    lv_obj_t * target = lv_event_get_target(e);
    if(event_code == LV_EVENT_CLICKED) {
        //_ui_flag_modify(ui_Roller6, LV_OBJ_FLAG_HIDDEN, _UI_MODIFY_FLAG_TOGGLE);
        lv_roller_get_selected_str(ui_Roller6, pw, 2);
    	lv_textarea_add_char(ui_TextArea_Login_PWD, *pw );
    	strcpy(pw,lv_textarea_get_text(ui_TextArea_Login_PWD));
    }
}
void ui_event_BTN_login_PWD_OK2(lv_event_t * e)
{
    lv_event_code_t event_code = lv_event_get_code(e);
    lv_obj_t * target = lv_event_get_target(e);
    if(event_code == LV_EVENT_CLICKED) {
        //_ui_screen_change(&ui_SC_6_1, LV_SCR_LOAD_ANIM_FADE_ON, 500, 0, &ui_SC_6_1_screen_init);
        if(!strcmp(pw, default_pw)){
			strcpy(pw,"");
        	lv_textarea_set_text(ui_TextArea_Login_PWD,"");
        	if(last_screen==71){
				cur_screen = 711;
        		_ui_screen_change(&ui_SC_Face_Rec, LV_SCR_LOAD_ANIM_FADE_ON, 100, 900, &ui_SC_Face_Rec_screen_init);
        	}
        	else if(cur_screen == 127){
				lv_obj_add_flag(ui_PL_Admin_PWD, LV_OBJ_FLAG_HIDDEN);
				_ui_flag_modify(ui_LV_confirm2, LV_OBJ_FLAG_HIDDEN, _UI_MODIFY_FLAG_TOGGLE);
			}
        	else{
 				lv_obj_add_flag(ui_PL_Admin_PWD, LV_OBJ_FLAG_HIDDEN);
 				cur_screen = 771;
 				user_table[cur_user].valid=0;
 			}
 			
        }
    }
}

void ui_event_BTN_set_PWD_cancel2(lv_event_t * e)
{
    lv_event_code_t event_code = lv_event_get_code(e);
    lv_obj_t * target = lv_event_get_target(e);
    if(event_code == LV_EVENT_CLICKED) {
        //_ui_screen_change(&ui_SC_1_1, LV_SCR_LOAD_ANIM_FADE_ON, 500, 0, &ui_SC_1_1_screen_init);
        if( cur_screen == 71 )
        	user_table[cur_user].valid=0;
        lv_obj_add_flag(ui_PL_Admin_PWD, LV_OBJ_FLAG_HIDDEN);
        cur_screen = 12;
        strcpy(pw,"");
        lv_textarea_set_text(ui_TextArea_Login_PWD,"");
        //ESP_LOGI(TAG, "Updating NVS user 0 valid=%d lv=%d faceid=%d name=%s", user_table[0].valid, user_table[0].lv, user_table[0].faceid, user_table[0].name );
    }
}
void ui_event_SC_3_2(lv_event_t * e)
{
	lv_event_code_t event_code = lv_event_get_code(e);

	if(cur_screen < 400){
		if(event_code == LV_EVENT_GESTURE &&  lv_indev_get_gesture_dir(lv_indev_get_act()) == LV_DIR_RIGHT) {
			lv_indev_wait_release(lv_indev_get_act());
            find_screen(2,1,RIGHT);  //LEFT 0 RIGHT 1 TOP 2 BOTTOM 3
            go_screen(screen_found,LV_SCR_LOAD_ANIM_MOVE_RIGHT,10);
		}
		if(event_code == LV_EVENT_GESTURE &&  lv_indev_get_gesture_dir(lv_indev_get_act()) == LV_DIR_BOTTOM) {
			lv_indev_wait_release(lv_indev_get_act());
            search_up(3,1, true);
            go_screen(screen_found,LV_SCR_LOAD_ANIM_MOVE_BOTTOM,10);
		}
		if(event_code == LV_EVENT_GESTURE &&  lv_indev_get_gesture_dir(lv_indev_get_act()) == LV_DIR_TOP) {
			lv_indev_wait_release(lv_indev_get_act());
            search_down(3,3, true);
            go_screen(screen_found,LV_SCR_LOAD_ANIM_MOVE_TOP,10);
		}
		if(event_code == LV_EVENT_GESTURE &&  lv_indev_get_gesture_dir(lv_indev_get_act()) == LV_DIR_LEFT) {
			lv_indev_wait_release(lv_indev_get_act());
        	find_screen(4,1,LEFT);  //LEFT 0 RIGHT 1 TOP 2 BOTTOM 3
        	go_screen(screen_found,LV_SCR_LOAD_ANIM_MOVE_LEFT,10);
		}
	}
}
void ui_event_PL_X_Up_bound1(lv_event_t * e)
{
    lv_event_code_t event_code = lv_event_get_code(e);

    if(event_code == LV_EVENT_CLICKED) {
        _ui_flag_modify(ui_BTN_GC_R1, LV_OBJ_FLAG_HIDDEN, _UI_MODIFY_FLAG_ADD);
        _ui_flag_modify(ui_BTN_GC_L1, LV_OBJ_FLAG_HIDDEN, _UI_MODIFY_FLAG_ADD);
    	cur_screen = cur_screen*10+3;  //3203
    	bound_value = atoi(lv_label_get_text(ui_X_UP_bon_Value1));
    	sprintf(buf,"%04d",bound_value);
    	lv_roller_set_selected(ui_Set_X_bon_x5,buf[3]-'0',LV_ANIM_ON);
    	lv_roller_set_selected(ui_Set_X_bon_x4,buf[2]-'0',LV_ANIM_ON);
    	lv_roller_set_selected(ui_Set_X_bon_x3,buf[1]-'0',LV_ANIM_ON);
    	lv_roller_set_selected(ui_Set_X_bon_x2,buf[0]-'0',LV_ANIM_ON);

    	_ui_flag_modify(ui_PL_X_Value_OK1, LV_OBJ_FLAG_HIDDEN, _UI_MODIFY_FLAG_REMOVE);
    }
}
void ui_event_PL_X_low_bound1(lv_event_t * e)
{
    lv_event_code_t event_code = lv_event_get_code(e);

    if(event_code == LV_EVENT_CLICKED) {
        _ui_flag_modify(ui_BTN_GC_R1, LV_OBJ_FLAG_HIDDEN, _UI_MODIFY_FLAG_ADD);
        _ui_flag_modify(ui_BTN_GC_L1, LV_OBJ_FLAG_HIDDEN, _UI_MODIFY_FLAG_ADD);

        cur_screen = cur_screen*10+4; //3204
    	bound_value = atoi(lv_label_get_text(ui_X_low_bon_Value1));
    	sprintf(buf,"%05d",bound_value);
    	lv_roller_set_selected(ui_Set_X_bon_x5,buf[4]-'0',LV_ANIM_ON);
    	lv_roller_set_selected(ui_Set_X_bon_x4,buf[3]-'0',LV_ANIM_ON);
    	lv_roller_set_selected(ui_Set_X_bon_x3,buf[2]-'0',LV_ANIM_ON);
    	lv_roller_set_selected(ui_Set_X_bon_x2,buf[1]-'0',LV_ANIM_ON);

    	_ui_flag_modify(ui_PL_X_Value_OK1, LV_OBJ_FLAG_HIDDEN, _UI_MODIFY_FLAG_REMOVE);
    }
}
void ui_event_BTN_X_bon_Value_OK1(lv_event_t * e)
{
    lv_event_code_t event_code = lv_event_get_code(e);

    if(event_code == LV_EVENT_CLICKED) {
        _ui_flag_modify(ui_PL_X_Value_OK1, LV_OBJ_FLAG_HIDDEN, _UI_MODIFY_FLAG_ADD);
        _ui_flag_modify(ui_BTN_GC_R1, LV_OBJ_FLAG_HIDDEN, _UI_MODIFY_FLAG_REMOVE);
        _ui_flag_modify(ui_BTN_GC_L1, LV_OBJ_FLAG_HIDDEN, _UI_MODIFY_FLAG_REMOVE);

    	bound_value = lv_roller_get_selected(ui_Set_X_bon_x5);
    	bound_value = 10*lv_roller_get_selected(ui_Set_X_bon_x4) + bound_value;
    	bound_value = 100*lv_roller_get_selected(ui_Set_X_bon_x3) + bound_value;
    	bound_value = 1000*lv_roller_get_selected(ui_Set_X_bon_x2) + bound_value;
    	cur_screen = cur_screen-2; //3201
    }
}
void ui_event_BTN_GC_L1(lv_event_t * e)
{
    lv_event_code_t event_code = lv_event_get_code(e);

    if(event_code == LV_EVENT_CLICKED) {
    	if(cur_screen == 320){
    		cur_screen = 322;
    	}
    	else if(cur_screen == 321){
    		cur_screen = 320;
    	}
    	else if(cur_screen == 322){
    		cur_screen = 321;
    	}
    }
}
void ui_event_BTN_GC_R1(lv_event_t * e)
{
    lv_event_code_t event_code = lv_event_get_code(e);

    if(event_code == LV_EVENT_CLICKED) {
    	if(cur_screen == 320){
    		cur_screen = 321;
    	}
    	else if(cur_screen == 321){
    		cur_screen = 322;
    	}
    	else if(cur_screen == 322){
    		cur_screen = 320;
    	}
    }
}
void ui_event_SC_4_1(lv_event_t * e)
{
    lv_event_code_t event_code = lv_event_get_code(e);

    if(event_code == LV_EVENT_GESTURE &&  lv_indev_get_gesture_dir(lv_indev_get_act()) == LV_DIR_RIGHT) {
        lv_indev_wait_release(lv_indev_get_act());
        find_screen(3,1,RIGHT);  //LEFT 0 RIGHT 1 TOP 2 BOTTOM 3
        go_screen(screen_found,LV_SCR_LOAD_ANIM_MOVE_RIGHT,10);
    }
    if(event_code == LV_EVENT_GESTURE &&  lv_indev_get_gesture_dir(lv_indev_get_act()) == LV_DIR_TOP) {
        lv_indev_wait_release(lv_indev_get_act());
        search_down(4,2, true);
        go_screen(screen_found,LV_SCR_LOAD_ANIM_MOVE_TOP,10);
    }
    if(event_code == LV_EVENT_GESTURE &&  lv_indev_get_gesture_dir(lv_indev_get_act()) == LV_DIR_BOTTOM) {
        lv_indev_wait_release(lv_indev_get_act());
        search_up(4,2, true);
        go_screen(screen_found,LV_SCR_LOAD_ANIM_MOVE_BOTTOM,10);
    }
    if(event_code == LV_EVENT_GESTURE &&  lv_indev_get_gesture_dir(lv_indev_get_act()) == LV_DIR_LEFT) {
        lv_indev_wait_release(lv_indev_get_act());
    	find_screen(5,1,LEFT);  //LEFT 0 RIGHT 1 TOP 2 BOTTOM 3
    	go_screen(screen_found,LV_SCR_LOAD_ANIM_MOVE_LEFT,10);
    }
}
void ui_event_BTN_TOOL_SET1(lv_event_t * e)
{
    lv_event_code_t event_code = lv_event_get_code(e);

    if(event_code == LV_EVENT_CLICKED) {
    	tool_now = lv_roller_get_selected(ui_RO_TOOL_SET1_X1);
    	tool_now = 10*lv_roller_get_selected(ui_RO_TOOL_SET1_X10) + tool_now;
    	tool_now = 100*lv_roller_get_selected(ui_RO_TOOL_SET1_X100) + tool_now;
    	tool_now = 1000*lv_roller_get_selected(ui_RO_TOOL_SET1_X1000) + tool_now;
    	tool_now = 10000*lv_roller_get_selected(ui_RO_TOOL_SET1_X10000) + tool_now;
    	cur_screen = 411;
    }
}
void ui_event_SC_4_2(lv_event_t * e)
{
    lv_event_code_t event_code = lv_event_get_code(e);

    if(event_code == LV_EVENT_GESTURE &&  lv_indev_get_gesture_dir(lv_indev_get_act()) == LV_DIR_RIGHT) {
        lv_indev_wait_release(lv_indev_get_act());
        find_screen(3,1,RIGHT);  //LEFT 0 RIGHT 1 TOP 2 BOTTOM 3
        go_screen(screen_found,LV_SCR_LOAD_ANIM_MOVE_RIGHT,10);
    }
    if(event_code == LV_EVENT_GESTURE &&  lv_indev_get_gesture_dir(lv_indev_get_act()) == LV_DIR_TOP) {
        lv_indev_wait_release(lv_indev_get_act());
        search_down(4,1, true);
        go_screen(screen_found,LV_SCR_LOAD_ANIM_MOVE_TOP,10);
    }
    if(event_code == LV_EVENT_GESTURE &&  lv_indev_get_gesture_dir(lv_indev_get_act()) == LV_DIR_BOTTOM) {
        lv_indev_wait_release(lv_indev_get_act());
        search_up(4,1, true);
        go_screen(screen_found,LV_SCR_LOAD_ANIM_MOVE_BOTTOM,10);
    }
    if(event_code == LV_EVENT_GESTURE &&  lv_indev_get_gesture_dir(lv_indev_get_act()) == LV_DIR_LEFT) {
        lv_indev_wait_release(lv_indev_get_act());
    	find_screen(5,1,LEFT);  //LEFT 0 RIGHT 1 TOP 2 BOTTOM 3
    	go_screen(screen_found,LV_SCR_LOAD_ANIM_MOVE_LEFT,10);
    }
    if(event_code == LV_EVENT_SCROLL_END ) {
    	lv_obj_set_style_bg_color(ui_RO_TOOL_SET2_X10000, lv_color_hex(0x77E377), LV_PART_SELECTED | LV_STATE_DEFAULT);
    }
}
void ui_event_BTN_TOOL_SET2(lv_event_t * e)
{
    lv_event_code_t event_code = lv_event_get_code(e);

    if(event_code == LV_EVENT_CLICKED) {
    	tool_now2 = lv_roller_get_selected(ui_RO_TOOL_SET2_X1);
    	tool_now2 = 10*lv_roller_get_selected(ui_RO_TOOL_SET2_X10) + tool_now2;
    	tool_now2 = 100*lv_roller_get_selected(ui_RO_TOOL_SET2_X100) + tool_now2;
    	tool_now2 = 1000*lv_roller_get_selected(ui_RO_TOOL_SET2_X1000) + tool_now2;
    	tool_now2 = 10000*lv_roller_get_selected(ui_RO_TOOL_SET2_X10000) + tool_now2;
    	cur_screen = 421;
    }
}
void ui_event_SC_5_1(lv_event_t * e)
{
    lv_event_code_t event_code = lv_event_get_code(e);

    if(event_code == LV_EVENT_GESTURE &&  lv_indev_get_gesture_dir(lv_indev_get_act()) == LV_DIR_RIGHT) {
        lv_indev_wait_release(lv_indev_get_act());
        find_screen(4,1,RIGHT);  //LEFT 0 RIGHT 1 TOP 2 BOTTOM 3
        go_screen(screen_found,LV_SCR_LOAD_ANIM_MOVE_RIGHT,10);
    }
    if(event_code == LV_EVENT_GESTURE &&  lv_indev_get_gesture_dir(lv_indev_get_act()) == LV_DIR_LEFT) {
        lv_indev_wait_release(lv_indev_get_act());
    	find_screen(6,1,LEFT);  //LEFT 0 RIGHT 1 TOP 2 BOTTOM 3
    	go_screen(screen_found,LV_SCR_LOAD_ANIM_MOVE_LEFT,10);
    }
}
void ui_event_BTN_Lic_renew(lv_event_t * e)
{
    lv_event_code_t event_code = lv_event_get_code(e);
    lv_obj_t * target = lv_event_get_target(e);
    if(event_code == LV_EVENT_CLICKED) {
        _ui_screen_change(&ui_SC_Key_input, LV_SCR_LOAD_ANIM_FADE_ON, 100, 0, &ui_SC_Key_input_screen_init);
        cur_screen = 52;
    }
}


void ui_event_BTN_edit_save(lv_event_t * e)
{
    lv_event_code_t event_code = lv_event_get_code(e);
    lv_obj_t * target = lv_event_get_target(e);
    if(event_code == LV_EVENT_CLICKED) {
		strcpy(user_table[cur_user].name,lv_textarea_get_text(ui_TA_ID_Name));
		user_table[cur_user].lv = atoi(lv_textarea_get_text(ui_TA_LV));
        _ui_screen_change(&ui_SC_1_2, LV_SCR_LOAD_ANIM_FADE_ON, 100, 0, &ui_SC_1_2_screen_init);
         cur_screen = 122;
		lv_textarea_set_text(ui_TA_ID_Name, "User");

    }
}
void ui_event_BTN_exit3(lv_event_t * e)
{
    lv_event_code_t event_code = lv_event_get_code(e);
    lv_obj_t * target = lv_event_get_target(e);
    if(event_code == LV_EVENT_CLICKED) {
        _ui_screen_change(&ui_SC_1_2, LV_SCR_LOAD_ANIM_FADE_ON, 100, 0, &ui_SC_1_2_screen_init);
        cur_screen = 123;
    }
}
void ui_event_BTN_edit_NL(lv_event_t * e)
{
    lv_event_code_t event_code = lv_event_get_code(e);

    if(event_code == LV_EVENT_CLICKED) {
        _ui_flag_modify(ui_Select_Edit_NoL, LV_OBJ_FLAG_HIDDEN, _UI_MODIFY_FLAG_REMOVE);
    }
}

void ui_event_BTN_Edit_Name(lv_event_t * e)
{
    lv_event_code_t event_code = lv_event_get_code(e);

    if(event_code == LV_EVENT_CLICKED) {
        _ui_flag_modify(ui_Select_Edit_NoL, LV_OBJ_FLAG_HIDDEN, _UI_MODIFY_FLAG_ADD);
        _ui_keyboard_set_target(ui_Keyboard1,  ui_TA_ID_Name);
    }
}

void ui_event_BTN_Edit_LV(lv_event_t * e)
{
    lv_event_code_t event_code = lv_event_get_code(e);

    if(event_code == LV_EVENT_CLICKED) {
        _ui_flag_modify(ui_Select_Edit_NoL, LV_OBJ_FLAG_HIDDEN, _UI_MODIFY_FLAG_ADD);
        _ui_keyboard_set_target(ui_Keyboard1,  ui_TA_LV);

    }
}

void ui_event_SC_Face_Login(lv_event_t * e)
{
    lv_event_code_t event_code = lv_event_get_code(e);

    if(event_code == LV_EVENT_SCREEN_LOADED) {
        _ui_flag_modify(ui_Image13, LV_OBJ_FLAG_HIDDEN, _UI_MODIFY_FLAG_ADD);
    }
}
void ui_event_BTN_exit2(lv_event_t * e)
{
    lv_event_code_t event_code = lv_event_get_code(e);
    lv_obj_t * target = lv_event_get_target(e);
    if(event_code == LV_EVENT_CLICKED) {
		go_screen(11, LV_SCR_LOAD_ANIM_FADE_ON, 100);
        //_ui_screen_change(&ui_SC_1_2, LV_SCR_LOAD_ANIM_FADE_ON, 500, 0, &ui_SC_1_2_screen_init);
    }
}
void ui_event_BTN_Lic_save(lv_event_t * e)
{
    lv_event_code_t event_code = lv_event_get_code(e);
    lv_obj_t * target = lv_event_get_target(e);
    if(event_code == LV_EVENT_CLICKED) {
		strcpy(license_code,lv_textarea_get_text(ui_TA_Lic_code));
        cur_screen = 521;
    }
}
void ui_event_BTN_exit4(lv_event_t * e)
{
    lv_event_code_t event_code = lv_event_get_code(e);
    lv_obj_t * target = lv_event_get_target(e);
    if(event_code == LV_EVENT_CLICKED) {
        _ui_screen_change(&ui_SC_5_1, LV_SCR_LOAD_ANIM_FADE_ON, 100, 10, &ui_SC_5_1_screen_init);
        cur_screen = 51;
    }
}

///////////////////// SCREENS ////////////////////

void ui_init(void)
{

    LV_EVENT_GET_COMP_CHILD = lv_event_register_id();

    lv_disp_t * dispp = lv_disp_get_default();
    lv_theme_t * theme = lv_theme_default_init(dispp, lv_palette_main(LV_PALETTE_BLUE), lv_palette_main(LV_PALETTE_RED),
                                               true, LV_FONT_DEFAULT);         
              	                               
    lv_disp_set_theme(dispp, theme);
    ui_SC_LOGO_screen_init(); //1
    ui_SC_first_login_screen_init();//10
    ui_SC_Time_set_screen_init();//64
    ui_SC_ModBus_set_screen_init();//65
    ui_SC_Face_Rec_screen_init();//71
    ui_SC_1_1_screen_init(); //11 19
    ui_SC_2_1_screen_init();
    ui_SC_2_2_screen_init();
    ui_SC_2_3_screen_init();
    ui_SC_2_4_screen_init();
    ui_SC_2_5_screen_init();
    ui_SC_2_6_screen_init();
    ui_SC_3_1_screen_init();
    ui_SC_3_3_screen_init();
    ui_SC_3_4_screen_init();
    ui_SC_6_1_screen_init(); //61
    ui_SC_set_ModBus_ID_screen_init(); //62
    ui_SC_set_PWD_screen_init(); //63
    ui_SC_1_2_screen_init(); 
    ui_SC_3_2_screen_init();
    ui_SC_4_1_screen_init();
    ui_SC_4_2_screen_init();
    ui_SC_5_1_screen_init();
    ui_SC_ID_EDIT_screen_init(); //121
    ui_SC_Face_Login_screen_init(); //70
    ui_SC_Key_input_screen_init();
    ui____initial_actions0 = lv_obj_create(NULL);
    
    if( holding_reg_params_slave.holding_data9==3 ){ 
		//11
		lv_obj_set_style_text_color(ui_Label3, lv_color_hex(0xffffff), LV_PART_MAIN | LV_STATE_DEFAULT);
		//12
		lv_obj_set_style_text_color(ui_Label66, lv_color_hex(0xffffff), LV_PART_MAIN | LV_STATE_DEFAULT);
		//lv_obj_set_style_text_color(ui_TextArea_Login_PWD, lv_color_hex(0xffffff), LV_PART_MAIN | LV_STATE_DEFAULT);
		//21
		lv_obj_set_style_text_color(ui_Label5, lv_color_hex(0xffffff), LV_PART_MAIN | LV_STATE_DEFAULT);
		lv_obj_set_style_text_color(ui_Label6, lv_color_hex(0xffffff), LV_PART_MAIN | LV_STATE_DEFAULT);
		//22
		lv_obj_set_style_text_color(ui_Label7, lv_color_hex(0xffffff), LV_PART_MAIN | LV_STATE_DEFAULT);
		lv_obj_set_style_text_color(ui_Label8, lv_color_hex(0xffffff), LV_PART_MAIN | LV_STATE_DEFAULT);
		lv_obj_set_style_text_color(ui_Lab_wheel, lv_color_hex(0xffffff), LV_PART_MAIN | LV_STATE_DEFAULT);
		//23
		lv_obj_set_style_text_color(ui_Label9, lv_color_hex(0xffffff), LV_PART_MAIN | LV_STATE_DEFAULT);
		lv_obj_set_style_text_color(ui_Label10, lv_color_hex(0xffffff), LV_PART_MAIN | LV_STATE_DEFAULT);
		lv_obj_set_style_text_color(ui_Lab_RPM, lv_color_hex(0xffffff), LV_PART_MAIN | LV_STATE_DEFAULT);
		lv_obj_set_style_text_color(ui_Label11, lv_color_hex(0xffffff), LV_PART_MAIN | LV_STATE_DEFAULT);
		//24
		lv_obj_set_style_text_color(ui_Label12, lv_color_hex(0xffffff), LV_PART_MAIN | LV_STATE_DEFAULT);
		lv_obj_set_style_text_color(ui_Lab_wheel1, lv_color_hex(0xffffff), LV_PART_MAIN | LV_STATE_DEFAULT);
		//25
		lv_obj_set_style_text_color(ui_Label13, lv_color_hex(0xffffff), LV_PART_MAIN | LV_STATE_DEFAULT);
		lv_obj_set_style_text_color(ui_Lab_RPM1, lv_color_hex(0xffffff), LV_PART_MAIN | LV_STATE_DEFAULT);
		lv_obj_set_style_text_color(ui_Label14, lv_color_hex(0xffffff), LV_PART_MAIN | LV_STATE_DEFAULT);
		//26
		lv_obj_set_style_text_color(ui_Label15, lv_color_hex(0xffffff), LV_PART_MAIN | LV_STATE_DEFAULT);
		lv_obj_set_style_text_color(ui_Lab_Ampere, lv_color_hex(0xffffff), LV_PART_MAIN | LV_STATE_DEFAULT);
		lv_obj_set_style_text_color(ui_Label16, lv_color_hex(0xffffff), LV_PART_MAIN | LV_STATE_DEFAULT);
		//31
		lv_obj_set_style_text_color(ui_LAB_GC_ASIX, lv_color_hex(0xffffff), LV_PART_MAIN | LV_STATE_DEFAULT);
		//32
		lv_obj_set_style_text_color(ui_LAB_GC_ASIX1, lv_color_hex(0xffffff), LV_PART_MAIN | LV_STATE_DEFAULT);
		//33
		lv_obj_set_style_text_color(ui_LB_Cab, lv_color_hex(0xffffff), LV_PART_MAIN | LV_STATE_DEFAULT);
		//34
		lv_obj_set_style_text_color(ui_Table_Title, lv_color_hex(0xffffff), LV_PART_ITEMS);
		lv_obj_set_style_text_color(ui_Table_Log, lv_color_hex(0xffffff), LV_PART_ITEMS);
		lv_obj_set_style_text_color(ui_Label47, lv_color_hex(0xffffff), LV_PART_MAIN | LV_STATE_DEFAULT);
		//41
		lv_obj_set_style_text_color(ui_Label25, lv_color_hex(0xffffff), LV_PART_MAIN | LV_STATE_DEFAULT);
		lv_obj_set_style_text_color(ui_Label29, lv_color_hex(0xffffff), LV_PART_MAIN | LV_STATE_DEFAULT);
		//lv_obj_set_style_text_color(ui_RO_TOOL_SET1_X10000, lv_color_hex(0xffffff), LV_PART_MAIN | LV_STATE_DEFAULT);
		//lv_obj_set_style_text_color(ui_RO_TOOL_SET1_X1000, lv_color_hex(0xffffff), LV_PART_MAIN | LV_STATE_DEFAULT);
		lv_obj_set_style_text_color(ui_Label30, lv_color_hex(0xffffff), LV_PART_MAIN | LV_STATE_DEFAULT);
		//42
		lv_obj_set_style_text_color(ui_Label36, lv_color_hex(0xffffff), LV_PART_MAIN | LV_STATE_DEFAULT);
		lv_obj_set_style_text_color(ui_Label37, lv_color_hex(0xffffff), LV_PART_MAIN | LV_STATE_DEFAULT);
		lv_obj_set_style_text_color(ui_Label39, lv_color_hex(0xffffff), LV_PART_MAIN | LV_STATE_DEFAULT);
		//51
		lv_obj_set_style_text_color(ui_Label38, lv_color_hex(0xffffff), LV_PART_MAIN | LV_STATE_DEFAULT);
		lv_obj_set_style_text_color(ui_Label40, lv_color_hex(0xffffff), LV_PART_MAIN | LV_STATE_DEFAULT);
		lv_obj_set_style_text_color(ui_LAB_exp_date, lv_color_hex(0xffffff), LV_PART_MAIN | LV_STATE_DEFAULT);
		lv_obj_set_style_text_color(ui_Label74, lv_color_hex(0xffffff), LV_PART_MAIN | LV_STATE_DEFAULT);
		//61
		lv_obj_set_style_text_color(ui_Label51, lv_color_hex(0xffffff), LV_PART_MAIN | LV_STATE_DEFAULT);
		//face login
		lv_obj_set_style_text_color(ui_Label63, lv_color_hex(0xffffff), LV_PART_MAIN | LV_STATE_DEFAULT);
		//face rec
		lv_obj_set_style_text_color(ui_Label28, lv_color_hex(0xffffff), LV_PART_MAIN | LV_STATE_DEFAULT);
		lv_obj_set_style_text_color(ui_Label57, lv_color_hex(0xffffff), LV_PART_MAIN | LV_STATE_DEFAULT);
		//id_edit
		lv_obj_set_style_text_color(ui_Label59, lv_color_hex(0xffffff), LV_PART_MAIN | LV_STATE_DEFAULT);
		lv_obj_set_style_text_color(ui_Label53, lv_color_hex(0xffffff), LV_PART_MAIN | LV_STATE_DEFAULT);
		lv_obj_set_style_text_color(ui_TA_ID_Name, lv_color_hex(0xffffff), LV_PART_MAIN | LV_STATE_DEFAULT);
		lv_obj_set_style_text_color(ui_TA_LV, lv_color_hex(0xffffff), LV_PART_MAIN | LV_STATE_DEFAULT);
		lv_obj_set_style_text_color(ui_Label61, lv_color_hex(0xffffff), LV_PART_MAIN | LV_STATE_DEFAULT);
		lv_obj_set_style_text_color(ui_Label62, lv_color_hex(0xffffff), LV_PART_MAIN | LV_STATE_DEFAULT);
		lv_obj_set_style_text_color(ui_Label73, lv_color_hex(0xffffff), LV_PART_MAIN | LV_STATE_DEFAULT);
		lv_obj_set_style_text_color(ui_Label76, lv_color_hex(0xffffff), LV_PART_MAIN | LV_STATE_DEFAULT);
		lv_obj_set_style_text_color(ui_Label77, lv_color_hex(0xffffff), LV_PART_MAIN | LV_STATE_DEFAULT);
		lv_obj_set_style_text_color(ui_Label78, lv_color_hex(0xffffff), LV_PART_MAIN | LV_STATE_DEFAULT);
		//key input
		lv_obj_set_style_text_color(ui_Label68, lv_color_hex(0xffffff), LV_PART_MAIN | LV_STATE_DEFAULT);
		lv_obj_set_style_text_color(ui_TA_Lic_code, lv_color_hex(0xffffff), LV_PART_MAIN | LV_STATE_DEFAULT);
		//ui_sc_logo
		lv_obj_set_style_text_color(ui_Lab_ID, lv_color_hex(0xffffff), LV_PART_MAIN | LV_STATE_DEFAULT);
		lv_obj_set_style_text_color(ui_Lab_Ver, lv_color_hex(0xffffff), LV_PART_MAIN | LV_STATE_DEFAULT);
		lv_obj_set_style_text_color(ui_Label17, lv_color_hex(0xffffff), LV_PART_MAIN | LV_STATE_DEFAULT);
		lv_obj_set_style_text_color(ui_Label2, lv_color_hex(0xffffff), LV_PART_MAIN | LV_STATE_DEFAULT);
		//modbus set
		lv_obj_set_style_text_color(ui_Label19, lv_color_hex(0xffffff), LV_PART_MAIN | LV_STATE_DEFAULT);
		lv_obj_set_style_text_color(ui_Label_baud, lv_color_hex(0xffffff), LV_PART_MAIN | LV_STATE_DEFAULT);
		//set modbus id
		lv_obj_set_style_text_color(ui_Label54, lv_color_hex(0xffffff), LV_PART_MAIN | LV_STATE_DEFAULT);
		//set pwd
		lv_obj_set_style_text_color(ui_Label56, lv_color_hex(0xffffff), LV_PART_MAIN | LV_STATE_DEFAULT);
		//time set
		lv_obj_set_style_text_color(ui_Label26, lv_color_hex(0xffffff), LV_PART_MAIN | LV_STATE_DEFAULT);
	}
	
    lv_disp_load_scr(ui_SC_LOGO);
    
}



///////////////////////////////////////////////////////////////////////////
#ifdef monkey
void lv_monkey_test(void)
{
    /*Create pointer monkey test*/
    lv_monkey_config_t config;
    lv_monkey_config_init(&config);
    config.type = LV_INDEV_TYPE_POINTER;
    config.period_range.min = 6000;
    config.period_range.max = 5000;
    lv_monkey_t * monkey = lv_monkey_create(&config);

    /*Start monkey test*/
    lv_monkey_set_enable(monkey, true);
}
#endif




