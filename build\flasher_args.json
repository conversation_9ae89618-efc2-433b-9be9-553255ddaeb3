{"write_flash_args": ["--flash_mode", "dio", "--flash_size", "16MB", "--flash_freq", "80m"], "flash_settings": {"flash_mode": "dio", "flash_size": "16MB", "flash_freq": "80m"}, "flash_files": {"0x0": "bootloader/bootloader.bin", "0x10000": "ameter_s3.bin", "0x8000": "partition_table/partition-table.bin", "0x610000": "srmodels/srmodels.bin"}, "bootloader": {"offset": "0x0", "file": "bootloader/bootloader.bin", "encrypted": "false"}, "app": {"offset": "0x10000", "file": "ameter_s3.bin", "encrypted": "false"}, "partition-table": {"offset": "0x8000", "file": "partition_table/partition-table.bin", "encrypted": "false"}, "model": {"offset": "0x610000", "file": "srmodels/srmodels.bin", "encrypted": "false"}, "extra_esptool_args": {"after": "hard_reset", "before": "default_reset", "stub": false, "chip": "esp32s3"}}