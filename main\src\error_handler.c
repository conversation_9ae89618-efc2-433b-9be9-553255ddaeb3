#include "error_handler.h"
#include "memory_manager.h"
#include "esp_system.h"
#include "freertos/FreeRTOS.h"
#include "freertos/semphr.h"
#include <string.h>
#include <time.h>

static const char* TAG = "ERROR_HANDLER";

// Global error handler state
static struct {
    error_handler_config_t config;
    error_record_t* error_records;
    recovery_function_t recovery_functions[ERROR_CATEGORY_MAX];
    SemaphoreHandle_t mutex;
    uint32_t error_count[ERROR_CATEGORY_MAX];
    uint32_t total_errors;
    uint32_t critical_errors;
    bool initialized;
} error_handler = {0};

// Error category names
static const char* error_category_names[] = {
    "MEMORY", "HARDWARE", "COMMUNICATION", "FILESYSTEM", 
    "TASK", "SYSTEM", "APPLICATION"
};

// Error severity names
static const char* error_severity_names[] = {
    "INFO", "WARNING", "ERROR", "CRITICAL", "FATAL"
};

esp_err_t error_handler_init(const error_handler_config_t* config) {
    if (error_handler.initialized) {
        ESP_LOGW(TAG, "Error handler already initialized");
        return ESP_OK;
    }

    if (!config) {
        ESP_LOGE(TAG, "Invalid configuration");
        return ESP_ERR_INVALID_ARG;
    }

    // Copy configuration
    memcpy(&error_handler.config, config, sizeof(error_handler_config_t));

    // Initialize mutex
    error_handler.mutex = xSemaphoreCreateMutex();
    if (!error_handler.mutex) {
        ESP_LOGE(TAG, "Failed to create mutex");
        return ESP_ERR_NO_MEM;
    }

    // Allocate error records array (using system allocation to avoid leak reports)
    if (config->max_error_records > 0) {
        size_t records_size = config->max_error_records * sizeof(error_record_t);
        error_handler.error_records = INTERNAL_SYSTEM_MALLOC(records_size);
        if (!error_handler.error_records) {
            ESP_LOGE(TAG, "Failed to allocate error records array");
            vSemaphoreDelete(error_handler.mutex);
            return ESP_ERR_NO_MEM;
        }
        memset(error_handler.error_records, 0, records_size);
        ESP_LOGI(TAG, "Allocated %zu bytes for error records (system allocation)", records_size);
    }

    // Initialize statistics
    memset(error_handler.error_count, 0, sizeof(error_handler.error_count));
    memset(error_handler.recovery_functions, 0, sizeof(error_handler.recovery_functions));
    error_handler.total_errors = 0;
    error_handler.critical_errors = 0;

    error_handler.initialized = true;
    ESP_LOGI(TAG, "Error handler initialized (max records: %u)", config->max_error_records);

    return ESP_OK;
}

static uint32_t find_error_slot(void) {
    // Find empty slot or oldest error to replace
    uint32_t oldest_slot = 0;
    uint32_t oldest_timestamp = UINT32_MAX;
    
    for (uint32_t i = 0; i < error_handler.config.max_error_records; i++) {
        if (error_handler.error_records[i].error_id == 0) {
            return i; // Found empty slot
        }
        
        if (error_handler.error_records[i].timestamp < oldest_timestamp) {
            oldest_timestamp = error_handler.error_records[i].timestamp;
            oldest_slot = i;
        }
    }
    
    return oldest_slot; // Return oldest slot for replacement
}

static esp_err_t attempt_recovery(const error_record_t* error) {
    if (!error_handler.config.enable_recovery) {
        return ESP_ERR_NOT_SUPPORTED;
    }

    // Try custom recovery function first
    if (error->category < ERROR_CATEGORY_MAX && 
        error_handler.recovery_functions[error->category]) {
        ESP_LOGI(TAG, "Attempting custom recovery for %s error", 
                 error_category_names[error->category]);
        return error_handler.recovery_functions[error->category](error);
    }

    // Default recovery actions
    switch (error->recovery_action) {
        case RECOVERY_ACTION_RETRY:
            ESP_LOGI(TAG, "Recovery action: RETRY");
            vTaskDelay(pdMS_TO_TICKS(error_handler.config.retry_delay_ms));
            return ESP_OK;
            
        case RECOVERY_ACTION_RESET_COMPONENT:
            ESP_LOGI(TAG, "Recovery action: RESET_COMPONENT");
            // Component-specific reset logic would go here
            return ESP_OK;
            
        case RECOVERY_ACTION_RESTART_TASK:
            ESP_LOGI(TAG, "Recovery action: RESTART_TASK");
            // Task restart logic would go here
            return ESP_OK;
            
        case RECOVERY_ACTION_SYSTEM_RESTART:
            ESP_LOGW(TAG, "Recovery action: SYSTEM_RESTART");
            esp_restart();
            return ESP_OK;
            
        default:
            return ESP_ERR_NOT_SUPPORTED;
    }
}

esp_err_t error_handler_report(error_severity_t severity,
                              error_category_t category,
                              esp_err_t error_code,
                              const char* component,
                              const char* function,
                              const char* file,
                              int line,
                              const char* description) {
    if (!error_handler.initialized) {
        // Fallback logging if error handler not initialized
        ESP_LOGE(TAG, "Error handler not initialized - %s: %s", component, description);
        return ESP_ERR_INVALID_STATE;
    }

    if (category >= ERROR_CATEGORY_MAX) {
        ESP_LOGE(TAG, "Invalid error category: %d", category);
        return ESP_ERR_INVALID_ARG;
    }

    if (xSemaphoreTake(error_handler.mutex, pdMS_TO_TICKS(100)) == pdTRUE) {
        // Update statistics
        error_handler.error_count[category]++;
        error_handler.total_errors++;
        
        if (severity >= ERROR_SEVERITY_CRITICAL) {
            error_handler.critical_errors++;
        }

        // Log the error
        if (error_handler.config.enable_logging) {
            ESP_LOG_LEVEL((esp_log_level_t)severity, TAG, 
                         "[%s][%s] %s:%d in %s() - %s (code: 0x%x)",
                         error_severity_names[severity],
                         error_category_names[category],
                         file, line, function,
                         description ? description : "No description",
                         error_code);
        }

        // Store error record if tracking is enabled
        if (error_handler.error_records && error_handler.config.enable_statistics) {
            uint32_t slot = find_error_slot();
            error_record_t* record = &error_handler.error_records[slot];
            
            // Check if this is a duplicate error
            bool is_duplicate = false;
            if (record->error_code == error_code && 
                record->category == category &&
                record->component == component &&
                record->function == function) {
                record->occurrence_count++;
                is_duplicate = true;
            } else {
                // New error record
                record->error_id = error_handler.total_errors;
                record->severity = severity;
                record->category = category;
                record->error_code = error_code;
                record->component = component;
                record->function = function;
                record->file = file;
                record->line = line;
                record->description = description;
                record->occurrence_count = 1;
                
                // Determine recovery action based on severity and category
                if (severity >= ERROR_SEVERITY_CRITICAL) {
                    if (category == ERROR_CATEGORY_MEMORY) {
                        record->recovery_action = RECOVERY_ACTION_SYSTEM_RESTART;
                    } else if (category == ERROR_CATEGORY_HARDWARE) {
                        record->recovery_action = RECOVERY_ACTION_RESET_COMPONENT;
                    } else {
                        record->recovery_action = RECOVERY_ACTION_RETRY;
                    }
                } else {
                    record->recovery_action = RECOVERY_ACTION_RETRY;
                }
            }
            
            record->timestamp = xTaskGetTickCount();
            
            // Attempt recovery for critical errors
            if (severity >= ERROR_SEVERITY_CRITICAL && !is_duplicate) {
                esp_err_t recovery_result = attempt_recovery(record);
                if (recovery_result != ESP_OK) {
                    ESP_LOGW(TAG, "Recovery failed for error 0x%x", error_code);
                }
            }
        }

        xSemaphoreGive(error_handler.mutex);
    }

    // Handle fatal errors
    if (severity == ERROR_SEVERITY_FATAL) {
        ESP_LOGE(TAG, "FATAL ERROR - System will restart");
        error_handler_print_report();
        esp_restart();
    }

    return ESP_OK;
}

esp_err_t error_handler_register_recovery(error_category_t category, recovery_function_t recovery_func) {
    if (!error_handler.initialized) {
        return ESP_ERR_INVALID_STATE;
    }

    if (category >= ERROR_CATEGORY_MAX || !recovery_func) {
        return ESP_ERR_INVALID_ARG;
    }

    error_handler.recovery_functions[category] = recovery_func;
    ESP_LOGI(TAG, "Registered custom recovery function for %s errors", 
             error_category_names[category]);

    return ESP_OK;
}

esp_err_t error_handler_get_stats(error_category_t category, uint32_t* count, error_record_t* last_error) {
    if (!error_handler.initialized) {
        return ESP_ERR_INVALID_STATE;
    }

    if (xSemaphoreTake(error_handler.mutex, pdMS_TO_TICKS(100)) == pdTRUE) {
        if (category == ERROR_CATEGORY_MAX) {
            // Return total error count
            if (count) *count = error_handler.total_errors;
        } else if (category < ERROR_CATEGORY_MAX) {
            if (count) *count = error_handler.error_count[category];
        } else {
            xSemaphoreGive(error_handler.mutex);
            return ESP_ERR_INVALID_ARG;
        }

        // Find last error for the category
        if (last_error && error_handler.error_records) {
            uint32_t latest_timestamp = 0;
            error_record_t* latest_record = NULL;
            
            for (uint32_t i = 0; i < error_handler.config.max_error_records; i++) {
                if (error_handler.error_records[i].error_id > 0 &&
                    (category == ERROR_CATEGORY_MAX || error_handler.error_records[i].category == category) &&
                    error_handler.error_records[i].timestamp > latest_timestamp) {
                    latest_timestamp = error_handler.error_records[i].timestamp;
                    latest_record = &error_handler.error_records[i];
                }
            }
            
            if (latest_record) {
                memcpy(last_error, latest_record, sizeof(error_record_t));
            } else {
                memset(last_error, 0, sizeof(error_record_t));
            }
        }

        xSemaphoreGive(error_handler.mutex);
    }

    return ESP_OK;
}

esp_err_t error_handler_clear_history(error_category_t category) {
    if (!error_handler.initialized) {
        return ESP_ERR_INVALID_STATE;
    }

    if (xSemaphoreTake(error_handler.mutex, pdMS_TO_TICKS(100)) == pdTRUE) {
        if (category == ERROR_CATEGORY_MAX) {
            // Clear all errors
            memset(error_handler.error_count, 0, sizeof(error_handler.error_count));
            error_handler.total_errors = 0;
            error_handler.critical_errors = 0;
            if (error_handler.error_records) {
                memset(error_handler.error_records, 0, 
                       error_handler.config.max_error_records * sizeof(error_record_t));
            }
        } else if (category < ERROR_CATEGORY_MAX) {
            // Clear specific category
            error_handler.error_count[category] = 0;
            if (error_handler.error_records) {
                for (uint32_t i = 0; i < error_handler.config.max_error_records; i++) {
                    if (error_handler.error_records[i].category == category) {
                        memset(&error_handler.error_records[i], 0, sizeof(error_record_t));
                    }
                }
            }
        } else {
            xSemaphoreGive(error_handler.mutex);
            return ESP_ERR_INVALID_ARG;
        }

        xSemaphoreGive(error_handler.mutex);
        ESP_LOGI(TAG, "Cleared error history for %s", 
                 category == ERROR_CATEGORY_MAX ? "ALL" : error_category_names[category]);
    }

    return ESP_OK;
}

void error_handler_print_report(void) {
    if (!error_handler.initialized) {
        ESP_LOGE(TAG, "Error handler not initialized");
        return;
    }

    ESP_LOGI(TAG, "=== Error Handler Report ===");
    ESP_LOGI(TAG, "Total errors: %u", error_handler.total_errors);
    ESP_LOGI(TAG, "Critical errors: %u", error_handler.critical_errors);

    for (int i = 0; i < ERROR_CATEGORY_MAX; i++) {
        if (error_handler.error_count[i] > 0) {
            ESP_LOGI(TAG, "%s errors: %u", error_category_names[i], error_handler.error_count[i]);
        }
    }

    // Print recent errors
    if (error_handler.error_records && xSemaphoreTake(error_handler.mutex, pdMS_TO_TICKS(100)) == pdTRUE) {
        ESP_LOGI(TAG, "Recent errors:");
        for (uint32_t i = 0; i < error_handler.config.max_error_records; i++) {
            if (error_handler.error_records[i].error_id > 0) {
                ESP_LOGI(TAG, "  [%u] %s/%s: %s (count: %u)",
                         error_handler.error_records[i].error_id,
                         error_severity_names[error_handler.error_records[i].severity],
                         error_category_names[error_handler.error_records[i].category],
                         error_handler.error_records[i].description,
                         error_handler.error_records[i].occurrence_count);
            }
        }
        xSemaphoreGive(error_handler.mutex);
    }
}

bool error_handler_should_restart(void) {
    if (!error_handler.initialized) {
        return false;
    }

    // Restart if too many critical errors
    return error_handler.critical_errors > 10;
}

esp_err_t error_handler_deinit(void) {
    if (!error_handler.initialized) {
        return ESP_OK; // Already deinitialized
    }

    ESP_LOGI(TAG, "Deinitializing error handler");

    // Free error records array if allocated
    if (error_handler.error_records) {
        SAFE_FREE(error_handler.error_records);
        error_handler.error_records = NULL;
    }

    // Delete mutex
    if (error_handler.mutex) {
        vSemaphoreDelete(error_handler.mutex);
        error_handler.mutex = NULL;
    }

    // Reset all fields
    memset(&error_handler, 0, sizeof(error_handler));
    error_handler.initialized = false;

    ESP_LOGI(TAG, "Error handler deinitialized successfully");
    return ESP_OK;
}
