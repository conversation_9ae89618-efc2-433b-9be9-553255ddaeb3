/*
 * SPDX-FileCopyrightText: 2022 Espressif Systems (Shanghai) CO LTD
 *
 * SPDX-License-Identifier: CC0-1.0
 */


#include "esp_log.h"
#include "bsp/ameter_s3.h"
#include "lvgl_monitor.h"  // Must be included after bsp header to override macros
#include "lvgl.h"
#include "ui/ui.h"

#include "driver/gpio.h"
#include "driver/i2c.h"
#include "driver/uart.h"

#include <stdio.h>
#include <freertos/FreeRTOS.h>
#include <freertos/task.h>
#include <pcf8563.h>
#include <string.h>
#include <esp_err.h>



#include "esp_adc/adc_oneshot.h"
#include "esp_adc/adc_continuous.h"
#include "esp_freertos_hooks.h"

#include "esp_system.h"
#include "nvs_flash.h"
#include "nvs.h"

#include "esp_io_expander_tca95xx_16bit.h"

//#include "mobus_futech.h"      // for modbus parameters structures
#include "bsp/modbus_params.h"  // for modbus parameters structures

#include "app_camera.hpp"
#include "app_face.hpp"

extern "C" {
#include "memory_manager.h"
#include "system_monitor.h"
#include "error_handler.h"
#include "camera_power_manager.h"
#include "cpu_monitor.h"
#include "lvgl_monitor.h"
#include "screen_monitor.h"

// C wrapper function for camera initialization
bool initialize_camera_hardware(void);
bool is_camera_object_available(void);
}

#include <math.h>
#include <sys/_stdint.h>
#include <sys/types.h>





#define TAG "AMETER-S3"

#pragma pack(push, 1)
typedef struct
{
	uint8_t valid;
	uint8_t faceid;
	uint8_t lv;
	char name[12]="Ameter";
} user_table_t;
#pragma pack(pop)

size_t name_len=12;


user_table_t user_table[11] = { 0 };
uint32_t cur_user=99;
uint32_t del_user=0;
uint32_t login_user=99;
uint32_t valid_users=0;

uint8_t theme[16] __attribute__((section(".ext_ram.data"))) = { 0 };

// Here are the user defined instances for device parameters packed by 1 byte
// These are keep the values that can be accessed from Modbus master
holding_reg_params_t holding_reg_params = { 0 };
input_reg_params_t input_reg_params; // Declared in header with PSRAM attribute, no initialization needed
coil_reg_params_t coil_reg_params = { 0 };
discrete_reg_params_t discrete_reg_params = { 0 };

holding_reg_params_slave_t holding_reg_params_slave;

uint32_t touched=0;
uint32_t touchedd=0;

nvs_handle_t nvs_handle_modbus;
bool mobus_slave_stop = true;

uint32_t modbus_master_run = 1;
uint32_t mb_slave_id=1;
uint32_t bound_value=0;
int32_t x_u_bound=0;
int32_t x_l_bound=0;
int32_t y_u_bound=0;
int32_t y_l_bound=0;
int32_t z_u_bound=0;
int32_t z_l_bound=0;
int32_t bound_value_u=0;
int32_t bound_value_l=0;
int32_t value_int=0;

int32_t board_init = 0; // value will default to 0, if not set yet in NVS
float adc_value=0;

uint32_t sleep_timer=0;

uint32_t tdata1=0;
uint32_t tdata2=0;
esp_io_expander_handle_t io_expander = NULL;
int32_t reading_byte=0;
uint8_t ameter_data=0;
unsigned char bit=0;
uint32_t gpio_value=0;
uint32_t io_num=0;
uint32_t gpio_num;

uint32_t clk1=2;
uint32_t clk2=2;

// Move string buffers to PSRAM to save internal RAM (use .ext_ram.data for initialized data)
char tool0[6] __attribute__((section(".ext_ram.data"))) = "0";
char tool1[6] __attribute__((section(".ext_ram.data"))) = "0";
char wheel[6] __attribute__((section(".ext_ram.data"))) = "0";
char amp[6] __attribute__((section(".ext_ram.data"))) = "0";
char log_buf[128] __attribute__((section(".ext_ram.data"))) = "0";

uint32_t tool_now=0;
uint32_t tool_now2=0;
uint32_t tool_set=0;
uint32_t tool_countdown=0;

int32_t xBias = 0;
int32_t yBias = 0;
int32_t zBias = 0;

static int adc_raw[3];

uint32_t default_screen=61;
uint32_t cur_screen=0;
uint32_t last_screen=0;
lv_obj_t * del_screen=NULL;
AppCamera *camera_p;
AppFace *face_p;

//uint32_t lvgl_run=0;
//uint32_t lvgl_last_run=0;

int32_t modbus_mode=1;
int32_t modbus_mode_nvs=0;

// Move password and license strings to PSRAM (use .ext_ram.data for initialized data)
char default_pw[12] __attribute__((section(".ext_ram.data"))) = "123456";
char factory_pw[12] __attribute__((section(".ext_ram.data"))) = "1qaz82";

char license_code[12] __attribute__((section(".ext_ram.data"))) = "123456";
uint16_t license_key=0;
uint16_t license_days=0;
uint16_t license_unlock=1;

char pw[12] __attribute__((section(".ext_ram.data"))) = "0";
int32_t int_pw;
int32_t log_index=0;
int32_t log_full=0;

int32_t colli_index=0;
uint8_t alarm_state = 0;

uint8_t permission_output = 0;

int screen_found=0;

char expire_date[12] __attribute__((section(".ext_ram.data"))) = "2099/09/09";

int baudrate=115200;

uint8_t abnormal_power = 0;

///////////////////////////////////////////////////////////////////////////////////
//#define LVGL_STACK_SIZE  64*1024

StaticTask_t TaskTCB_RTC;
StaticTask_t TaskTCB_ADC;
StaticTask_t TaskTCB_log;
StaticTask_t TaskTCB_MB;
//StaticTask_t TaskTCB_lvgl;

/* Buffer that the task being created will use as its stack. Note this is an array of
StackType_t variables. The size of StackType_t is dependent on the RTOS port.
Move task stacks to PSRAM to save internal RAM - INCREASED SIZES TO FIX STACK WARNINGS */
StackType_t xStackRTC[ 4096 ] __attribute__((section(".ext_ram.bss")));  // Increased from configMINIMAL_STACK_SIZE*4 to 4096
StackType_t xStackADC[ 4096 ] __attribute__((section(".ext_ram.bss")));  // Increased from 3072 to 4096
StackType_t xStacklog[ 4096 ] __attribute__((section(".ext_ram.bss")));  // Increased from 3072 to 4096
StackType_t xStackMB[ 4096 ] __attribute__((section(".ext_ram.bss")));
//StackType_t xStacklvgl[ LVGL_STACK_SIZE ];

TaskHandle_t xHandle_ADC = NULL;
TaskHandle_t xHandle_RTC = NULL;
TaskHandle_t xHandle_log = NULL;
TaskHandle_t xHandle_MB = NULL;
//TaskHandle_t xHandle_lvgl = NULL;


/*
typedef struct lvgl_port_ctx_s {
    SemaphoreHandle_t   lvgl_mux;
    esp_timer_handle_t  tick_timer;
    bool                running;
    int                 task_max_sleep_ms;
} lvgl_port_ctx_t;

lvgl_port_ctx_t lvgl_port_ctx;
 */


lv_chart_series_t * ui_X_Asix_series_1;
lv_chart_series_t * ui_X_Asix_series_2;
lv_chart_series_t * ui_X_Asix_series_3;



// setup datetime: 2020-04-03 12:35, Sunday
struct tm time2 = {
		.tm_sec  = 33,
		.tm_min  = 07,
		.tm_hour = 21,
		.tm_mday = 21,
		.tm_mon  = 07,   // months since January
		.tm_year = 101, // years since 1900
		.tm_wday = 0    // days since Sunday
};

struct tm time3 = {
		.tm_sec  = 33,
		.tm_min  = 07,
		.tm_hour = 21,
		.tm_mday = 21,
		.tm_mon  = 07,   // months since January
		.tm_year = 101, // years since 1900
		.tm_wday = 0    // days since Sunday
};

struct tm exp_time = {
		.tm_sec  = 00,
		.tm_min  = 00,
		.tm_hour = 00,
		.tm_mday = 21,
		.tm_mon  = 07,   // months since January 07=8
		.tm_year = 124, // years since 1900    123=2023
		.tm_wday = 0    // days since Sunday
};
uint16_t time_temp=0;

i2c_dev_t rtc_dev;
bool time_valid=false;
bool muted=false;

extern "C" {


void mb_slave(void);
void mb_master(void);
void master_write_func(uint16_t cid, int32_t value);
void master_operation_func(void *arg);
void setup_reg_data(void);
void destory_modbus_slave(void);
void SaveModbusDataToNVS(void);
void update_background(void);

unsigned char ameter_read_byte();

#define GPIO_INPUT_IO_0     BSP_EXT_INT //40
#define GPIO_INPUT_PIN_SEL  (1ULL<<GPIO_INPUT_IO_0)

#define GPIO_INPUT_IO_POWER     GPIO_NUM_10

//#define GPIO_INPUT_IO_1     CONFIG_GPIO_INPUT_1
//#define GPIO_INPUT_PIN_SEL  ((1ULL<<GPIO_INPUT_IO_0) | (1ULL<<GPIO_INPUT_IO_1))
/*
 * Let's say, GPIO_INPUT_IO_0=4, GPIO_INPUT_IO_1=5
 * In binary representation,
 * 1ULL<<GPIO_INPUT_IO_0 is equal to 0000000000000000000000000000000000010000 and
 * 1ULL<<GPIO_INPUT_IO_1 is equal to 0000000000000000000000000000000000100000
 * GPIO_INPUT_PIN_SEL                0000000000000000000000000000000000110000
 * */

#define ESP_INTR_FLAG_DEFAULT 0

static QueueHandle_t gpio_evt_queue = NULL;

static void gpio_task(void* arg){
	for (;;) {
		if (xQueueReceive(gpio_evt_queue, &io_num, portMAX_DELAY)) {
			printf("GPIO[%" PRIu32 "] intr, val: %d\n", io_num, gpio_get_level((gpio_num_t)io_num));
			//printf("GPIO[%"PRIu32"] intr \n", io_num);
//			if(io_num==10){
//				nvs_set_u8(nvs_handle_modbus, "abnormal_power", 1);
//				//ESP_LOGI(TAG, "Save nvs keys user %d log log_index=%d", cur_user, log_index);
//				log_index--;
//				nvs_set_u16(nvs_handle_modbus, nvs_key("h",(log_index*14)+109), time3.tm_year);
//				nvs_set_u16(nvs_handle_modbus, nvs_key("h",(log_index*14)+110), time3.tm_mon);
//				nvs_set_u16(nvs_handle_modbus, nvs_key("h",(log_index*14)+111), time3.tm_mday);
//				nvs_set_u16(nvs_handle_modbus, nvs_key("h",(log_index*14)+112), time3.tm_hour);
//				nvs_set_u16(nvs_handle_modbus, nvs_key("h",(log_index*14)+113), time3.tm_min);
//				nvs_commit(nvs_handle_modbus);
//				vTaskDelay(1);
//				log_index++;
//				ESP_LOGI(TAG, "abnormal power down log saved. log_index=%d adc_value=%f",log_index, adc_value);
//			}
			// 0 en low
			// 1 clk
			// 2 d1
			// 3 d2
			esp_io_expander_get_level(io_expander, IO_EXPANDER_PIN_NUM_0, &gpio_value);
			esp_io_expander_get_level(io_expander, IO_EXPANDER_PIN_NUM_1, &clk1);
			//ESP_LOGI(TAG, "En pin value =%d",value);
			//ESP_LOGI(TAG, "clk1 =%d clk2 =%d",clk1, clk2);

			//esp_io_expander_get_level(io_expander, IO_EXPANDER_PIN_NUM_2, &value);
			//ESP_LOGI(TAG, "d1 =%d",value);
			//esp_io_expander_get_level(io_expander, IO_EXPANDER_PIN_NUM_3, &value);
			//ESP_LOGI(TAG, "d2 =%d",value);
			if( gpio_value == 0 ){
				if(clk1 != clk2){
					ameter_read_byte();
				}
				touchedd = 1;
				//if( bit == 7 ){
				//tdata1 = tdata1 >> 1;
				//tdata2 = tdata2 >> 1;
				//ESP_LOGI(TAG, "ameter_data d1=%d",tdata1);
				//ESP_LOGI(TAG, "ameter_data d2=%d",tdata2);
				//	bit=0;
				//	tdata1 = tdata2 = 0;
				//	clk2 = clk1 = 2;
				//}
			}
			else if( gpio_value == 1 && clk1 == clk2  && bit > 1){
			//if(  clk1 == clk2  && bit > 2){
				//tdata1=reverse(tdata1);
				tdata1 = tdata1 >> (32-bit);
				tdata2 = tdata2 >> (32-bit);
				//ESP_LOGI(TAG, "ameter_data d1=%d d1=%x d2=%d d1=%x bits=%d",tdata1,tdata1,tdata2,tdata2, bit);
				sprintf(tool0, "%05d", tdata1);
				sprintf(tool1, "%05d", tdata2);
				while(!bsp_display_lock(1000)){bsp_display_unlock();}
				lv_label_set_text(ui_Lab_Tool_No, tool1);
				lv_label_set_text(ui_Lab_Standby_tool_No, tool0);
				lv_label_set_text(ui_Lab_Tool_No1, tool1);
				lv_label_set_text(ui_Lab_Standby_tool_No1, tool0);
				lv_label_set_text(ui_Lab_Tool_No2, tool1);
				lv_label_set_text(ui_Lab_Standby_tool_No2, tool0);
				bsp_display_unlock();

				bit=0;
				tdata1 = tdata2 = 0;
				clk2 = clk1 = 2;
			}
			clk2 = clk1;
			//ESP_LOGI(TAG, "bit=%d tdata1 =%d", bit, tdata1);
			//ESP_LOGI(TAG, "bit=%d tdata2 =%d", bit, tdata2);
		}
	}
}

static void IRAM_ATTR gpio_isr_handler(void* arg){
	gpio_num = (uint32_t) arg;
	xQueueSendFromISR(gpio_evt_queue, &gpio_num, NULL);
}

static void IRAM_ATTR gpio_isr_handler2(void* arg){
	gpio_num = (uint32_t) arg;
	xQueueSendFromISR(gpio_evt_queue, &gpio_num, NULL);
}

/*******************************************************************************
 * Private functions
 *******************************************************************************/
 
 const char base32_chars[] = "Y3FGHJL45ZCDPQABX2T6K7RSUVEIOWNM";

void decode_license(const char *license_code, uint16_t *days, uint16_t *key) {
    uint32_t combined = 0;
    for (int i = 0; i < 8; i++) {
        const char *ptr = strchr(base32_chars, license_code[i]);
        if (ptr) {
            combined = combined * 32 + (ptr - base32_chars);
        }
    }
    *days = combined / 10000;
    *key = combined % 10000;
}


void log_mem_info(void){
	static char buffer[192];    /* Make sure buffer is enough for `sprintf` */
	int i=0;
	int j=1;

	while (1) {
		/**
		 * It's not recommended to frequently use functions like `heap_caps_get_free_size()` to obtain memory information
		 * in practical applications, especially when the application extensively uses `malloc()` to dynamically allocate
		 * a significant number of memory blocks. The frequent interrupt disabling may potentially lead to issues with other functionalities.
		 */
		sprintf(buffer,"   Biggest /     Free /    Total\n"
				"\t    SRAM : [%8d / %8d / %8d]\n"
				"\t   PSRAM : [%8d / %8d / %8d]\n"
				"\t Default : [%8d / %8d / %8d]\n",
				heap_caps_get_largest_free_block(MALLOC_CAP_INTERNAL),
				heap_caps_get_free_size(MALLOC_CAP_INTERNAL),
				heap_caps_get_total_size(MALLOC_CAP_INTERNAL),
				heap_caps_get_largest_free_block(MALLOC_CAP_SPIRAM),
				heap_caps_get_free_size(MALLOC_CAP_SPIRAM),
				heap_caps_get_total_size(MALLOC_CAP_SPIRAM),
				heap_caps_get_largest_free_block(MALLOC_CAP_DEFAULT),//MALLOC_CAP_DEFAULT
				heap_caps_get_free_size(MALLOC_CAP_DEFAULT),
				heap_caps_get_total_size(MALLOC_CAP_DEFAULT));
		ESP_LOGI("MEM", "%s", buffer);

		ESP_LOGI(TAG, "checking.........last=%d cur=%d",last_screen ,cur_screen);
		
		ESP_LOGI(TAG, "time3 %04d-%02d-%02d %02d:%02d:%02d, %s", time3.tm_year + 1900, time3.tm_mon + 1,time3.tm_mday, time3.tm_hour, time3.tm_min, time3.tm_sec, time_valid ? "VALID" : "NOT VALID");
		//vTaskDelay(pdMS_TO_TICKS(50000));

		vTaskDelay(pdMS_TO_TICKS(1000));
		

		//test
//		esp_io_expander_set_level(io_expander, (1ULL << 11), i); // led off
//		esp_io_expander_set_level(io_expander, (1ULL << 10), j); // led off
//		esp_io_expander_set_level(io_expander, (1ULL << 9), i); // led off
//		esp_io_expander_set_level(io_expander, (1ULL << 8), j); // led off
//		i=(!i);
//		j=(!j);
	}

	ESP_LOGI(TAG, "+++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++");
	ESP_LOGI(TAG, "EXIT log_mem_info");
	vTaskDelete(NULL);
}

}


// *INDENT-OFF*
void app_lvgl_display(void)
{
	bsp_display_lock(0);

	ui_init();

	bsp_display_unlock();
}

void beep(void){
	if(muted)
		return;
	gpio_set_level(BSP_SPEAKER,0);
	gpio_set_level(BSP_SPEAKER,1);
	vTaskDelay(100 / portTICK_PERIOD_MS);
	gpio_set_level(BSP_SPEAKER,0);
}

static lv_disp_t *lvgl_disp = NULL;
static lv_indev_t *lvgl_touch_indev = NULL;
static esp_lcd_touch_handle_t touch_handle;

static QueueHandle_t xQueueFrame_0 = xQueueCreate(3, sizeof(camera_fb_t *));
//static QueueHandle_t xQueueFrame_1 = xQueueCreate(4, sizeof(camera_fb_t *));
//static QueueHandle_t xQueueFrame_2 = xQueueCreate(2, sizeof(camera_fb_t *));

void pcf8563(void *pvParameters)
{
	memset(&rtc_dev, 0, sizeof(i2c_dev_t));

	ESP_LOGI(TAG, "Start to read RTC pcf8563");
	//speaker
	ESP_LOGI(TAG, "Beep for a while when start?..............");
	//gpio_pad_select_gpio(BSP_SPEAKER);  //esp_rom_gpio_pad_select_gpio
	gpio_set_direction(BSP_SPEAKER, GPIO_MODE_OUTPUT);
    beep();

	ESP_ERROR_CHECK(pcf8563_init_desc(&rtc_dev, I2C_NUM_1, BSP_I2C1_SDA, BSP_I2C1_SCL));

	// setup datetime: 2020-04-03 12:35, Sunday
	struct tm time = {
			.tm_sec  = 54,
			.tm_min  = 43,
			.tm_hour = 21,
			.tm_mday = 17,
			.tm_mon  = 05,   // months since January
			.tm_year = 124, // years since 1900
			.tm_wday = 0    // days since Sunday
	};

	while (1)
	{
		esp_err_t r = pcf8563_get_time(&rtc_dev, &time, &time_valid);
		if (r == ESP_OK){
			//ESP_LOGI(TAG, "%04d-%02d-%02d %02d:%02d:%02d, %s\n", time.tm_year + 1900, time.tm_mon + 1,time.tm_mday, time.tm_hour, time.tm_min, time.tm_sec, time_valid ? "VALID" : "NOT VALID");
			time3=time;
			//ESP_LOGI(TAG, "time3 %04d-%02d-%02d %02d:%02d:%02d, %s\n", time3.tm_year + 1900, time3.tm_mon + 1,time3.tm_mday, time3.tm_hour, time3.tm_min, time3.tm_sec, time_valid ? "VALID" : "NOT VALID");
			//lv_label_set_text_fmt(perf_label, "%04d-%02d-%02d %02d:%02d:%02d, %s", time.tm_year + 1900, time.tm_mon + 1,time.tm_mday, time.tm_hour, time.tm_min, time.tm_sec, time_valid ? "VALID" : "NOT VALID");
			if(time_valid){
				holding_reg_params_slave.holding_data2 = time.tm_year+1900;	//year
				holding_reg_params_slave.holding_data4 = time.tm_mon+1;	//month
				holding_reg_params_slave.holding_data5 = time.tm_mday;	//day
				holding_reg_params_slave.holding_data6 = time.tm_hour;	//hour
				holding_reg_params_slave.holding_data7 = time.tm_min;	//min
				holding_reg_params_slave.holding_data8 = time.tm_sec;	//second
			}
		}
		else
			ESP_LOGE(TAG, "Error %d: %s\n", r, esp_err_to_name(r));
		vTaskDelay(pdMS_TO_TICKS(980));
		if( permission_output != holding_reg_params_slave.holding_data15 && login_user!=99){
			esp_io_expander_set_level(io_expander, (1ULL << 12), 0); // output off
			esp_io_expander_set_level(io_expander, (1ULL << 13), 0); // output off
			esp_io_expander_set_level(io_expander, (1ULL << 14), 0); // output off
			esp_io_expander_set_level(io_expander, (1ULL << 15), 0); // output off
			while(!bsp_display_lock(1000)){bsp_display_unlock();}
			lv_obj_add_state(ui_BTN_Set_Time, LV_STATE_DISABLED);
			lv_obj_add_state(ui_BTN_Set_ModBusID, LV_STATE_DISABLED);
			lv_obj_add_state(ui_BTN_Set_ModBus_Mode, LV_STATE_DISABLED);
			lv_obj_add_state(ui_BTN_Set_PWD, LV_STATE_DISABLED);
			bsp_display_unlock();
			if( holding_reg_params_slave.holding_data15 == 1){
				esp_io_expander_set_level(io_expander, (1ULL << (16-user_table[login_user].lv)), 1); // light level led
				//esp_io_expander_set_level(io_expander, (1ULL << 11), 0);
			}
			else{
				if(user_table[login_user].lv == 1){
					esp_io_expander_set_level(io_expander, (1ULL << 12), 1);
					esp_io_expander_set_level(io_expander, (1ULL << 13), 1);
				}
				else if(user_table[login_user].lv == 2){
					esp_io_expander_set_level(io_expander, (1ULL << 12), 0);
					esp_io_expander_set_level(io_expander, (1ULL << 13), 1);
				}
				else if(user_table[login_user].lv == 3){
					esp_io_expander_set_level(io_expander, (1ULL << 12), 1);
					esp_io_expander_set_level(io_expander, (1ULL << 13), 0);
				}
				else if(user_table[login_user].lv == 4){
					esp_io_expander_set_level(io_expander, (1ULL << 12), 0);
					esp_io_expander_set_level(io_expander, (1ULL << 13), 0);
					while(!bsp_display_lock(1000)){bsp_display_unlock();}
					lv_obj_clear_state(ui_BTN_Set_Time, LV_STATE_DISABLED);
					lv_obj_clear_state(ui_BTN_Set_ModBusID, LV_STATE_DISABLED);
					lv_obj_clear_state(ui_BTN_Set_ModBus_Mode, LV_STATE_DISABLED);
					lv_obj_clear_state(ui_BTN_Set_PWD, LV_STATE_DISABLED);
					bsp_display_unlock();
				}
			}
			permission_output = holding_reg_params_slave.holding_data15;
		}
	}

	ESP_LOGI(TAG, "EXIT pcf8563");
	vTaskDelete(NULL);
}

void GetInitializedNVS(){
	esp_err_t err;
	ESP_LOGI(TAG, "GetInitializedNVS data................");
	for(cur_user=0; cur_user<10; cur_user++){
		vTaskDelay(2);
		err = nvs_get_u8(nvs_handle_modbus, nvs_key("valid",cur_user), &user_table[cur_user].valid);
		switch (err) {
		case ESP_OK:
			//board_init = 1;
			//ESP_LOGI(TAG, "Modbus Data Read OK.....\n");
			//ESP_LOGI(TAG, "board_init = %d\n", board_init);
			break;
		case ESP_ERR_NVS_NOT_FOUND:
			ESP_LOGI(TAG, "The Modbus Data is not initialized yet!\n");
			//board_init = 0;
			return;
			break;
		default :
			ESP_LOGI(TAG, "Error (%s) reading!\n", esp_err_to_name(err));
		}
		if(err==ESP_OK){
			if( user_table[cur_user].valid==1){
				nvs_get_u8(nvs_handle_modbus, nvs_key("lv",cur_user), &user_table[cur_user].lv);
				vTaskDelay(2);
				nvs_get_u8(nvs_handle_modbus, nvs_key("faceid",cur_user), &user_table[cur_user].faceid);
				vTaskDelay(2);
				nvs_get_str(nvs_handle_modbus, nvs_key("name",cur_user), user_table[cur_user].name,&name_len);
				vTaskDelay(2);
				ESP_LOGI(TAG, "NVS user=%d lv=%d faceid=%d name=%s", cur_user, user_table[cur_user].lv, user_table[cur_user].faceid, user_table[cur_user].name );
			}
			else{
				user_table[cur_user].valid=0;
				user_table[cur_user].lv=0;
				user_table[cur_user].faceid=0;
			}
		}
		else{
			user_table[cur_user].valid=0;
			user_table[cur_user].lv=0;
			user_table[cur_user].faceid=0;
		}
	}
}

void UpdateUserTable(){
	ESP_LOGI(TAG, "Update User Table.....................");
	valid_users=0;
	for(int user_temp=0; user_temp<7; user_temp++){
		//nvs_set_u8(nvs_handle_modbus, nvs_key("valid",cur_user), user_table[cur_user].valid);

		if( user_table[user_temp].valid==1){
			nvs_set_u8(nvs_handle_modbus, nvs_key("lv",user_temp), user_table[user_temp].lv);
			//nvs_set_u8(nvs_handle_modbus, nvs_key("faceid",user_temp), user_table[user_temp].faceid);
			vTaskDelay(1);
			nvs_commit(nvs_handle_modbus);
			vTaskDelay(2);
			ESP_LOGI(TAG, "Updating NVS user=%d lv=%d faceid=%d name=%s", user_temp, user_table[user_temp].lv, user_table[user_temp].faceid, user_table[user_temp].name );
			//set user settings
			//71
			while(!bsp_display_lock(1000)){bsp_display_unlock();}
			lv_label_set_text_fmt(ui_comp_get_child(ui_PL_del_ID1, 5*user_temp+5 ),"%d",user_table[user_temp].lv);
			lv_label_set_text_fmt(ui_comp_get_child(ui_PL_del_ID1, 5*user_temp+3 ),"%s(%d)",user_table[user_temp].name,user_table[user_temp].faceid);
			vTaskDelay(2);
			bsp_display_unlock();

			valid_users++;
		}
		else{
			while(!bsp_display_lock(1000)){bsp_display_unlock();}
			lv_label_set_text(ui_comp_get_child(ui_PL_del_ID1, 5*user_temp+5 ),"N/A");
			lv_label_set_text(ui_comp_get_child(ui_PL_del_ID1, 5*user_temp+3 ),"N/A");
			vTaskDelay(2);
			bsp_display_unlock();
			nvs_set_u8(nvs_handle_modbus, nvs_key("valid",cur_user), user_table[cur_user].valid);
		}
	}
	if( 0 ){
	//if( valid_users == 0 && board_init==1 ){
		board_init=0;
		nvs_set_i32(nvs_handle_modbus, "board_init", board_init);
		vTaskDelay(10);
		nvs_commit(nvs_handle_modbus);
		vTaskDelay(10);
		login_user=99;
		cur_user=99;
		esp_io_expander_set_level(io_expander, (1ULL << 11), 0); // led off
		esp_io_expander_set_level(io_expander, (1ULL << 10), 0); // led off
		esp_io_expander_set_level(io_expander, (1ULL << 9), 0); // led off
		esp_io_expander_set_level(io_expander, (1ULL << 8), 0); // led off
		esp_io_expander_set_level(io_expander, (1ULL << 7), 0); // led off
		esp_io_expander_set_level(io_expander, (1ULL << 6), 0); // led off
		esp_io_expander_set_level(io_expander, (1ULL << 5), 0); // led off
		esp_io_expander_set_level(io_expander, (1ULL << 4), 0); // led off
		esp_io_expander_set_level(io_expander, (1ULL << 12), 0); // output off
		esp_io_expander_set_level(io_expander, (1ULL << 13), 0); // output off
		esp_io_expander_set_level(io_expander, (1ULL << 14), 0); // output off
		esp_io_expander_set_level(io_expander, (1ULL << 15), 0); // output off
		ESP_LOGW(TAG, "Restarting to Screen 1...............................");
		while(!bsp_display_lock(1000)){bsp_display_unlock();}
		cur_screen = 1;
		//_ui_screen_change(&ui_Screen1, LV_SCR_LOAD_ANIM_FADE_ON, 1100, 10, &ui_Screen1_screen_init);
		vTaskDelay(100);
		bsp_display_unlock();

	}
	else if( valid_users > 0 && board_init==0 ){
		ESP_LOGI(TAG, "First initializing done.");
		board_init = 1;
		ESP_LOGI(TAG, "Committing board_init in NVS ... ");
		nvs_set_i32(nvs_handle_modbus, "board_init", board_init);
		vTaskDelay(10);
		nvs_commit(nvs_handle_modbus);
		vTaskDelay(20);
	}
	ESP_LOGI(TAG, "UpdateUserTable exit...................");
	//nvs_commit(nvs_handle_modbus);
}

void ScreenChangeEvent(void *pvParameters){
	ESP_LOGI(TAG, "Checking Screen Change Events................................");
	esp_err_t err;
	uint32_t i=0;
	/////////////////////////////////////////////////////////////////////////////////////
	while(1){
		// Screen change event.................
		if( cur_screen != last_screen ){
			ESP_LOGI(TAG, "last=%d cur=%d screen_found=%d",last_screen ,cur_screen, screen_found);

			// Handle camera power management for screen change
			camera_power_handle_screen_change(cur_screen, last_screen);

			// Camera power is now managed automatically by camera_power_manager
			// Only handle face recognition state changes
			if( last_screen==70 || last_screen==80){
				ESP_LOGI(TAG, "Stop Face Recognition.......from recongnize (login)");
				face_p->state = FACE_IDLE;
				face_p->stop();
				vTaskDelay(150); // Increased delay to ensure face task fully stops
				// Also stop camera task to prevent frame capture failures
				camera_p->stop();
				vTaskDelay(100); // Increased delay to ensure camera task fully stops
				// Additional delay to ensure all tasks are completely stopped before power management
				vTaskDelay(50);
				if( cur_screen==701 ){
					while(!bsp_display_lock(1000)){bsp_display_unlock();}
					lv_label_set_text(ui_Label3, "Logout");
					_ui_screen_change(&ui_SC_1_1, LV_SCR_LOAD_ANIM_FADE_ON, 1500, 1500, &ui_SC_1_1_screen_init);
					bsp_display_unlock();
					beep();
					cur_screen=11;
					//if( user_table[login_user].lv > 0 ){
					ESP_LOGI(TAG, "Stop Camera.......user=%d lv=%d", login_user, user_table[login_user].lv);
					esp_io_expander_set_level(io_expander, (1ULL << (12-user_table[login_user].lv)), 1); // light level led
					esp_io_expander_set_level(io_expander, (1ULL << (8-user_table[login_user].lv)), 1); // light level led
					//}
					if( holding_reg_params_slave.holding_data15 == 1){
						ESP_LOGI(TAG, "Set led light holding_data15=%d", holding_reg_params_slave.holding_data15);
						esp_io_expander_set_level(io_expander, (1ULL << (16-user_table[login_user].lv)), 1); // light level led
						//esp_io_expander_set_level(io_expander, (1ULL << 11), 0);
						if(user_table[login_user].lv == 4){
							while(!bsp_display_lock(1000)){bsp_display_unlock();}
							if(license_unlock==1){
								lv_obj_clear_state(ui_BTN_Set_Time, LV_STATE_DISABLED);
							}
							lv_obj_clear_state(ui_BTN_Set_ModBusID, LV_STATE_DISABLED);
							lv_obj_clear_state(ui_BTN_Set_ModBus_Mode, LV_STATE_DISABLED);
							lv_obj_clear_state(ui_BTN_Set_PWD, LV_STATE_DISABLED);
							bsp_display_unlock();
						}
					}
					else{
						ESP_LOGI(TAG, "Set led light holding_data15=%d", holding_reg_params_slave.holding_data15);
						if(user_table[login_user].lv == 1){
							esp_io_expander_set_level(io_expander, (1ULL << 12), 1);
							esp_io_expander_set_level(io_expander, (1ULL << 13), 1);
						}
						else if(user_table[login_user].lv == 2){
							esp_io_expander_set_level(io_expander, (1ULL << 12), 0);
							esp_io_expander_set_level(io_expander, (1ULL << 13), 1);
						}
						else if(user_table[login_user].lv == 3){
							esp_io_expander_set_level(io_expander, (1ULL << 12), 1);
							esp_io_expander_set_level(io_expander, (1ULL << 13), 0);
						}
						else if(user_table[login_user].lv == 4){
							esp_io_expander_set_level(io_expander, (1ULL << 12), 0);
							esp_io_expander_set_level(io_expander, (1ULL << 13), 0);
							while(!bsp_display_lock(1000)){bsp_display_unlock();}
							if(license_unlock==1){
								lv_obj_clear_state(ui_BTN_Set_Time, LV_STATE_DISABLED);
							}
							lv_obj_clear_state(ui_BTN_Set_ModBusID, LV_STATE_DISABLED);
							lv_obj_clear_state(ui_BTN_Set_ModBus_Mode, LV_STATE_DISABLED);
							lv_obj_clear_state(ui_BTN_Set_PWD, LV_STATE_DISABLED);
							bsp_display_unlock();
						}
					}
				}
				vTaskDelay(10);
			}
			else if( cur_screen == 121 ){
				ESP_LOGI(TAG, "Stop Camera.......from enrolled");
				beep();
				face_p->state = FACE_IDLE;
				face_p->stop();
				vTaskDelay(110);
				camera_p->stop();
				vTaskDelay(50);
			}
			else if( cur_screen == 122 ){
				ESP_LOGI(TAG, "Save from enrolled..");
				ESP_LOGI(TAG, "name=%s lv=%d",user_table[cur_user].name ,user_table[cur_user].lv);
				beep();
                //////////////////
                ESP_LOGI(TAG, "Check nvs keys %s", nvs_key("valid",cur_user));
                nvs_set_u8(nvs_handle_modbus, nvs_key("valid",cur_user), user_table[cur_user].valid);
                vTaskDelay(5);
                nvs_set_u8(nvs_handle_modbus, nvs_key("lv",cur_user), user_table[cur_user].lv);
                vTaskDelay(5);
                nvs_set_u8(nvs_handle_modbus, nvs_key("faceid",cur_user), user_table[cur_user].faceid);
                vTaskDelay(5);
                nvs_set_str(nvs_handle_modbus, nvs_key("name",cur_user), user_table[cur_user].name);
                vTaskDelay(5);
                nvs_commit(nvs_handle_modbus);
                vTaskDelay(5);
                ESP_LOGI(TAG, "nvs keys comitted.");
                //////////////////////////////////////////////////
                ESP_LOGI(TAG, "UpdateUserTable 122");
				UpdateUserTable();
				vTaskDelay(10);
				//last_screen = 122;

				cur_screen = 12;
			}
			else if( cur_screen == 123 ){
				ESP_LOGI(TAG, "Cancel from enrolled.....");
				face_p->del_user();
				beep();
				cur_screen = 12;
			}
			else if( last_screen==711 && cur_screen!=72){
				ESP_LOGI(TAG, "Stop Camera.......from enroll exit btn");
				face_p->state = FACE_IDLE;
				face_p->stop();
				vTaskDelay(110);
				camera_p->stop();
				vTaskDelay(50);
			}
			else if( last_screen==72){
				ESP_LOGI(TAG, "Stop Camera.......from enroll exit btn");
				face_p->state = FACE_IDLE;
				face_p->stop();
				vTaskDelay(110);
				camera_p->stop();
				vTaskDelay(50);
			}
			else if( cur_screen==70 ){
				ESP_LOGI(TAG, "Camera... FACE_RECOGNIZE...");
				cur_user = 10;
				// Camera power is now managed automatically by camera_power_manager
				// Wait for camera to be fully ready after power management

				// Check if camera is ready before starting face recognition
				camera_power_state_t power_state = camera_power_get_state();
				if (power_state == CAMERA_POWER_ON) {
					// Camera is already powered, but give it extra time to stabilize
					ESP_LOGI(TAG, "Camera already powered, allowing stabilization time");
					vTaskDelay(50); // Extra stabilization time for reliable operation
					camera_p->run();
					vTaskDelay(20); // Increased delay for task creation
					face_p->state = FACE_RECOGNIZE;
					face_p->run();
					vTaskDelay(10);
				} else {
					// Camera not powered yet, wait for power management to complete
					ESP_LOGW(TAG, "Camera not ready, waiting for power management...");
					vTaskDelay(150); // Increased wait time for power management
					power_state = camera_power_get_state();
					if (power_state == CAMERA_POWER_ON) {
						// Additional stabilization time after power on
						vTaskDelay(50);
						camera_p->run();
						vTaskDelay(20);
						face_p->state = FACE_RECOGNIZE;
						face_p->run();
						vTaskDelay(10);
					} else {
						ESP_LOGE(TAG, "Camera failed to power on for face recognition");
					}
				}
			}
			else if( cur_screen==711 ){
				ESP_LOGI(TAG, "Camera... before FACE_ENROLL");
				//if(!strcmp(pw, default_pw)){
					//lv_obj_add_flag(ui_PL_Admin_PWD, LV_OBJ_FLAG_HIDDEN);
					//ESP_LOGI(TAG, "Password Matched.......");
					beep();
					//_ui_screen_change(&ui_SC_Face_Rec, LV_SCR_LOAD_ANIM_FADE_ON, 1100, 300, &ui_SC_Face_Rec_screen_init);
					// Camera power is now managed automatically by camera_power_manager
					vTaskDelay(200); // Wait for camera power to stabilize and initialize

					// Check if camera is ready
					camera_power_state_t power_state = camera_power_get_state();
					if (power_state == CAMERA_POWER_ON) {
						camera_p->run();
						face_p->state = FACE_IDLE;
						vTaskDelay(20);
						face_p->run();
					} else {
						ESP_LOGW(TAG, "Camera not ready for admin mode");
					}
					lv_obj_add_flag(ui_PL_Admin_PWD, LV_OBJ_FLAG_HIDDEN);
					vTaskDelay(10);
					strcpy(pw,"");
				//}
			}
			else if( cur_screen==77 ){
				ESP_LOGI(TAG, "Camera... FACE_DELETE cur_user=%d", cur_user);
				lv_obj_clear_flag(ui_PL_Admin_PWD, LV_OBJ_FLAG_HIDDEN);
			}
			else if( cur_screen==72 ){
				ESP_LOGI(TAG, "Camera... FACE_ENROLL");
				// Camera should already be powered by auto management
				// Start camera and face recognition tasks for enrollment
				camera_power_state_t power_state = camera_power_get_state();
				if (power_state == CAMERA_POWER_ON) {
					camera_p->run();
					vTaskDelay(10);
					face_p->state = FACE_ENROLL; //FACE_ENROLL FACE_DELETE
					face_p->run();
					vTaskDelay(5);
				} else {
					ESP_LOGW(TAG, "Camera not ready for face enrollment");
				}
			}
			else if( last_screen==11 && cur_screen==1 ){
				ESP_LOGI(TAG, "Logout...........");
				beep();
				esp_io_expander_set_level(io_expander, (1ULL << 11), 0); // led off
				esp_io_expander_set_level(io_expander, (1ULL << 10), 0); // led off
				esp_io_expander_set_level(io_expander, (1ULL << 9), 0); // led off
				esp_io_expander_set_level(io_expander, (1ULL << 8), 0); // led off
				esp_io_expander_set_level(io_expander, (1ULL << 7), 0); // led off
				esp_io_expander_set_level(io_expander, (1ULL << 6), 0); // led off
				esp_io_expander_set_level(io_expander, (1ULL << 5), 0); // led off
				esp_io_expander_set_level(io_expander, (1ULL << 4), 0); // led off
				ESP_LOGI(TAG, "Save nvs keys user %d log log_index=%d", cur_user, log_index);
				log_index--;
				nvs_set_u16(nvs_handle_modbus, nvs_key("h",(log_index*14)+109), time3.tm_year+1900);
				nvs_set_u16(nvs_handle_modbus, nvs_key("h",(log_index*14)+110), time3.tm_mon+1);
				nvs_set_u16(nvs_handle_modbus, nvs_key("h",(log_index*14)+111), time3.tm_mday);
				nvs_set_u16(nvs_handle_modbus, nvs_key("h",(log_index*14)+112), time3.tm_hour);
				nvs_set_u16(nvs_handle_modbus, nvs_key("h",(log_index*14)+113), time3.tm_min);
				*(&holding_reg_params_slave.holding_data109+(log_index*14)) = time3.tm_year+1900;
				*(&holding_reg_params_slave.holding_data110+(log_index*14)) = time3.tm_mon+1;
				*(&holding_reg_params_slave.holding_data111+(log_index*14)) = time3.tm_mday;
				*(&holding_reg_params_slave.holding_data112+(log_index*14)) = time3.tm_hour;
				*(&holding_reg_params_slave.holding_data113+(log_index*14)) = time3.tm_min;
				log_index++;
				nvs_commit(nvs_handle_modbus);
				ESP_LOGI(TAG, "nvs keys comitted. log_index=%d",log_index);
				//////////////////////////////////////////////////
				while(!bsp_display_lock(1000)){bsp_display_unlock();}
				lv_label_set_text(ui_Label3, "Login");
			    lv_obj_set_style_bg_color(ui_BTN_login, lv_color_hex(0x42DD5D), LV_PART_MAIN | LV_STATE_DEFAULT);
			    lv_obj_set_style_bg_opa(ui_BTN_login, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
			    lv_obj_set_style_bg_grad_color(ui_BTN_login, lv_color_hex(0x92F25C), LV_PART_MAIN | LV_STATE_DEFAULT);
			    lv_obj_set_style_bg_main_stop(ui_BTN_login, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
			    lv_obj_set_style_bg_grad_stop(ui_BTN_login, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
			    lv_obj_set_style_bg_grad_dir(ui_BTN_login, LV_GRAD_DIR_HOR, LV_PART_MAIN | LV_STATE_DEFAULT);
				bsp_display_unlock();
				login_user=99;
				cur_user=0;
				vTaskDelay(20);
				esp_restart();
			}
			else if( cur_screen==11  ){
				ESP_LOGI(TAG, "Update colors? clickable?");
				while(!bsp_display_lock(1000)){bsp_display_unlock();}
				lv_obj_add_state(ui_BTN_EvnClear, LV_STATE_FOCUSED);
				bsp_display_unlock();
			}
			else if( cur_screen==12  ){
				ESP_LOGI(TAG, "UpdateUserTable 12");
				UpdateUserTable();
				vTaskDelay(10);
			}
			else if( cur_screen==3201 ){
				ESP_LOGI(TAG, "Set X up bound values to modbus slave.....%d",bound_value);
				while(!bsp_display_lock(1000)){bsp_display_unlock();}
				lv_label_set_text_fmt(ui_X_UP_bon_Value1,"%d",bound_value);
				master_write_func(16, bound_value);
				vTaskDelay(2);
				bsp_display_unlock();

				last_screen=3201;
				cur_screen=320;
			}
			else if( cur_screen==3202 ){
				bound_value = 0-bound_value;
				ESP_LOGI(TAG, "Set X low bound values to modbus slave.....%d",bound_value);
				while(!bsp_display_lock(1000)){bsp_display_unlock();}
				lv_label_set_text_fmt(ui_X_low_bon_Value1,"%d",bound_value);
				master_write_func(19, bound_value);
				vTaskDelay(2);
				bsp_display_unlock();

				last_screen=3202;
				cur_screen=320;
			}
			else if( cur_screen==3211 ){
				ESP_LOGI(TAG, "Set Y up bound values to modbus slave.....%d",bound_value);
				while(!bsp_display_lock(1000)){bsp_display_unlock();}
				lv_label_set_text_fmt(ui_X_UP_bon_Value1,"%d",bound_value);
				master_write_func(17, bound_value);
				vTaskDelay(2);
				bsp_display_unlock();

				last_screen=3211;
				cur_screen=321;
			}
			else if( cur_screen==3212 ){
				bound_value = 0-bound_value;
				ESP_LOGI(TAG, "Set Y low bound values to modbus slave.....%d",bound_value);
				while(!bsp_display_lock(1000)){bsp_display_unlock();}
				lv_label_set_text_fmt(ui_X_low_bon_Value1,"%d",bound_value);
				master_write_func(20, bound_value);
				vTaskDelay(2);
				bsp_display_unlock();
				last_screen=3212;
				cur_screen=321;
			}
			else if( cur_screen==3221 ){
				ESP_LOGI(TAG, "Set Z up bound values to modbus slave.....%d",bound_value);
				while(!bsp_display_lock(1000)){bsp_display_unlock();}
				lv_label_set_text_fmt(ui_X_UP_bon_Value1,"%d",bound_value);
				master_write_func(18, bound_value);
				vTaskDelay(2);
				bsp_display_unlock();
				last_screen=3221;
				cur_screen=322;
			}
			else if( cur_screen==3222 ){
				bound_value = 0-bound_value;
				ESP_LOGI(TAG, "Set Z low bound values to modbus slave.....%d",bound_value);
				while(!bsp_display_lock(1000)){bsp_display_unlock();}
				lv_label_set_text_fmt(ui_X_low_bon_Value1,"%d",bound_value);
				master_write_func(21, bound_value);
				vTaskDelay(2);
				bsp_display_unlock();
				last_screen=3222;
				cur_screen=322;
			}
			else if( cur_screen==31 ){
				ESP_LOGI(TAG, "Update screen to X Asix");
								while(!bsp_display_lock(1000)){bsp_display_unlock();}
								lv_label_set_text(ui_LAB_GC_ASIX, "X Asix");
								bsp_display_unlock();
			}
			else if( cur_screen==320 || cur_screen==32 ){
				ESP_LOGI(TAG, "Get x bound data from mobus");
				//if( last_screen==322 || last_screen==321 || last_screen==31){
					//vTaskDelay(200);
					while(!bsp_display_lock(1000)){bsp_display_unlock();}
					lv_label_set_text(ui_LAB_GC_ASIX1, "X Asix");
					lv_label_set_text(ui_Label18, "X Upper bound");
					lv_label_set_text(ui_Label22, "X Lower bound");
					lv_label_set_text_fmt(ui_X_UP_bon_Value1,"%d",x_u_bound);
					lv_label_set_text_fmt(ui_X_low_bon_Value1,"%d",x_l_bound);
					bsp_display_unlock();
				//}
			}
			else if( cur_screen==321 ){
				ESP_LOGI(TAG, "Get y bound data from mobus");
				if( last_screen==320 || last_screen==322){
					//vTaskDelay(200);
					while(!bsp_display_lock(1000)){bsp_display_unlock();}
					lv_label_set_text(ui_LAB_GC_ASIX1, "Y Asix");
					lv_label_set_text(ui_Label18, "Y Upper bound");
					lv_label_set_text(ui_Label22, "Y Lower bound");
					lv_label_set_text_fmt(ui_X_UP_bon_Value1,"%d",y_u_bound);
					lv_label_set_text_fmt(ui_X_low_bon_Value1,"%d",y_l_bound);
					bsp_display_unlock();
				}
			}
			else if( cur_screen==322 ){
				ESP_LOGI(TAG, "Get z bound data from mobus");
				if( last_screen==320 || last_screen==321){
					//vTaskDelay(200);
					while(!bsp_display_lock(1000)){bsp_display_unlock();}
					lv_label_set_text(ui_LAB_GC_ASIX1, "Z Asix");
					lv_label_set_text(ui_Label18, "Z Upper bound");
					lv_label_set_text(ui_Label22, "Z Lower bound");
					lv_label_set_text_fmt(ui_X_UP_bon_Value1,"%d",z_u_bound);
					lv_label_set_text_fmt(ui_X_low_bon_Value1,"%d",z_l_bound);
					bsp_display_unlock();
				}
			}
			else if( cur_screen==313 ){ //collision occured................................
				ESP_LOGI(TAG, "collision just occured....alarm_state=%d",alarm_state);
				lv_obj_clear_flag(ui_PL_GC_Alarm, LV_OBJ_FLAG_HIDDEN);
				if(user_table[login_user].lv == 4){
					lv_obj_clear_state(ui_BTN_AlarmRst, LV_STATE_DISABLED);
				}
				if( alarm_state==0 ){
					ESP_LOGI(TAG, "Logging collision....login_user %d  colli_index=%d", login_user, colli_index);
					alarm_state=1;
					nvs_set_u16(nvs_handle_modbus, nvs_key("cyear",colli_index), time3.tm_year);
					nvs_set_u16(nvs_handle_modbus, nvs_key("cmon",colli_index), time3.tm_mon);
					nvs_set_u16(nvs_handle_modbus, nvs_key("cday",colli_index), time3.tm_mday);
					nvs_set_u16(nvs_handle_modbus, nvs_key("chour",colli_index), time3.tm_hour);
					nvs_set_u16(nvs_handle_modbus, nvs_key("cmin",colli_index), time3.tm_min);
					nvs_set_u16(nvs_handle_modbus, nvs_key("csec",colli_index), time3.tm_sec);
					nvs_set_u16(nvs_handle_modbus, nvs_key("c_id",colli_index), login_user);

					sprintf(log_buf,"%04d-%02d-%02d %02d:%02d:%02d", time3.tm_year + 1900, time3.tm_mon+1,time3.tm_mday, time3.tm_hour, time3.tm_min, time3.tm_sec);
					//ESP_LOGI(TAG, log_buf);
					while(!bsp_display_lock(1000)){bsp_display_unlock();}
					lv_table_set_cell_value(ui_Table_Log, colli_index, 0, log_buf);//time
					lv_table_set_cell_value_fmt(ui_Table_Log, colli_index, 1, "%d",login_user);//id
					bsp_display_unlock();
					printf(log_buf);

					colli_index++;
					nvs_set_i32(nvs_handle_modbus, "colli_index", colli_index);

					nvs_commit(nvs_handle_modbus);
				}
				vTaskDelay(5);
			}
			else if( cur_screen==33 ){
				ESP_LOGI(TAG, "Calibration page....cur_user=%d lv=%d",cur_user,user_table[cur_user].lv);
				while(!bsp_display_lock(1000)){bsp_display_unlock();}
				if(user_table[login_user].lv == 4){
					lv_obj_add_flag(ui_BTN_Cab, LV_OBJ_FLAG_CLICKABLE);
					lv_obj_set_style_bg_color(ui_BTN_Cab, lv_color_hex(0x32C328), LV_PART_MAIN | LV_STATE_DEFAULT);
					lv_obj_set_style_bg_opa(ui_BTN_Cab, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
					lv_obj_set_style_bg_grad_color(ui_BTN_Cab, lv_color_hex(0x36F544), LV_PART_MAIN | LV_STATE_DEFAULT);
					//lv_obj_set_style_shadow_color(ui_BTN_Cab, lv_color_hex(0x000000), LV_PART_MAIN | LV_STATE_DEFAULT);
					lv_obj_set_style_shadow_opa(ui_BTN_Cab, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
					lv_obj_set_style_text_opa(ui_LB_Cab, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
					lv_obj_clear_state(ui_BTN_Cab, LV_STATE_DISABLED);
				}
				else{
					lv_obj_clear_flag(ui_BTN_Cab, LV_OBJ_FLAG_CLICKABLE);
					lv_obj_set_style_bg_color(ui_BTN_Cab, lv_color_hex(0xF0F0F0), LV_PART_MAIN | LV_STATE_DEFAULT);
					lv_obj_set_style_bg_opa(ui_BTN_Cab, 32, LV_PART_MAIN | LV_STATE_DEFAULT);
					lv_obj_set_style_bg_grad_color(ui_BTN_Cab, lv_color_hex(0xF0F0F0), LV_PART_MAIN | LV_STATE_DEFAULT);
					//lv_obj_set_style_shadow_color(ui_BTN_Cab, lv_color_hex(0x000000), LV_PART_MAIN | LV_STATE_DEFAULT);
					lv_obj_set_style_shadow_opa(ui_BTN_Cab, 32, LV_PART_MAIN | LV_STATE_DEFAULT);
					lv_obj_set_style_text_opa(ui_LB_Cab, 64, LV_PART_MAIN | LV_STATE_DEFAULT);
					lv_obj_add_state(ui_BTN_Cab, LV_STATE_DISABLED);
				}
				bsp_display_unlock();
			}
			else if( cur_screen==331 ){
				ESP_LOGI(TAG, "Start Calibration....");
				beep();
				//lv_obj_add_flag(ui_BTN_Cab, LV_OBJ_FLAG_CLICKABLE);
				//0x0078	UInt8	開啟加速度計輸出頻率自動校正，1為啟用，校正需時約10秒，校正時不更新資料，校正完畢自動關閉並恢復更新
				//0x0079	UInt8	開啟/關閉自動校正，0x00為關閉，0x01為啟用自動BIAS校正，0x10為啟用自動傾斜角校正
				//master_write_func(25, 0x1); //25
				master_write_func(26, 0x1);  //26

				cur_screen=33;
			}
			else if( cur_screen==332 ){
				ESP_LOGI(TAG, "Get Calibration values....");
				while(!bsp_display_lock(10)){bsp_display_unlock();}
				lv_label_set_text_fmt(ui_LB_X_bias, "%d",xBias);
				lv_label_set_text_fmt(ui_LB_Y_bias, "%d",yBias);
				lv_label_set_text_fmt(ui_LB_Z_bias, "%d",zBias);
				bsp_display_unlock();
				cur_screen=33;
			}
			else if( cur_screen==34 ){  //ui_LB_EnvClear
				ESP_LOGI(TAG, "Update colors? clickable?");
				if(user_table[login_user].lv == 4){
					while(!bsp_display_lock(1000)){bsp_display_unlock();}
					lv_obj_add_flag(ui_BTN_EvnClear, LV_OBJ_FLAG_CLICKABLE);
					lv_obj_clear_state(ui_BTN_EvnClear, LV_STATE_DISABLED);       /// States  LV_STATE_DEFAULT
					lv_obj_add_state(ui_BTN_EvnClear, LV_STATE_DEFAULT);
					bsp_display_unlock();
				}
				else{
					while(!bsp_display_lock(1000)){bsp_display_unlock();}
					lv_obj_clear_flag(ui_BTN_EvnClear, LV_OBJ_FLAG_CLICKABLE);
					lv_obj_clear_state(ui_BTN_EvnClear, LV_STATE_DEFAULT);
					lv_obj_add_state(ui_BTN_EvnClear, LV_STATE_DISABLED);       /// States
					bsp_display_unlock();
				}
				ESP_LOGI(TAG, "Enter Log page....colli_index=%d",colli_index);
				for(i=0; i<colli_index; i++){
					ESP_LOGI(TAG, "read nvs keys user %d log i=%d", cur_user, i);
					nvs_get_u16(nvs_handle_modbus, nvs_key("cyear",i), &time_temp); time2.tm_year=time_temp;
					nvs_get_u16(nvs_handle_modbus, nvs_key("cmon",i), &time_temp); time2.tm_mon=time_temp;
					nvs_get_u16(nvs_handle_modbus, nvs_key("cday",i), &time_temp); time2.tm_mday=time_temp;
					nvs_get_u16(nvs_handle_modbus, nvs_key("chour",i), &time_temp); time2.tm_hour=time_temp;
					nvs_get_u16(nvs_handle_modbus, nvs_key("cmin",i), &time_temp); time2.tm_min=time_temp;
					nvs_get_u16(nvs_handle_modbus, nvs_key("csec",i), &time_temp); time2.tm_sec=time_temp;
					nvs_get_u16(nvs_handle_modbus, nvs_key("c_id",i), &time_temp); cur_user=time_temp;
					sprintf(log_buf,"%04d-%02d-%02d %02d:%02d:%02d", time2.tm_year + 1900, time2.tm_mon+1,time2.tm_mday, time2.tm_hour, time2.tm_min, time2.tm_sec);
					//ESP_LOGI(TAG, log_buf);
					while(!bsp_display_lock(1000)){bsp_display_unlock();}
					lv_table_set_cell_value(ui_Table_Log, i, 0, log_buf);//time
					lv_table_set_cell_value_fmt(ui_Table_Log, i, 1, "%d",cur_user);//id
					bsp_display_unlock();
					printf(log_buf);

					nvs_commit(nvs_handle_modbus);
					vTaskDelay(5);
				}
			}
			else if( cur_screen==341 ){
				ESP_LOGI(TAG, "Clear all logs.................colli_index=%d",colli_index);
				beep();
				if( colli_index==0 ){
					last_screen=341;
					cur_screen=34;
					ESP_LOGI(TAG, "CANNOT Clear all logs.................colli_index=%d",colli_index);
					continue;
				}
				//lv_table_set_row_cnt(ui_Table_Log, 1);

				for(i=0; i<colli_index; i++){
					ESP_LOGI(TAG, "Save nvs keys user %d log i=%d", cur_user, i);
					nvs_set_u16(nvs_handle_modbus, nvs_key("cyear",colli_index), 0); //id
					nvs_set_u16(nvs_handle_modbus, nvs_key("cmon",colli_index), 0);
					nvs_set_u16(nvs_handle_modbus, nvs_key("cday",colli_index), 0);
					nvs_set_u16(nvs_handle_modbus, nvs_key("chour",colli_index), 0);
					nvs_set_u16(nvs_handle_modbus, nvs_key("cmin",colli_index), 0);
					nvs_set_u16(nvs_handle_modbus, nvs_key("csec",colli_index), 0);
					nvs_set_u16(nvs_handle_modbus, nvs_key("c_id",colli_index), 0);
					while(!bsp_display_lock(1000)){bsp_display_unlock();}
					lv_table_set_cell_value(ui_Table_Log, i, 0, "");
					lv_table_set_cell_value(ui_Table_Log, i, 1, "");
					bsp_display_unlock();

					nvs_commit(nvs_handle_modbus);
					vTaskDelay(5);
				}
				colli_index=0;
				nvs_set_i32(nvs_handle_modbus, "colli_index", colli_index);
				nvs_commit(nvs_handle_modbus);
				last_screen=341;
				cur_screen=34;
				continue;
			}
			else if( cur_screen==41 ){
				while(!bsp_display_lock(1000)){bsp_display_unlock();}

				//holding_reg_params_slave.holding_data25 = 1; //set blade H
				//holding_reg_params_slave.holding_data26 = 0x81cd; //set blade L
				tool_set=holding_reg_params_slave.holding_data26+(holding_reg_params_slave.holding_data25<<16);
				lv_label_set_text_fmt(ui_LBS_TOOL_SET, "%05d",tool_set);

				sprintf(log_buf,"%05d",tool_set);
				lv_roller_set_selected(ui_RO_TOOL_SET1_X10000,log_buf[0]-'0',LV_ANIM_ON);
				lv_roller_set_selected(ui_RO_TOOL_SET1_X1000,log_buf[1]-'0',LV_ANIM_ON);
				lv_roller_set_selected(ui_RO_TOOL_SET1_X100,log_buf[2]-'0',LV_ANIM_ON);
				lv_roller_set_selected(ui_RO_TOOL_SET1_X10,log_buf[3]-'0',LV_ANIM_ON);
				lv_roller_set_selected(ui_RO_TOOL_SET1_X1,log_buf[4]-'0',LV_ANIM_ON);

				//holding_reg_params_slave.holding_data27 = 1; //now blade H
				//holding_reg_params_slave.holding_data28 = 0x2fd1; //now blade L
				tool_set=holding_reg_params_slave.holding_data28+(holding_reg_params_slave.holding_data27<<16);
				lv_label_set_text_fmt(ui_LAB_TOOL_NO, "%05d",tool_set);

				bsp_display_unlock();

			}
			else if( cur_screen==411 ){  //ui_LB_EnvClear
				ESP_LOGI(TAG, "Set tool number............................tool=%d",tool_now);
				beep();
				while(!bsp_display_lock(1000)){bsp_display_unlock();}
				lv_label_set_text_fmt(ui_LBS_TOOL_SET, "%05d",tool_now);
				lv_obj_set_style_bg_color(ui_RO_TOOL_SET1_X1, lv_color_hex(0xE37777), LV_PART_SELECTED | LV_STATE_DEFAULT);
				lv_obj_set_style_bg_color(ui_RO_TOOL_SET1_X10, lv_color_hex(0xE37777), LV_PART_SELECTED | LV_STATE_DEFAULT);
				lv_obj_set_style_bg_color(ui_RO_TOOL_SET1_X100, lv_color_hex(0xE37777), LV_PART_SELECTED | LV_STATE_DEFAULT);
				lv_obj_set_style_bg_color(ui_RO_TOOL_SET1_X1000, lv_color_hex(0xE37777), LV_PART_SELECTED | LV_STATE_DEFAULT);
				lv_obj_set_style_bg_color(ui_RO_TOOL_SET1_X10000, lv_color_hex(0xE37777), LV_PART_SELECTED | LV_STATE_DEFAULT);
				bsp_display_unlock();
				holding_reg_params_slave.holding_data26=tool_now;
				holding_reg_params_slave.holding_data25=(tool_now>>16);

				ESP_LOGI(TAG, "Set tool number H25=%d  L26=%d",holding_reg_params_slave.holding_data25, holding_reg_params_slave.holding_data26);

				//tool_set=holding_reg_params_slave.holding_data28+(holding_reg_params_slave.holding_data27<<16);
				//lv_label_set_text_fmt(ui_LAB_TOOL_NO, "%05d",tool_set);



				cur_screen=41;
			}
			else if( cur_screen==42 ){
				while(!bsp_display_lock(1000)){bsp_display_unlock();}

				//holding_reg_params_slave.holding_data25 = 1; //set blade H
				//holding_reg_params_slave.holding_data26 = 0x81cd; //set blade L
				tool_set=holding_reg_params_slave.holding_data26+(holding_reg_params_slave.holding_data25<<16);
				lv_label_set_text_fmt(ui_LBS_TOOL_SET1, "%05d",tool_set);

				sprintf(log_buf,"%05d",tool_set);
				lv_roller_set_selected(ui_RO_TOOL_SET2_X10000,log_buf[0]-'0',LV_ANIM_ON);
				lv_roller_set_selected(ui_RO_TOOL_SET2_X1000,log_buf[1]-'0',LV_ANIM_ON);
				lv_roller_set_selected(ui_RO_TOOL_SET2_X100,log_buf[2]-'0',LV_ANIM_ON);
				lv_roller_set_selected(ui_RO_TOOL_SET2_X10,log_buf[3]-'0',LV_ANIM_ON);
				lv_roller_set_selected(ui_RO_TOOL_SET2_X1,log_buf[4]-'0',LV_ANIM_ON);

				bsp_display_unlock();

			}
			else if( cur_screen==421 ){  //ui_LB_EnvClear
				ESP_LOGI(TAG, "Set tool2 number............................tool2=%d",tool_now2);
				beep();

				while(!bsp_display_lock(1000)){bsp_display_unlock();}
				lv_label_set_text_fmt(ui_LBS_TOOL_SET1, "%05d",tool_now2);
				lv_obj_set_style_bg_color(ui_RO_TOOL_SET2_X1, lv_color_hex(0xE37777), LV_PART_SELECTED | LV_STATE_DEFAULT);
				lv_obj_set_style_bg_color(ui_RO_TOOL_SET2_X10, lv_color_hex(0xE37777), LV_PART_SELECTED | LV_STATE_DEFAULT);
				lv_obj_set_style_bg_color(ui_RO_TOOL_SET2_X100, lv_color_hex(0xE37777), LV_PART_SELECTED | LV_STATE_DEFAULT);
				lv_obj_set_style_bg_color(ui_RO_TOOL_SET2_X1000, lv_color_hex(0xE37777), LV_PART_SELECTED | LV_STATE_DEFAULT);
				lv_obj_set_style_bg_color(ui_RO_TOOL_SET2_X10000, lv_color_hex(0xE37777), LV_PART_SELECTED | LV_STATE_DEFAULT);
				bsp_display_unlock();
				holding_reg_params_slave.holding_data26=tool_now2;
				holding_reg_params_slave.holding_data25=(tool_now2>>16);
				ESP_LOGI(TAG, "Set tool number H25=%d  L26=%d",holding_reg_params_slave.holding_data25, holding_reg_params_slave.holding_data26);

				cur_screen=42;
			}
			else if( cur_screen==99 ){  //ui_LB_EnvClear
				ESP_LOGI(TAG, "Log page....log_index=%d",log_index);
				i=0;
				//lv_obj_add_flag(ui_BTN_Cab, LV_OBJ_FLAG_CLICKABLE);
				//nvs_set_u16(nvs_handle_modbus, nvs_key("h",(log_index*14)+100), cur_user); //id
				while(!bsp_display_lock(1000)){bsp_display_unlock();}
				if(user_table[login_user].lv == 4){
					lv_obj_add_flag(ui_BTN_EvnClear, LV_OBJ_FLAG_CLICKABLE);
					lv_obj_set_style_bg_color(ui_BTN_EvnClear, lv_color_hex(0x32C328), LV_PART_MAIN | LV_STATE_DEFAULT);
					lv_obj_set_style_bg_opa(ui_BTN_EvnClear, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
					lv_obj_set_style_bg_grad_color(ui_BTN_EvnClear, lv_color_hex(0x36F544), LV_PART_MAIN | LV_STATE_DEFAULT);
					//lv_obj_set_style_shadow_color(ui_BTN_Cab, lv_color_hex(0x000000), LV_PART_MAIN | LV_STATE_DEFAULT);
					lv_obj_set_style_shadow_opa(ui_BTN_EvnClear, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
					lv_obj_set_style_text_opa(ui_BTN_EvnClear, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
					lv_obj_set_style_text_opa(ui_LB_EnvClear, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
				}
				else{
					lv_obj_clear_flag(ui_BTN_EvnClear, LV_OBJ_FLAG_CLICKABLE);
					lv_obj_set_style_bg_color(ui_BTN_EvnClear, lv_color_hex(0xF0F0F0), LV_PART_MAIN | LV_STATE_DEFAULT);
					lv_obj_set_style_bg_opa(ui_BTN_EvnClear, 32, LV_PART_MAIN | LV_STATE_DEFAULT);
					lv_obj_set_style_bg_grad_color(ui_BTN_EvnClear, lv_color_hex(0xF0F0F0), LV_PART_MAIN | LV_STATE_DEFAULT);
					//lv_obj_set_style_shadow_color(ui_BTN_Cab, lv_color_hex(0x000000), LV_PART_MAIN | LV_STATE_DEFAULT);
					lv_obj_set_style_shadow_opa(ui_BTN_EvnClear, 32, LV_PART_MAIN | LV_STATE_DEFAULT);
					lv_obj_set_style_text_opa(ui_BTN_EvnClear, 64, LV_PART_MAIN | LV_STATE_DEFAULT);
					lv_obj_set_style_text_opa(ui_LB_EnvClear, 64, LV_PART_MAIN | LV_STATE_DEFAULT);
				}
				for(i=0; i<log_full; i++){
					time_temp = *(&holding_reg_params_slave.holding_data100+(i*14));
					lv_table_set_cell_value_fmt(ui_Table_Log, i+1, 0, "%01d",time_temp);

					// get log in time
					time2.tm_year = *(&holding_reg_params_slave.holding_data104+(i*14));
					time2.tm_mon = *(&holding_reg_params_slave.holding_data105+(i*14));
					time2.tm_mday = *(&holding_reg_params_slave.holding_data106+(i*14));
					time2.tm_hour = *(&holding_reg_params_slave.holding_data107+(i*14));
					time2.tm_min = *(&holding_reg_params_slave.holding_data108+(i*14));
					sprintf(log_buf,"%04d-%02d-%02d %02d:%02d", time2.tm_year + 1900, time2.tm_mon+1,time2.tm_mday, time2.tm_hour, time2.tm_min);
					//ESP_LOGI(TAG, log_buf);
					lv_table_set_cell_value(ui_Table_Log, i+1, 1, log_buf);
					printf(log_buf);

					// get log out time
					if((i+1)!=log_index){
						time2.tm_year = *(&holding_reg_params_slave.holding_data109+(i*14));
						time2.tm_mon = *(&holding_reg_params_slave.holding_data110+(i*14));
						time2.tm_mday = *(&holding_reg_params_slave.holding_data111+(i*14));
						time2.tm_hour = *(&holding_reg_params_slave.holding_data112+(i*14));
						time2.tm_min = *(&holding_reg_params_slave.holding_data113+(i*14));
						sprintf(log_buf,"%04d-%02d-%02d %02d:%02d", time3.tm_year + 1900, time3.tm_mon+1,time3.tm_mday, time3.tm_hour, time3.tm_min);
						lv_table_set_cell_value(ui_Table_Log, i+1, 2, log_buf);
						printf(log_buf);
					}
					else
						lv_table_set_cell_value(ui_Table_Log, i+1, 2, "login now");
				}
				bsp_display_unlock();
			}
			else if( cur_screen==451 ){
				ESP_LOGI(TAG, "Clear all logs.................log_full=%d",log_full);
				lv_table_set_row_cnt(ui_Table_Log, 1);
				beep();

				for(i=0; i<log_full; i++){
					*(&holding_reg_params_slave.holding_data100+(i*14)) = 0;
					*(&holding_reg_params_slave.holding_data104+(i*14)) = 0;
					*(&holding_reg_params_slave.holding_data105+(i*14)) = 0;
					*(&holding_reg_params_slave.holding_data106+(i*14)) = 0;
					*(&holding_reg_params_slave.holding_data107+(i*14)) = 0;
					*(&holding_reg_params_slave.holding_data108+(i*14)) = 0;
					*(&holding_reg_params_slave.holding_data109+(i*14)) = 0;
					*(&holding_reg_params_slave.holding_data110+(i*14)) = 0;
					*(&holding_reg_params_slave.holding_data111+(i*14)) = 0;
					*(&holding_reg_params_slave.holding_data112+(i*14)) = 0;
					*(&holding_reg_params_slave.holding_data113+(i*14)) = 0;
					ESP_LOGI(TAG, "Save nvs keys user %d log log_index=%d", cur_user, log_index);
					nvs_set_u16(nvs_handle_modbus, nvs_key("h",(i*14)+100), 0); //id
					nvs_set_u16(nvs_handle_modbus, nvs_key("h",(i*14)+104), 0);
					nvs_set_u16(nvs_handle_modbus, nvs_key("h",(i*14)+105), 0);
					nvs_set_u16(nvs_handle_modbus, nvs_key("h",(i*14)+106), 0);
					nvs_set_u16(nvs_handle_modbus, nvs_key("h",(i*14)+107), 0);
					nvs_set_u16(nvs_handle_modbus, nvs_key("h",(i*14)+108), 0);
					nvs_set_u16(nvs_handle_modbus, nvs_key("h",(i*14)+109), 0);
					nvs_set_u16(nvs_handle_modbus, nvs_key("h",(i*14)+110), 0);
					nvs_set_u16(nvs_handle_modbus, nvs_key("h",(i*14)+111), 0);
					nvs_set_u16(nvs_handle_modbus, nvs_key("h",(i*14)+112), 0);
					nvs_set_u16(nvs_handle_modbus, nvs_key("h",(i*14)+113), 0);

					nvs_commit(nvs_handle_modbus);
					vTaskDelay(5);
				}
				log_full=0;
				log_index=0;
				nvs_set_i32(nvs_handle_modbus, "log_index", log_index);
				nvs_set_i32(nvs_handle_modbus, "log_full", log_full);
				nvs_commit(nvs_handle_modbus);
				lv_table_set_cell_value(ui_Table_Log, 1, 0, "");
				lv_table_set_cell_value(ui_Table_Log, 1, 1, "");
				lv_table_set_cell_value(ui_Table_Log, 1, 2, "");
				lv_table_set_cell_value(ui_Table_Log, 2, 0, "");
				lv_table_set_cell_value(ui_Table_Log, 2, 1, "");
				lv_table_set_cell_value(ui_Table_Log, 2, 2, "");
				last_screen=451;
				cur_screen=45;
				continue;
			}
			else if( cur_screen==51 ){
				ESP_LOGI(TAG, "Checking license lock.........time_valid=%d",time_valid);
				while(!bsp_display_lock(1000)){bsp_display_unlock();}
				if(time_valid){
					if( difftime(mktime(&exp_time),mktime(&time3))>0 ){ //license ok
						ESP_LOGI(TAG, "License ok.....");
						license_unlock=1;
						holding_reg_params_slave.holding_data30 = 0;
						lv_obj_add_flag(ui_img_dongle_err, LV_OBJ_FLAG_HIDDEN);
						lv_obj_clear_flag(ui_img_lic_ok, LV_OBJ_FLAG_HIDDEN);
						lv_label_set_text(ui_Label40, "License\nExpiration Date");
						lv_label_set_text(ui_LAB_exp_date, expire_date);
						lv_obj_clear_flag(ui_Label40, LV_OBJ_FLAG_HIDDEN);
						lv_obj_clear_flag(ui_LAB_exp_date, LV_OBJ_FLAG_HIDDEN);
						lv_obj_add_flag(ui_img_lic_exp, LV_OBJ_FLAG_HIDDEN);
						lv_obj_add_flag(ui_BTN_Lic_renew, LV_OBJ_FLAG_HIDDEN);
					}
					else{
						ESP_LOGI(TAG, "License expired.....");
						//license_unlock=0;
						holding_reg_params_slave.holding_data30 = 1;
						lv_obj_add_flag(ui_img_dongle_err, LV_OBJ_FLAG_HIDDEN);
						lv_label_set_text(ui_Label40, "License Key\nhas Expired.");
						lv_label_set_text(ui_LAB_exp_date, expire_date);
						lv_obj_clear_flag(ui_Label40, LV_OBJ_FLAG_HIDDEN);
						lv_obj_clear_flag(ui_LAB_exp_date, LV_OBJ_FLAG_HIDDEN);
						lv_obj_clear_flag(ui_img_lic_exp, LV_OBJ_FLAG_HIDDEN);
						lv_obj_clear_flag(ui_BTN_Lic_renew, LV_OBJ_FLAG_HIDDEN);  
					}
					//LV_IMG_DECLARE(ui_img_137911575);    // assets/licence-ok.png  //149
					//LV_IMG_DECLARE(ui_img_2141690491);    // assets/licence-x.png  //150
				}
				else{
					ESP_LOGI(TAG, "RTC time invalid !!!!!!!!");
					//license_unlock=0;
					holding_reg_params_slave.holding_data30 = 1;
					lv_obj_clear_flag(ui_img_dongle_err, LV_OBJ_FLAG_HIDDEN);
					lv_obj_add_flag(ui_img_lic_ok, LV_OBJ_FLAG_HIDDEN);
					lv_obj_add_flag(ui_img_lic_exp, LV_OBJ_FLAG_HIDDEN);
					lv_obj_add_flag(ui_LAB_exp_date, LV_OBJ_FLAG_HIDDEN);
					lv_obj_add_flag(ui_Label40, LV_OBJ_FLAG_HIDDEN);
					lv_obj_add_flag(ui_LAB_exp_date, LV_OBJ_FLAG_HIDDEN);
					lv_obj_add_flag(ui_BTN_Lic_renew, LV_OBJ_FLAG_HIDDEN);
				}
				bsp_display_unlock();

			}
			else if( cur_screen==52 ){
				ESP_LOGI(TAG, "Input license code.....................................");
			}
			else if( cur_screen==521 ){
				ESP_LOGI(TAG, "checking license code............license_code=%s",license_code);
				if( strlen(license_code)==8 ){
					decode_license(license_code, &license_days, &license_key);
					ESP_LOGI(TAG, "Decoded Days: %u, Decoded Key: %u\n", license_days, license_key);
					if( license_key == holding_reg_params_slave.holding_data29 ){
						ESP_LOGI(TAG, "adding license_days.............................");
						ESP_LOGI(TAG, "%04d-%02d-%02d %02d:%02d:%02d\n", exp_time.tm_year + 1900, exp_time.tm_mon + 1,exp_time.tm_mday, exp_time.tm_hour, exp_time.tm_min, exp_time.tm_sec);
						exp_time.tm_mday = exp_time.tm_mday + license_days;
						mktime(&exp_time);
						ESP_LOGI(TAG, "%04d-%02d-%02d %02d:%02d:%02d\n", exp_time.tm_year + 1900, exp_time.tm_mon + 1,exp_time.tm_mday, exp_time.tm_hour, exp_time.tm_min, exp_time.tm_sec);
						sprintf(expire_date,"%04d/%02d/%02d", exp_time.tm_year + 1900, exp_time.tm_mon+1,exp_time.tm_mday);
						while(!bsp_display_lock(1000)){bsp_display_unlock();}
						lv_label_set_text(ui_LAB_exp_date, expire_date);
						bsp_display_unlock();
						holding_reg_params_slave.holding_data2=exp_time.tm_year+1900;
						holding_reg_params_slave.holding_data4=exp_time.tm_mon+1;
	            	    holding_reg_params_slave.holding_data5=exp_time.tm_mday;
	            	    SaveModbusDataToNVS();
						beep();
						lv_refr_now(NULL);
					}else{
						ESP_LOGI(TAG, "license_key error....................................");
					}
				}
				else{
					ESP_LOGI(TAG, "license_code error....................................");
				}
			}
			else if( cur_screen==62 ){
				ESP_LOGI(TAG, "Enter Set Modbus Slave address page ...mb_slave_id=%d", mb_slave_id);
				mb_slave_id = holding_reg_params_slave.holding_data23;
				sprintf(log_buf,"%03d",mb_slave_id);
				lv_roller_set_selected(ui_ModBusIDx100,log_buf[0]-'0',LV_ANIM_ON);
				lv_roller_set_selected(ui_ModBusIDx10,log_buf[1]-'0',LV_ANIM_ON);
				lv_roller_set_selected(ui_ModBusIDx1,log_buf[2]-'0',LV_ANIM_ON);
			}
			else if( cur_screen==64 ){
				ESP_LOGI(TAG, "Start to set time.......time_valid=%d",time_valid);
				ESP_LOGI(TAG, "%02d-%02d-%02d %02d:%02d", time3.tm_year , time3.tm_mon ,time3.tm_mday, time3.tm_hour, time3.tm_min);
				if(time_valid){
					while(!bsp_display_lock(1000)){bsp_display_unlock();}
					lv_roller_set_selected(ui_Roller_set_Year, uint16_t(time3.tm_year-124), LV_ANIM_ON);  //+1900
					lv_roller_set_selected(ui_Roller_set_month, uint16_t(time3.tm_mon), LV_ANIM_ON);  //+1
					lv_roller_set_selected(ui_Roller_set_Day, uint16_t(time3.tm_mday-1), LV_ANIM_ON); // +1
					lv_roller_set_selected(ui_Roller_set_Hour, uint16_t(time3.tm_hour), LV_ANIM_ON);
					lv_roller_set_selected(ui_Roller_set_minute, uint16_t(time3.tm_min), LV_ANIM_ON);
					bsp_display_unlock();
				}
			}
			else if( cur_screen==659 ){
				beep();
				cur_screen=61;
				ESP_LOGW(TAG, "Restarting Modbus Master......modbus_mode=%d",modbus_mode);
				if(!modbus_mode){
					mobus_slave_stop = true;
					destory_modbus_slave();
					modbus_master_run=0;
					vTaskDelay(700);
					modbus_master_run=1;
					mb_master();
					xTaskCreatePinnedToCore((TaskFunction_t)master_operation_func, "master_operation_func", 4096, NULL, 2, NULL, 0);
				}
			}

			else if( cur_screen==1234 ){
				//lv_obj_add_flag(perf_label, LV_OBJ_FLAG_HIDDEN);
				cur_screen=last_screen;
			}

			//////////////////////////////////////////////////////////////////////////////////////////////////////
			if( del_screen!=NULL ){
				vTaskDelay(10);
				ESP_LOGI(TAG, "Deleting old screen........");
				//lv_obj_del(del_screen);
				del_screen=NULL;
			}
			////////////////////////////////////////////////////////////////////
			if( last_screen==1 ){
				ESP_LOGI(TAG, "First..........");
				//del_screen=ui_Screen1;
			}
			else if( last_screen==62 && cur_screen==61 ){
				if( holding_reg_params_slave.holding_data23 != mb_slave_id ){
					ESP_LOGI(TAG, "Set Modbus Slave address...=%d old=%d", mb_slave_id,holding_reg_params_slave.holding_data23);
					holding_reg_params_slave.holding_data23 = mb_slave_id;
					nvs_set_u16(nvs_handle_modbus, "h023", mb_slave_id);
					vTaskDelay(10);
					nvs_commit(nvs_handle_modbus);
					vTaskDelay(10);
					
					ESP_LOGW(TAG, "Restart Modbus Slave......");
					mobus_slave_stop=true;
					modbus_master_run=0;
					destory_modbus_slave();
					vTaskDelay(500);
				
					beep();
					ESP_LOGI(TAG, "Need restarting after setting Modbus................................\n\n\n\n");
					ESP_LOGI(TAG, "\nRestrat whole system......................................esp_restart\n\n");
					esp_restart();
				
					mobus_slave_stop=false;
					xTaskCreatePinnedToCore((TaskFunction_t)mb_slave, "mb_slave", 4096, NULL, 2, NULL, 1);
					
				}
			}
			else if( last_screen==63 && cur_screen==61 ){
				if( strlen(pw)==6 ){
					ESP_LOGI(TAG, "Set new default password........default_pw=%s",pw);
					beep();
				
					strcpy(default_pw, pw);
					strcpy(pw,"");
				
					while(!bsp_display_lock(1000)){bsp_display_unlock();}
					lv_textarea_set_text(ui_TextArea2, "");
					vTaskDelay(2);
					bsp_display_unlock();
					ESP_LOGI(TAG, "Save default password in NVS ... ");
					int_pw = atoi(default_pw);
					nvs_set_i32(nvs_handle_modbus, "default_pw", int_pw);
					vTaskDelay(10);
					nvs_commit(nvs_handle_modbus);
					vTaskDelay(10);
				}
			}
			else if( last_screen==64 && cur_screen==61 ){
				ESP_LOGI(TAG, "Setting RTC..........");
				beep();
				printf("%04d-%02d-%02d %02d:%02d:%02d, %s\n", time2.tm_year + 1900, time2.tm_mon+1,
						time2.tm_mday, time2.tm_hour, time2.tm_min, time2.tm_sec, time_valid ? "VALID" : "NOT VALID");
				ESP_ERROR_CHECK(pcf8563_set_time(&rtc_dev, &time2));
				//del_screen=ui_SC_Time_set;
			}
			else if( last_screen==65 && cur_screen==61 ){
				ESP_LOGI(TAG, "65 to 61 Set Modbus Mode =%d",modbus_mode);
				beep();
				//if(modbus_mode != modbus_mode_nvs){
					ESP_LOGI(TAG, "Committing modbus_mode in NVS ... modbus_mode=%d",modbus_mode);
					nvs_set_i32(nvs_handle_modbus, "modbus_mode", modbus_mode);
					vTaskDelay(10);
					nvs_commit(nvs_handle_modbus);
					vTaskDelay(10);
					modbus_mode_nvs = modbus_mode;
					vTaskDelay(10);
					
					//ESP_LOGI(TAG, "Need restarting after setting Modbus................................\n\n\n\n");
					//ESP_LOGI(TAG, "\nRestrat whole system......................................esp_restart\n\n");
					//esp_restart();

					if( modbus_mode ){
						ESP_LOGW(TAG, "Switching to Modbus Slave......");
						mobus_slave_stop = true;
						modbus_master_run=0;
						destory_modbus_slave();
						
						while(!bsp_display_lock(1000)){bsp_display_unlock();}
						lv_obj_clear_state(ui_BTN_ModBus_Master, LV_STATE_DISABLED);
						lv_obj_add_state(ui_BTN_ModBus_Slave, LV_STATE_DISABLED);
						lv_obj_clear_state(ui_BTN_Set_ModBusID, LV_STATE_DISABLED);
						_ui_screen_change(&ui_SC_6_1, LV_SCR_LOAD_ANIM_FADE_ON, 1000, 100, &ui_SC_6_1_screen_init);
						bsp_display_unlock();
						vTaskDelay(100);
						beep();
						ESP_LOGI(TAG, "Need restarting after setting Modbus................................\n\n\n\n");
						ESP_LOGI(TAG, "\nRestrat whole system......................................esp_restart\n\n");
						esp_restart();
						vTaskDelay(400);
						mobus_slave_stop=false;
						xTaskCreatePinnedToCore((TaskFunction_t)mb_slave, "mb_slave", 4096, NULL, 2, NULL, 1);
					}
					else{
						ESP_LOGW(TAG, "Switching to Modbus Master......");
						mobus_slave_stop = true;
						modbus_master_run=0;
						destory_modbus_slave();
						while(!bsp_display_lock(1000)){bsp_display_unlock();}
						lv_obj_add_state(ui_BTN_ModBus_Master, LV_STATE_DISABLED);
						lv_obj_clear_state(ui_BTN_ModBus_Slave, LV_STATE_DISABLED);
						lv_obj_add_state(ui_BTN_Set_ModBusID, LV_STATE_DISABLED);
						_ui_screen_change(&ui_SC_6_1, LV_SCR_LOAD_ANIM_FADE_ON, 1000, 100, &ui_SC_6_1_screen_init);
						bsp_display_unlock();
						vTaskDelay(100);
						setup_reg_data();
						beep();
						ESP_LOGI(TAG, "Need restarting after setting Modbus................................\n\n\n\n");
						ESP_LOGI(TAG, "\nRestrat whole system......................................esp_restart\n\n");
						esp_restart();
					
						vTaskDelay(400);
						modbus_master_run=1;
						mb_master();
						xTaskCreatePinnedToCore((TaskFunction_t)master_operation_func, "master_operation_func", 4096, NULL, 2, NULL, 1);
					}


				//}
//				else if( modbus_mode ){
//					ESP_LOGI(TAG, "Modbus Slave already started");
//					while(!bsp_display_lock(1000)){bsp_display_unlock();}
//					_ui_screen_change(&ui_SC_6_1, LV_SCR_LOAD_ANIM_FADE_ON, 500, 0, &ui_SC_6_1_screen_init);
//					bsp_display_unlock();
//					//mobus_slave_stop = false;
//					//xTaskCreatePinnedToCore((TaskFunction_t)mb_slave, "mb_slave", 4096, NULL, 2, NULL, 1);
//				}
//				else{
//					ESP_LOGI(TAG, "Modbus Master already started.");
//					while(!bsp_display_lock(1000)){bsp_display_unlock();}
//					_ui_screen_change(&ui_SC_6_1, LV_SCR_LOAD_ANIM_FADE_ON, 500, 0, &ui_SC_6_1_screen_init);
//					bsp_display_unlock();
//					//mobus_slave_stop = true;
//					//vTaskDelay(100);
//				}
				vTaskDelay(10);
				//del_screen=ui_SC_ModBus_set;
			}
			else if( last_screen==61 && cur_screen==65 ){
					ESP_LOGI(TAG, "Modbus Mode=%d",modbus_mode);
					
					while(!bsp_display_lock(1000)){bsp_display_unlock();}
					
					if( modbus_mode ){
						lv_obj_clear_state(ui_BTN_ModBus_Master, LV_STATE_DISABLED);
						lv_obj_add_state(ui_BTN_ModBus_Slave, LV_STATE_DISABLED);
    				}
    				else{
						lv_obj_add_state(ui_BTN_ModBus_Master, LV_STATE_DISABLED);
						lv_obj_clear_state(ui_BTN_ModBus_Slave, LV_STATE_DISABLED);
    				}
					
						if(holding_reg_params_slave.holding_data24 == 5){
							lv_label_set_text(ui_Label_baud, "115200");
						}
						else if(holding_reg_params_slave.holding_data24 == 4){
							lv_label_set_text(ui_Label_baud, "57600");
						}
						else if(holding_reg_params_slave.holding_data24 == 3){
							lv_label_set_text(ui_Label_baud, "38400");
						}
						else if(holding_reg_params_slave.holding_data24 == 2){
							lv_label_set_text(ui_Label_baud, "19200");
						}
						else if(holding_reg_params_slave.holding_data24 == 1){
							lv_label_set_text(ui_Label_baud, "9600");
						}
					bsp_display_unlock();
			}
			else if( cur_screen==773 ){
				ESP_LOGI(TAG, "cur=773 UpdateUserTable id delete");
				
				face_p->state = FACE_IDLE;
				face_p->stop();
				vTaskDelay(110);
				camera_p->stop();
				vTaskDelay(50);

				UpdateUserTable();
				cur_screen=12;
			}
			else if( last_screen==2 ){
				vTaskDelay(10);
				//del_screen=ui_Screen2;
			}
			last_screen = cur_screen;
		}
		else{
			if( sleep_timer <= holding_reg_params_slave.holding_data22*600 ){
				sleep_timer++;
			}
		}
		/////////////////////////////////////////////////////////////////////////////////////////
		//ESP_LOGI(TAG, "last=%d cur=%d",last_screen ,cur_screen);
//		if( cur_screen==61 ){
//			//ESP_LOGI(TAG, "Checking pw=%s default_pw=%s", pw, default_pw);
//			_ui_flag_modify(ui_Panel25, LV_OBJ_FLAG_HIDDEN, _UI_MODIFY_FLAG_ADD);
//			if(!strcmp(pw, default_pw)){
//				ESP_LOGI(TAG, "Password Matched.......61 pw=%s default_pw=%s",pw, default_pw);
//				beep();
//				while(!bsp_display_lock(1000)){bsp_display_unlock();}
//				lv_textarea_set_text(ui_Admin_PWD, "");
//				strcpy(pw,"");
//				_ui_flag_modify(ui_Panel25, LV_OBJ_FLAG_HIDDEN, _UI_MODIFY_FLAG_ADD);
//				vTaskDelay(50);
//				bsp_display_unlock();
//				strcpy(pw, "");
//			}
//		}
		if( cur_screen==65 ){
					while(!bsp_display_lock(1000)){bsp_display_unlock();}
					if( modbus_mode ){
						ESP_LOGI(TAG, "Modbus disable Slave");
						lv_obj_clear_state(ui_BTN_ModBus_Master, LV_STATE_DISABLED);
						lv_obj_add_state(ui_BTN_ModBus_Slave, LV_STATE_DISABLED);
    				}
    				else{
						ESP_LOGI(TAG, "Modbus disable Master");
						lv_obj_add_state(ui_BTN_ModBus_Master, LV_STATE_DISABLED);
						lv_obj_clear_state(ui_BTN_ModBus_Slave, LV_STATE_DISABLED);
    				}
    				bsp_display_unlock();
    	}
		if( cur_screen==771 ){
			//if(!strcmp(pw, default_pw)){
			//		ESP_LOGI(TAG, "Password Matched.......");
					cur_screen=12;
					beep();
					// Camera power is now managed automatically by camera_power_manager
					vTaskDelay(200); // Wait for camera power to stabilize and initialize

					// Check if camera is ready
					camera_power_state_t power_state = camera_power_get_state();
					if (power_state == CAMERA_POWER_ON) {
						camera_p->run();
						vTaskDelay(20);
						face_p->state = FACE_DELETE; //FACE_ENROLL FACE_DELETE
						face_p->run();
					} else {
						ESP_LOGW(TAG, "Camera not ready for face delete");
					}
					vTaskDelay(20);
					strcpy(pw, "");

			//}
		}
		if( cur_screen==10 ){
			//ESP_LOGI(TAG, "Checking pw=%s default_pw=%s", pw, default_pw);
			if(!strcmp(pw, default_pw)){
				ESP_LOGI(TAG, "Password Matched.......10 pw=%s default_pw=%s",pw, default_pw);
				beep();
				strcpy(pw, "");
			}
		}
		else if( cur_screen==315 ||cur_screen==314 || cur_screen==313 || cur_screen==31){
			if(!strcmp(pw, default_pw) && !modbus_mode &&  alarm_state){
				ESP_LOGI(TAG, "Password Matched.......31 pw=%s default_pw=%s",pw, default_pw);
				strcpy(pw,"");
				while(!bsp_display_lock(1000)){bsp_display_unlock();}
				lv_textarea_set_text(ui_TextArea4, "");
				beep();
				
				lv_obj_add_flag(ui_PL_AlarmRst_PWD, LV_OBJ_FLAG_HIDDEN);
				lv_obj_add_flag(ui_PL_GC_Alarm, LV_OBJ_FLAG_HIDDEN);
				//①	寫入位置0x0080，寫入資料為0x4D52，重啟感應器。
				//②	等待10秒。
				//master_write_func(30, 0x4D524D52);  //26
				master_write_func(30, 0x4D524D52);  //26
				
				lv_obj_clear_flag(ui_PL_AlarmRst_Ani, LV_OBJ_FLAG_HIDDEN);
				
				vTaskDelay(50);
				lv_label_set_text(ui_Label50, "9");
				bsp_display_unlock();
				
				for(int i=8;i>-1;i--){
					vTaskDelay(100);
					while(!bsp_display_lock(1000)){bsp_display_unlock();}
					lv_label_set_text_fmt(ui_Label50, "%d",i);
					bsp_display_unlock();
				}
				while(!bsp_display_lock(1000)){bsp_display_unlock();}
				lv_obj_add_flag(ui_PL_AlarmRst_Ani, LV_OBJ_FLAG_HIDDEN);
				lv_label_set_text(ui_Label50, "10");
				bsp_display_unlock();
				strcpy(pw,"");
				alarm_state=0;
				cur_screen=31;
				
			}
			else{
				//ESP_LOGI(TAG, "Password Unmatch.. pw=%s default_pw=%s modbus_mode=%d",pw, default_pw, modbus_mode);
				vTaskDelay(10);
			}
		}
		else if( cur_screen==41 ){
			while(!bsp_display_lock(1000)){bsp_display_unlock();}
			tool_set=holding_reg_params_slave.holding_data26+(holding_reg_params_slave.holding_data25<<16);
			sprintf(log_buf,"%05d",tool_set);
			if( lv_roller_get_selected(ui_RO_TOOL_SET1_X1) != log_buf[4]-'0' ){
				lv_obj_set_style_bg_color(ui_RO_TOOL_SET1_X1, lv_color_hex(0x779377), LV_PART_SELECTED | LV_STATE_DEFAULT);
			}
			else{
				lv_obj_set_style_bg_color(ui_RO_TOOL_SET1_X1, lv_color_hex(0xE37777), LV_PART_SELECTED | LV_STATE_DEFAULT);
			}
			if( lv_roller_get_selected(ui_RO_TOOL_SET1_X10) != log_buf[3]-'0' ){
				lv_obj_set_style_bg_color(ui_RO_TOOL_SET1_X10, lv_color_hex(0x779377), LV_PART_SELECTED | LV_STATE_DEFAULT);
			}
			else{
				lv_obj_set_style_bg_color(ui_RO_TOOL_SET1_X10, lv_color_hex(0xE37777), LV_PART_SELECTED | LV_STATE_DEFAULT);
			}
			if( lv_roller_get_selected(ui_RO_TOOL_SET1_X100) != log_buf[2]-'0' ){
				lv_obj_set_style_bg_color(ui_RO_TOOL_SET1_X100, lv_color_hex(0x779377), LV_PART_SELECTED | LV_STATE_DEFAULT);
			}
			else{
				lv_obj_set_style_bg_color(ui_RO_TOOL_SET1_X100, lv_color_hex(0xE37777), LV_PART_SELECTED | LV_STATE_DEFAULT);
			}
			if( lv_roller_get_selected(ui_RO_TOOL_SET1_X1000) != log_buf[1]-'0' ){
				lv_obj_set_style_bg_color(ui_RO_TOOL_SET1_X1000, lv_color_hex(0x779377), LV_PART_SELECTED | LV_STATE_DEFAULT);
			}
			else{
				lv_obj_set_style_bg_color(ui_RO_TOOL_SET1_X1000, lv_color_hex(0xE37777), LV_PART_SELECTED | LV_STATE_DEFAULT);
			}
			if( lv_roller_get_selected(ui_RO_TOOL_SET1_X10000) != log_buf[0]-'0' ){
				lv_obj_set_style_bg_color(ui_RO_TOOL_SET1_X10000, lv_color_hex(0x779377), LV_PART_SELECTED | LV_STATE_DEFAULT);
			}
			else{
				lv_obj_set_style_bg_color(ui_RO_TOOL_SET1_X10000, lv_color_hex(0xE37777), LV_PART_SELECTED | LV_STATE_DEFAULT);
			}
	    	tool_now = lv_roller_get_selected(ui_RO_TOOL_SET1_X1);
	    	tool_now = 10*lv_roller_get_selected(ui_RO_TOOL_SET1_X10) + tool_now;
	    	tool_now = 100*lv_roller_get_selected(ui_RO_TOOL_SET1_X100) + tool_now;
	    	tool_now = 1000*lv_roller_get_selected(ui_RO_TOOL_SET1_X1000) + tool_now;
	    	tool_now = 10000*lv_roller_get_selected(ui_RO_TOOL_SET1_X10000) + tool_now;
			if(tool_set!=tool_now && tool_now!=tool_now2){
				tool_now2 = tool_now;
				tool_countdown=0;
			}
			else if(tool_set!=tool_now && tool_now==tool_now2){
				tool_countdown++;
			}
	    	else{
	    		tool_countdown=0;
	    	}
	    	if(tool_countdown==30){
	    		sprintf(log_buf,"%05d",tool_set);
	    		lv_roller_set_selected(ui_RO_TOOL_SET1_X10000,log_buf[0]-'0',LV_ANIM_ON);
	    		lv_roller_set_selected(ui_RO_TOOL_SET1_X1000,log_buf[1]-'0',LV_ANIM_ON);
	    		lv_roller_set_selected(ui_RO_TOOL_SET1_X100,log_buf[2]-'0',LV_ANIM_ON);
	    		lv_roller_set_selected(ui_RO_TOOL_SET1_X10,log_buf[3]-'0',LV_ANIM_ON);
	    		lv_roller_set_selected(ui_RO_TOOL_SET1_X1,log_buf[4]-'0',LV_ANIM_ON);
	    		tool_countdown=0;
	    	}
			bsp_display_unlock();
		}
		else if( cur_screen==42 ){
			while(!bsp_display_lock(1000)){bsp_display_unlock();}
			tool_set=holding_reg_params_slave.holding_data26+(holding_reg_params_slave.holding_data25<<16);
			sprintf(log_buf,"%05d",tool_set);
			if( lv_roller_get_selected(ui_RO_TOOL_SET2_X1) != log_buf[4]-'0' ){
				lv_obj_set_style_bg_color(ui_RO_TOOL_SET2_X1, lv_color_hex(0x779377), LV_PART_SELECTED | LV_STATE_DEFAULT);
			}
			else{
				lv_obj_set_style_bg_color(ui_RO_TOOL_SET2_X1, lv_color_hex(0xE37777), LV_PART_SELECTED | LV_STATE_DEFAULT);
			}
			if( lv_roller_get_selected(ui_RO_TOOL_SET2_X10) != log_buf[3]-'0' ){
				lv_obj_set_style_bg_color(ui_RO_TOOL_SET2_X10, lv_color_hex(0x779377), LV_PART_SELECTED | LV_STATE_DEFAULT);
			}
			else{
				lv_obj_set_style_bg_color(ui_RO_TOOL_SET2_X10, lv_color_hex(0xE37777), LV_PART_SELECTED | LV_STATE_DEFAULT);
			}
			if( lv_roller_get_selected(ui_RO_TOOL_SET2_X100) != log_buf[2]-'0' ){
				lv_obj_set_style_bg_color(ui_RO_TOOL_SET2_X100, lv_color_hex(0x779377), LV_PART_SELECTED | LV_STATE_DEFAULT);
			}
			else{
				lv_obj_set_style_bg_color(ui_RO_TOOL_SET2_X100, lv_color_hex(0xE37777), LV_PART_SELECTED | LV_STATE_DEFAULT);
			}
			if( lv_roller_get_selected(ui_RO_TOOL_SET2_X1000) != log_buf[1]-'0' ){
				lv_obj_set_style_bg_color(ui_RO_TOOL_SET2_X1000, lv_color_hex(0x779377), LV_PART_SELECTED | LV_STATE_DEFAULT);
			}
			else{
				lv_obj_set_style_bg_color(ui_RO_TOOL_SET2_X1000, lv_color_hex(0xE37777), LV_PART_SELECTED | LV_STATE_DEFAULT);
			}
			if( lv_roller_get_selected(ui_RO_TOOL_SET2_X10000) != log_buf[0]-'0' ){
				lv_obj_set_style_bg_color(ui_RO_TOOL_SET2_X10000, lv_color_hex(0x779377), LV_PART_SELECTED | LV_STATE_DEFAULT);
			}
			else{
				lv_obj_set_style_bg_color(ui_RO_TOOL_SET2_X10000, lv_color_hex(0xE37777), LV_PART_SELECTED | LV_STATE_DEFAULT);
			}
			tool_now2 = lv_roller_get_selected(ui_RO_TOOL_SET1_X1);
			tool_now2 = 10*lv_roller_get_selected(ui_RO_TOOL_SET2_X10) + tool_now2;
			tool_now2 = 100*lv_roller_get_selected(ui_RO_TOOL_SET2_X100) + tool_now2;
			tool_now2 = 1000*lv_roller_get_selected(ui_RO_TOOL_SET2_X1000) + tool_now2;
			tool_now2 = 10000*lv_roller_get_selected(ui_RO_TOOL_SET2_X10000) + tool_now2;
			if(tool_set!=tool_now2 && tool_now!=tool_now2){
				tool_now = tool_now2;
				tool_countdown=0;
			}
			else if(tool_set!=tool_now2 && tool_now==tool_now2){
				tool_countdown++;
			}
			else{
				tool_countdown=0;
			}
			if(tool_countdown==30){
				sprintf(log_buf,"%05d",tool_set);
				lv_roller_set_selected(ui_RO_TOOL_SET2_X10000,log_buf[0]-'0',LV_ANIM_ON);
				lv_roller_set_selected(ui_RO_TOOL_SET2_X1000,log_buf[1]-'0',LV_ANIM_ON);
				lv_roller_set_selected(ui_RO_TOOL_SET2_X100,log_buf[2]-'0',LV_ANIM_ON);
				lv_roller_set_selected(ui_RO_TOOL_SET2_X10,log_buf[3]-'0',LV_ANIM_ON);
				lv_roller_set_selected(ui_RO_TOOL_SET2_X1,log_buf[4]-'0',LV_ANIM_ON);
				tool_countdown=0;
			}
			bsp_display_unlock();
		}
		else if( last_screen == 521 ){
				cur_screen=51;
				_ui_screen_change(&ui_SC_5_1, LV_SCR_LOAD_ANIM_FADE_ON, 500, 0, &ui_SC_5_1_screen_init);
		}
		///sleep time and brightness//////////////////////////////////////////////
		if( sleep_timer == holding_reg_params_slave.holding_data22*600 ){  //sleep time
			ESP_LOGW(TAG, "Set Brightness to 8......sleep_timer=%d....holding_data22=%d",sleep_timer, holding_reg_params_slave.holding_data22 );
			bsp_display_brightness_set(8); //set brightness
			sleep_timer++;
		}
		else if( touched != 0 ){
	    	if( sleep_timer >= holding_reg_params_slave.holding_data22*600 ){
	    		ESP_LOGI(TAG, "Setting brightness from ScreenChangeEvent by touched ...................");
	    		bsp_display_brightness_set(holding_reg_params_slave.holding_data21); //set brightness
	    	}
	    	sleep_timer = 0;
	    }
	    else if( touchedd != 0 ){
	    	if( sleep_timer >= holding_reg_params_slave.holding_data22*600 ){
	    		ESP_LOGI(TAG, "Setting brightness from ScreenChangeEvent by ameter enable pin ...................");
	    		bsp_display_brightness_set(holding_reg_params_slave.holding_data21); //set brightness
	    	}
	    	touchedd = 0;
	    	sleep_timer = 0;
	    }
		//else ESP_LOGI(TAG, "sleep_timer=%d.........................................", sleep_timer );

		//vTaskDelay(10);
		vTaskDelay(110 / portTICK_PERIOD_MS);
	}

	vTaskDelete(NULL);
	ESP_LOGI(TAG, "+++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++");
}


void ReadADC(void){

	ESP_LOGI(TAG, "Start ReadADC");

	adc_oneshot_unit_handle_t adc1_handle;
	adc_oneshot_unit_init_cfg_t init_config1 = {
			.unit_id = ADC_UNIT_1,
			.ulp_mode = ADC_ULP_MODE_DISABLE,
	};
	ESP_ERROR_CHECK(adc_oneshot_new_unit(&init_config1, &adc1_handle));

	adc_oneshot_chan_cfg_t config = {
			.atten = ADC_ATTEN_DB_12,
			.bitwidth = ADC_BITWIDTH_DEFAULT,
	};
	ESP_ERROR_CHECK(adc_oneshot_config_channel(adc1_handle, ADC_CHANNEL_7, &config));
	ESP_ERROR_CHECK(adc_oneshot_config_channel(adc1_handle, ADC_CHANNEL_8, &config));
	ESP_ERROR_CHECK(adc_oneshot_config_channel(adc1_handle, ADC_CHANNEL_9, &config));

	vTaskDelay(10);
	while(1){
		//ESP_LOGI(TAG, "ADC..........................................................");
		//D1 =Lab_Tool _No1 ( 10進制整數,最多5位數)
		//D2 = Lab_Standby_tool_No1
		//Lab_wheel = (ADC8/10)* "類比源2最大值" / (10^ "類比源2小數點位置" )
		//(ADC8/10)* reg22 / (10^ reg24 )

		if( cur_screen==23 || cur_screen==25 ){
			ESP_ERROR_CHECK(adc_oneshot_read(adc1_handle, ADC_CHANNEL_7, &adc_raw[0]));
			adc_value = adc_raw[0]*3.3 / 4096 * 3.7; // 3.3 / 4096 * 7.8
			//ESP_LOGI(TAG, "ADC%d Channel[%d] amp Raw Data: %d value = %f", ADC_UNIT_1 + 1, ADC_CHANNEL_7, adc_raw[0], adc_value);

			while(!bsp_display_lock(10)){vTaskDelay(1);}
			lv_img_set_angle(ui_sc3_2_pointer_img, (int16_t)(adc_value * (2600) / (10) -1300));
			lv_img_set_angle(ui_sc3_4_pointer_img, (int16_t)(adc_value * (2600) / (10) -1300));
			adc_value = adc_value/10*holding_reg_params_slave.holding_data17/pow(10,holding_reg_params_slave.holding_data19);
			//ESP_LOGI(TAG, "ADC%d Channel[%d] rpm Raw Data: %d value = %f", ADC_UNIT_1 + 1, ADC_CHANNEL_8, adc_raw[1], adc_value);
			if(holding_reg_params_slave.holding_data15){
				sprintf(wheel, "%.1fRPM", adc_value);
			}
			else{
				sprintf(wheel, "%.0fRPM", adc_value);
			}
			lv_label_set_text(ui_Lab_RPM, wheel);
			lv_label_set_text(ui_Lab_RPM1, wheel);
			bsp_display_unlock();
			vTaskDelay(10);
		}

		/////////////////////////////////////////////////////////////////////////////////////////////////////////////////

		//ESP_LOGI(TAG, "ADC%d Channel[%d] rpm Raw Data: %d value = %f", ADC_UNIT_1 + 1, ADC_CHANNEL_8, adc_raw[1], adc_value);
		if( cur_screen==22 || cur_screen==24 ){
			ESP_ERROR_CHECK(adc_oneshot_read(adc1_handle, ADC_CHANNEL_8, &adc_raw[1]));
			adc_value = adc_raw[1]*3.3 / 4096 * 3.7; // 3.3 / 4096 * 7.8
			while(!bsp_display_lock(10)){vTaskDelay(1);}
			lv_img_set_angle(ui_sc3_1_pointer_img, (int16_t)(adc_value * (2600) / (10) -1300));
			lv_img_set_angle(ui_sc3_3_pointer_img, (int16_t)(adc_value * (2600) / (10) -1300));
			adc_value = adc_value/10*holding_reg_params_slave.holding_data18/pow(10,holding_reg_params_slave.holding_data20);
			//ESP_LOGI(TAG, "ADC%d Channel[%d] rpm Raw Data: %d value = %f", ADC_UNIT_1 + 1, ADC_CHANNEL_8, adc_raw[1], adc_value);
			if(holding_reg_params_slave.holding_data15){
				sprintf(wheel, "%.1f%%", adc_value);
			}
			else{
				sprintf(wheel, "%.0f%%", adc_value);
			}
			lv_label_set_text(ui_Lab_wheel, wheel);
			lv_label_set_text(ui_Lab_wheel1, wheel);
			bsp_display_unlock();
			vTaskDelay(10);
		}
		if( cur_screen==26 ){
			ESP_ERROR_CHECK(adc_oneshot_read(adc1_handle, ADC_CHANNEL_8, &adc_raw[1]));
			adc_value = adc_raw[1]*3.3 / 4096 * 9.2; // 3.3 / 4096 * 7.8
			sprintf(amp, "%.1f", adc_value);
			while(!bsp_display_lock(10)){vTaskDelay(1);}
			lv_label_set_text(ui_Lab_Ampere, amp);
			bsp_display_unlock();
			vTaskDelay(10);
		}

		/////////////////////////////////////////////////////////////////////////////////////////////////////////////////
		if( abnormal_power==0 ){
//		if(login_user<10){
			ESP_ERROR_CHECK(adc_oneshot_read(adc1_handle, ADC_CHANNEL_9, &adc_raw[2]));
			adc_value = adc_raw[2]*3.3 / 4096 * 9.2; // 3.3 / 4096 * 7.8
			//ESP_LOGI(TAG, "ADC%d Channel[%d] volt Raw Data: %d value = %f", ADC_UNIT_1 + 1, ADC_CHANNEL_9, adc_raw[2], adc_value);
			//if(adc_value<19){
			if(adc_value<1){
				//ESP_LOGD(TAG, "value = %f Saving power down log..........", adc_value);
				abnormal_power = 1;
				nvs_set_u8(nvs_handle_modbus, "abnormal_power", 1);
				//ESP_LOGI(TAG, "Save nvs keys user %d log log_index=%d", cur_user, log_index);
				log_index--;
				nvs_set_u16(nvs_handle_modbus, nvs_key("h",(log_index*14)+109), time3.tm_year);
				nvs_set_u16(nvs_handle_modbus, nvs_key("h",(log_index*14)+110), time3.tm_mon);
				nvs_set_u16(nvs_handle_modbus, nvs_key("h",(log_index*14)+111), time3.tm_mday);
				nvs_set_u16(nvs_handle_modbus, nvs_key("h",(log_index*14)+112), time3.tm_hour);
				nvs_set_u16(nvs_handle_modbus, nvs_key("h",(log_index*14)+113), time3.tm_min);
				nvs_commit(nvs_handle_modbus);
				vTaskDelay(1);
				log_index++;
				ESP_LOGI(TAG, "abnormal power down log saved. log_index=%d adc_value=%f",log_index, adc_value);
				//esp_powerdown();
			}
		}

		//ESP_LOGI(TAG, "last=%d cur=%d",last_screen ,cur_screen);
		//xTaskCreatePinnedToCore(lvgl_port_task, "LVGL task", 40960, NULL, 19, NULL, 1);
		vTaskDelay(4);

	}
	ESP_LOGI(TAG, "ReadADC died........\nADC%d Channel[%d] volt Raw Data: %d value = %f", ADC_UNIT_1 + 1, ADC_CHANNEL_9, adc_raw[2], adc_value);
	//analogRead
	ESP_ERROR_CHECK(adc_oneshot_del_unit(adc1_handle));

	vTaskDelete(NULL);

	ESP_LOGI(TAG, "+++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++");

}

u_int8_t countSetBits(u_int8_t n) {
    u_int8_t count = 0;
    while (n) {
        count += n & 1; // Increment count if the last bit is 1
        n >>= 1;        // Right shift to process the next bit
    }
    return count;
}

void theme_decode(void){
	uint8_t total_big=0;
	uint8_t total_small[8] = { 0 };

	 theme[0] = holding_reg_params_slave.holding_data11&0xff;
	 theme[1] = (holding_reg_params_slave.holding_data11 >> 8)&0xff;

	 theme[2] = holding_reg_params_slave.holding_data12&0xff;
	 theme[3] = (holding_reg_params_slave.holding_data12 >> 8)&0xff;

	 theme[4] = holding_reg_params_slave.holding_data13&0xff;
	 theme[4] = 0; // never show
	 theme[5] = (holding_reg_params_slave.holding_data13 >> 8)&0xff;

	 theme[6] = holding_reg_params_slave.holding_data14&0xff;
	 theme[7] = (holding_reg_params_slave.holding_data14 >> 8)&0xff;

	 for(int i=0;i<8;i++){
		 if(theme[i]!=0)
		 	total_big++;
		 ESP_LOGI(TAG, "total_big=%d theme %d=%x",total_big ,i,theme[i]);
		 total_small[i]=countSetBits(theme[i]);
	     ESP_LOGI(TAG, "total_small=%d",total_small[i]);
	 }
	 
	 while(!bsp_display_lock(10)){vTaskDelay(1);}
	 if(total_big==5){
		//theme 0
		lv_obj_add_flag(ui_Image165, LV_OBJ_FLAG_HIDDEN);  //lv_obj_clear_flag
		lv_obj_add_flag(ui_Image174, LV_OBJ_FLAG_HIDDEN); //little 1
		//theme 1
		lv_obj_add_flag(ui_Image175, LV_OBJ_FLAG_HIDDEN);//little 0
		lv_obj_add_flag(ui_Image177, LV_OBJ_FLAG_HIDDEN);//little 1
		lv_obj_add_flag(ui_Image179, LV_OBJ_FLAG_HIDDEN);//little 2
		lv_obj_add_flag(ui_Image181, LV_OBJ_FLAG_HIDDEN);//little 3
		lv_obj_add_flag(ui_Image183, LV_OBJ_FLAG_HIDDEN);//little 4
		lv_obj_add_flag(ui_Image185, LV_OBJ_FLAG_HIDDEN);//little 5
		if( theme[0]==0 ){
			lv_img_set_src(ui_Image27, &ui_img_b01_20_png);
			lv_img_set_src(ui_Image38, &ui_img_b01_20_png);
			lv_img_set_src(ui_Image50, &ui_img_b01_20_png);
			lv_img_set_src(ui_Image62, &ui_img_b01_20_png);
			lv_img_set_src(ui_Image74, &ui_img_b01_20_png);
			lv_img_set_src(ui_Image86, &ui_img_b01_20_png);
			lv_img_set_src(ui_Image28, &ui_img_b02_10_png);
			lv_img_set_src(ui_Image39, &ui_img_b02_10_png);
			lv_img_set_src(ui_Image51, &ui_img_b02_10_png);
			lv_img_set_src(ui_Image63, &ui_img_b02_10_png);
			lv_img_set_src(ui_Image75, &ui_img_b02_10_png);
			lv_img_set_src(ui_Image87, &ui_img_b02_10_png);
		}
		//theme 2
		lv_obj_add_flag(ui_Image187, LV_OBJ_FLAG_HIDDEN);//little 0
		lv_obj_add_flag(ui_Image111, LV_OBJ_FLAG_HIDDEN);//little 1
		lv_obj_add_flag(ui_Image54, LV_OBJ_FLAG_HIDDEN);//little 2
		lv_obj_add_flag(ui_Image201, LV_OBJ_FLAG_HIDDEN);//little 3
		if( (theme[0] ==0)||(theme[1] ==0) ){
			lv_img_set_src(ui_Image139, &ui_img_b02_10_png);
			lv_img_set_src(ui_Image129, &ui_img_b02_10_png);
			lv_img_set_src(ui_Image107, &ui_img_b02_10_png);
			lv_img_set_src(ui_Image79, &ui_img_b02_10_png);
			lv_img_set_src(ui_Image138, &ui_img_b01_20_png);
			lv_img_set_src(ui_Image128, &ui_img_b01_20_png);
			lv_img_set_src(ui_Image106, &ui_img_b01_20_png);
			lv_img_set_src(ui_Image78, &ui_img_b01_20_png);
		}
		//theme 3
		lv_obj_add_flag(ui_Image124, LV_OBJ_FLAG_HIDDEN);
		lv_obj_add_flag(ui_Image131, LV_OBJ_FLAG_HIDDEN);//2
		if( (theme[5] !=0)&&(theme[4] !=0) ){
			lv_img_set_src(ui_Image117, &ui_img_b02_10_png);
			lv_img_set_src(ui_Image116, &ui_img_b01_20_png);
			lv_img_set_src(ui_Image119, &ui_img_b02_10_png);//2
			lv_img_set_src(ui_Image118, &ui_img_b01_20_png);
		}
		//theme 4
		lv_obj_add_flag(ui_Image146, LV_OBJ_FLAG_HIDDEN);
		if( theme[5] !=0 ){
			lv_img_set_src(ui_Image145, &ui_img_b02_10_png);
			lv_img_set_src(ui_Image144, &ui_img_b01_20_png);
		}
		//theme 5
		lv_obj_add_flag(ui_Image148, LV_OBJ_FLAG_HIDDEN);
		lv_img_set_src(ui_Image163, &ui_img_b01_20_png);
	 }
	 else if(total_big==4){
		//theme 0
		lv_obj_add_flag(ui_Image165, LV_OBJ_FLAG_HIDDEN); 
		lv_obj_add_flag(ui_Image166, LV_OBJ_FLAG_HIDDEN);
		lv_obj_add_flag(ui_Image174, LV_OBJ_FLAG_HIDDEN); //little 1
		lv_obj_add_flag(ui_Image173, LV_OBJ_FLAG_HIDDEN);
		//theme 1
		lv_obj_add_flag(ui_Image175, LV_OBJ_FLAG_HIDDEN);//little 0
		lv_obj_add_flag(ui_Image42, LV_OBJ_FLAG_HIDDEN);
		lv_obj_add_flag(ui_Image177, LV_OBJ_FLAG_HIDDEN);//little 1
		lv_obj_add_flag(ui_Image176, LV_OBJ_FLAG_HIDDEN);
		lv_obj_add_flag(ui_Image179, LV_OBJ_FLAG_HIDDEN);//little 2
		lv_obj_add_flag(ui_Image178, LV_OBJ_FLAG_HIDDEN);
		lv_obj_add_flag(ui_Image181, LV_OBJ_FLAG_HIDDEN);//little 3
		lv_obj_add_flag(ui_Image180, LV_OBJ_FLAG_HIDDEN);
		lv_obj_add_flag(ui_Image183, LV_OBJ_FLAG_HIDDEN);//little 4
		lv_obj_add_flag(ui_Image182, LV_OBJ_FLAG_HIDDEN);
		lv_obj_add_flag(ui_Image185, LV_OBJ_FLAG_HIDDEN);//little 5
		lv_obj_add_flag(ui_Image184, LV_OBJ_FLAG_HIDDEN);
		if( theme[0]==0 ){
			lv_img_set_src(ui_Image27, &ui_img_b01_20_png);
			lv_img_set_src(ui_Image38, &ui_img_b01_20_png);
			lv_img_set_src(ui_Image50, &ui_img_b01_20_png);
			lv_img_set_src(ui_Image62, &ui_img_b01_20_png);
			lv_img_set_src(ui_Image74, &ui_img_b01_20_png);
			lv_img_set_src(ui_Image86, &ui_img_b01_20_png);
			lv_img_set_src(ui_Image28, &ui_img_b02_10_png);
			lv_img_set_src(ui_Image39, &ui_img_b02_10_png);
			lv_img_set_src(ui_Image51, &ui_img_b02_10_png);
			lv_img_set_src(ui_Image63, &ui_img_b02_10_png);
			lv_img_set_src(ui_Image75, &ui_img_b02_10_png);
			lv_img_set_src(ui_Image87, &ui_img_b02_10_png);
		}
		//theme 2
		lv_obj_add_flag(ui_Image187, LV_OBJ_FLAG_HIDDEN);//little 0
		lv_obj_add_flag(ui_Image186, LV_OBJ_FLAG_HIDDEN);
		lv_obj_add_flag(ui_Image111, LV_OBJ_FLAG_HIDDEN);//little 1
		lv_obj_add_flag(ui_Image110, LV_OBJ_FLAG_HIDDEN);
		lv_obj_add_flag(ui_Image54, LV_OBJ_FLAG_HIDDEN);//little 2
		lv_obj_add_flag(ui_Image7, LV_OBJ_FLAG_HIDDEN);
		lv_obj_add_flag(ui_Image201, LV_OBJ_FLAG_HIDDEN);//little 3
		lv_obj_add_flag(ui_Image200, LV_OBJ_FLAG_HIDDEN);
		lv_img_set_src(ui_Image139, &ui_img_b02_10_png);
		lv_img_set_src(ui_Image129, &ui_img_b02_10_png);
		lv_img_set_src(ui_Image107, &ui_img_b02_10_png);
		lv_img_set_src(ui_Image79, &ui_img_b02_10_png);
		if( (theme[0] !=0)&&(theme[1] !=0) ){
			lv_img_set_src(ui_Image139, &ui_img_b01_20_png);
			lv_img_set_src(ui_Image129, &ui_img_b01_20_png);
			lv_img_set_src(ui_Image107, &ui_img_b01_20_png);
			lv_img_set_src(ui_Image79, &ui_img_b01_20_png);
		}
		else if( (theme[0] !=0)||(theme[1] !=0) ){
			lv_img_set_src(ui_Image138, &ui_img_b01_20_png);
			lv_img_set_src(ui_Image128, &ui_img_b01_20_png);
			lv_img_set_src(ui_Image106, &ui_img_b01_20_png);
			lv_img_set_src(ui_Image78, &ui_img_b01_20_png);
		}
		else{
			lv_img_set_src(ui_Image137, &ui_img_b01_20_png);
			lv_img_set_src(ui_Image127, &ui_img_b01_20_png);
			lv_img_set_src(ui_Image105, &ui_img_b01_20_png);
			lv_img_set_src(ui_Image67, &ui_img_b01_20_png);
		}
		//theme 3
		lv_obj_add_flag(ui_Image124, LV_OBJ_FLAG_HIDDEN);
		lv_obj_add_flag(ui_Image122, LV_OBJ_FLAG_HIDDEN);
		lv_obj_add_flag(ui_Image131, LV_OBJ_FLAG_HIDDEN);//2
		lv_obj_add_flag(ui_Image120, LV_OBJ_FLAG_HIDDEN);
		if( (theme[5] !=0)&&(theme[4] !=0) ){
			lv_img_set_src(ui_Image117, &ui_img_b02_10_png);
			lv_img_set_src(ui_Image115, &ui_img_b01_20_png);
			lv_img_set_src(ui_Image119, &ui_img_b02_10_png);//2
			lv_img_set_src(ui_Image113, &ui_img_b01_20_png);
		}
		else if( (theme[5] !=0)||(theme[4] !=0) ){
			lv_img_set_src(ui_Image117, &ui_img_b02_10_png);
			lv_img_set_src(ui_Image116, &ui_img_b01_20_png);
			lv_img_set_src(ui_Image119, &ui_img_b02_10_png);//2
			lv_img_set_src(ui_Image118, &ui_img_b01_20_png);
		}
		//theme 4
		lv_obj_add_flag(ui_Image146, LV_OBJ_FLAG_HIDDEN);
		lv_obj_add_flag(ui_Image145, LV_OBJ_FLAG_HIDDEN);
		if( theme[5] !=0 ){
			lv_img_set_src(ui_Image144, &ui_img_b02_10_png);
			lv_img_set_src(ui_Image143, &ui_img_b01_20_png);
		}
		else{
			lv_img_set_src(ui_Image144, &ui_img_b01_20_png);
		}
		//theme 5
		lv_obj_add_flag(ui_Image148, LV_OBJ_FLAG_HIDDEN);
		lv_obj_add_flag(ui_Image163, LV_OBJ_FLAG_HIDDEN);
		lv_img_set_src(ui_Image147, &ui_img_b01_20_png);
	 }
	 else if(total_big==3){
		//theme 0
		lv_obj_add_flag(ui_Image165, LV_OBJ_FLAG_HIDDEN); 
		lv_obj_add_flag(ui_Image166, LV_OBJ_FLAG_HIDDEN);
		lv_obj_add_flag(ui_Image11, LV_OBJ_FLAG_HIDDEN);
		lv_obj_add_flag(ui_Image174, LV_OBJ_FLAG_HIDDEN); //little 1
		lv_obj_add_flag(ui_Image173, LV_OBJ_FLAG_HIDDEN);
		lv_obj_add_flag(ui_Image172, LV_OBJ_FLAG_HIDDEN);
		//theme 1
		lv_obj_add_flag(ui_Image175, LV_OBJ_FLAG_HIDDEN);//little 0
		lv_obj_add_flag(ui_Image42, LV_OBJ_FLAG_HIDDEN);
		lv_obj_add_flag(ui_Image30, LV_OBJ_FLAG_HIDDEN);
		lv_obj_add_flag(ui_Image177, LV_OBJ_FLAG_HIDDEN);//little 1
		lv_obj_add_flag(ui_Image176, LV_OBJ_FLAG_HIDDEN);
		lv_obj_add_flag(ui_Image41, LV_OBJ_FLAG_HIDDEN);
		lv_obj_add_flag(ui_Image179, LV_OBJ_FLAG_HIDDEN);//little 2
		lv_obj_add_flag(ui_Image178, LV_OBJ_FLAG_HIDDEN);
		lv_obj_add_flag(ui_Image53, LV_OBJ_FLAG_HIDDEN);
		lv_obj_add_flag(ui_Image181, LV_OBJ_FLAG_HIDDEN);//little 3
		lv_obj_add_flag(ui_Image180, LV_OBJ_FLAG_HIDDEN);
		lv_obj_add_flag(ui_Image65, LV_OBJ_FLAG_HIDDEN);
		lv_obj_add_flag(ui_Image183, LV_OBJ_FLAG_HIDDEN);//little 4
		lv_obj_add_flag(ui_Image182, LV_OBJ_FLAG_HIDDEN);
		lv_obj_add_flag(ui_Image77, LV_OBJ_FLAG_HIDDEN);
		lv_obj_add_flag(ui_Image185, LV_OBJ_FLAG_HIDDEN);//little 5
		lv_obj_add_flag(ui_Image184, LV_OBJ_FLAG_HIDDEN);
		lv_obj_add_flag(ui_Image89, LV_OBJ_FLAG_HIDDEN);
		if( theme[0]==0 ){
			lv_img_set_src(ui_Image27, &ui_img_b01_20_png);
			lv_img_set_src(ui_Image38, &ui_img_b01_20_png);
			lv_img_set_src(ui_Image50, &ui_img_b01_20_png);
			lv_img_set_src(ui_Image62, &ui_img_b01_20_png);
			lv_img_set_src(ui_Image74, &ui_img_b01_20_png);
			lv_img_set_src(ui_Image86, &ui_img_b01_20_png);
			lv_img_set_src(ui_Image28, &ui_img_b02_10_png);
			lv_img_set_src(ui_Image39, &ui_img_b02_10_png);
			lv_img_set_src(ui_Image51, &ui_img_b02_10_png);
			lv_img_set_src(ui_Image63, &ui_img_b02_10_png);
			lv_img_set_src(ui_Image75, &ui_img_b02_10_png);
			lv_img_set_src(ui_Image87, &ui_img_b02_10_png);
		}
		//theme 2
		lv_obj_add_flag(ui_Image187, LV_OBJ_FLAG_HIDDEN);//little 0
		lv_obj_add_flag(ui_Image186, LV_OBJ_FLAG_HIDDEN);
		lv_obj_add_flag(ui_Image97, LV_OBJ_FLAG_HIDDEN);
		lv_obj_add_flag(ui_Image111, LV_OBJ_FLAG_HIDDEN);//little 1
		lv_obj_add_flag(ui_Image110, LV_OBJ_FLAG_HIDDEN);
		lv_obj_add_flag(ui_Image108, LV_OBJ_FLAG_HIDDEN);
		lv_obj_add_flag(ui_Image54, LV_OBJ_FLAG_HIDDEN);//little 2
		lv_obj_add_flag(ui_Image7, LV_OBJ_FLAG_HIDDEN);
		lv_obj_add_flag(ui_Image130, LV_OBJ_FLAG_HIDDEN);
		lv_obj_add_flag(ui_Image201, LV_OBJ_FLAG_HIDDEN);//little 3
		lv_obj_add_flag(ui_Image200, LV_OBJ_FLAG_HIDDEN);
		lv_obj_add_flag(ui_Image140, LV_OBJ_FLAG_HIDDEN);
		lv_img_set_src(ui_Image139, &ui_img_b02_10_png);
		lv_img_set_src(ui_Image129, &ui_img_b02_10_png);
		lv_img_set_src(ui_Image107, &ui_img_b02_10_png);
		lv_img_set_src(ui_Image79, &ui_img_b02_10_png);
		if( (theme[0] !=0)&&(theme[1] !=0) ){
			lv_img_set_src(ui_Image139, &ui_img_b01_20_png);
			lv_img_set_src(ui_Image129, &ui_img_b01_20_png);
			lv_img_set_src(ui_Image107, &ui_img_b01_20_png);
			lv_img_set_src(ui_Image79, &ui_img_b01_20_png);
		}
		else if( (theme[0] !=0)||(theme[1] !=0) ){
			lv_img_set_src(ui_Image138, &ui_img_b01_20_png);
			lv_img_set_src(ui_Image128, &ui_img_b01_20_png);
			lv_img_set_src(ui_Image106, &ui_img_b01_20_png);
			lv_img_set_src(ui_Image78, &ui_img_b01_20_png);
		}
		else{
			lv_img_set_src(ui_Image137, &ui_img_b01_20_png);
			lv_img_set_src(ui_Image127, &ui_img_b01_20_png);
			lv_img_set_src(ui_Image105, &ui_img_b01_20_png);
			lv_img_set_src(ui_Image67, &ui_img_b01_20_png);
		}
		//theme 3
		lv_obj_add_flag(ui_Image124, LV_OBJ_FLAG_HIDDEN);
		lv_obj_add_flag(ui_Image122, LV_OBJ_FLAG_HIDDEN);
		lv_obj_add_flag(ui_Image117, LV_OBJ_FLAG_HIDDEN);
		lv_obj_add_flag(ui_Image131, LV_OBJ_FLAG_HIDDEN);//2
		lv_obj_add_flag(ui_Image120, LV_OBJ_FLAG_HIDDEN);
		lv_obj_add_flag(ui_Image119, LV_OBJ_FLAG_HIDDEN);
		if( (theme[5] !=0)&&(theme[4] !=0) ){
			lv_img_set_src(ui_Image114, &ui_img_b01_20_png);
			lv_img_set_src(ui_Image112, &ui_img_b01_20_png);//2
		}
		else if( (theme[5] ==0)||(theme[4] ==0) ){
			lv_img_set_src(ui_Image116, &ui_img_b01_20_png);
			lv_img_set_src(ui_Image118, &ui_img_b01_20_png);//2
		}
		else{
			lv_img_set_src(ui_Image115, &ui_img_b01_20_png);
			lv_img_set_src(ui_Image113, &ui_img_b01_20_png);//2
		}
		//theme 4
		lv_obj_add_flag(ui_Image146, LV_OBJ_FLAG_HIDDEN);
		lv_obj_add_flag(ui_Image145, LV_OBJ_FLAG_HIDDEN);
		lv_obj_add_flag(ui_Image144, LV_OBJ_FLAG_HIDDEN);
		if( theme[5] !=0 ){
			lv_img_set_src(ui_Image143, &ui_img_b02_10_png);
			lv_img_set_src(ui_Image142, &ui_img_b01_20_png);
		}
		else{
			lv_img_set_src(ui_Image143, &ui_img_b01_20_png);
		}
		//theme 5
		lv_obj_add_flag(ui_Image148, LV_OBJ_FLAG_HIDDEN);
		lv_obj_add_flag(ui_Image163, LV_OBJ_FLAG_HIDDEN);
		lv_obj_add_flag(ui_Image147, LV_OBJ_FLAG_HIDDEN);
		lv_img_set_src(ui_Image162, &ui_img_b01_20_png);
	 }
	 else if(total_big==2){
		//theme 0
		lv_obj_add_flag(ui_Image165, LV_OBJ_FLAG_HIDDEN); 
		lv_obj_add_flag(ui_Image166, LV_OBJ_FLAG_HIDDEN);
		lv_obj_add_flag(ui_Image11, LV_OBJ_FLAG_HIDDEN); 
		lv_obj_add_flag(ui_Image10, LV_OBJ_FLAG_HIDDEN);
		lv_obj_add_flag(ui_Image174, LV_OBJ_FLAG_HIDDEN); //little 1
		lv_obj_add_flag(ui_Image173, LV_OBJ_FLAG_HIDDEN);
		lv_obj_add_flag(ui_Image172, LV_OBJ_FLAG_HIDDEN);
		lv_obj_add_flag(ui_Image167, LV_OBJ_FLAG_HIDDEN);
		//theme 1
		lv_obj_add_flag(ui_Image175, LV_OBJ_FLAG_HIDDEN);//little 0
		lv_obj_add_flag(ui_Image42, LV_OBJ_FLAG_HIDDEN);
		lv_obj_add_flag(ui_Image30, LV_OBJ_FLAG_HIDDEN);
		lv_obj_add_flag(ui_Image29, LV_OBJ_FLAG_HIDDEN);
		lv_obj_add_flag(ui_Image177, LV_OBJ_FLAG_HIDDEN);//little 1
		lv_obj_add_flag(ui_Image176, LV_OBJ_FLAG_HIDDEN);
		lv_obj_add_flag(ui_Image41, LV_OBJ_FLAG_HIDDEN);
		lv_obj_add_flag(ui_Image40, LV_OBJ_FLAG_HIDDEN);
		lv_obj_add_flag(ui_Image179, LV_OBJ_FLAG_HIDDEN);//little 2
		lv_obj_add_flag(ui_Image178, LV_OBJ_FLAG_HIDDEN);
		lv_obj_add_flag(ui_Image53, LV_OBJ_FLAG_HIDDEN);
		lv_obj_add_flag(ui_Image52, LV_OBJ_FLAG_HIDDEN);
		lv_obj_add_flag(ui_Image181, LV_OBJ_FLAG_HIDDEN);//little 3
		lv_obj_add_flag(ui_Image180, LV_OBJ_FLAG_HIDDEN);
		lv_obj_add_flag(ui_Image65, LV_OBJ_FLAG_HIDDEN);
		lv_obj_add_flag(ui_Image64, LV_OBJ_FLAG_HIDDEN);
		lv_obj_add_flag(ui_Image183, LV_OBJ_FLAG_HIDDEN);//little 4
		lv_obj_add_flag(ui_Image182, LV_OBJ_FLAG_HIDDEN);
		lv_obj_add_flag(ui_Image77, LV_OBJ_FLAG_HIDDEN);
		lv_obj_add_flag(ui_Image76, LV_OBJ_FLAG_HIDDEN);
		lv_obj_add_flag(ui_Image185, LV_OBJ_FLAG_HIDDEN);//little 5
		lv_obj_add_flag(ui_Image184, LV_OBJ_FLAG_HIDDEN);
		lv_obj_add_flag(ui_Image89, LV_OBJ_FLAG_HIDDEN);
		lv_obj_add_flag(ui_Image88, LV_OBJ_FLAG_HIDDEN);
		if( theme[0]==0 ){
			lv_img_set_src(ui_Image27, &ui_img_b01_20_png);
			lv_img_set_src(ui_Image38, &ui_img_b01_20_png);
			lv_img_set_src(ui_Image50, &ui_img_b01_20_png);
			lv_img_set_src(ui_Image62, &ui_img_b01_20_png);
			lv_img_set_src(ui_Image74, &ui_img_b01_20_png);
			lv_img_set_src(ui_Image86, &ui_img_b01_20_png);
			lv_img_set_src(ui_Image28, &ui_img_b02_10_png);
			lv_img_set_src(ui_Image39, &ui_img_b02_10_png);
			lv_img_set_src(ui_Image51, &ui_img_b02_10_png);
			lv_img_set_src(ui_Image63, &ui_img_b02_10_png);
			lv_img_set_src(ui_Image75, &ui_img_b02_10_png);
			lv_img_set_src(ui_Image87, &ui_img_b02_10_png);
		}
		//theme 2
		lv_obj_add_flag(ui_Image187, LV_OBJ_FLAG_HIDDEN);//little 0
		lv_obj_add_flag(ui_Image186, LV_OBJ_FLAG_HIDDEN);
		lv_obj_add_flag(ui_Image97, LV_OBJ_FLAG_HIDDEN);
		lv_obj_add_flag(ui_Image79, LV_OBJ_FLAG_HIDDEN);
		lv_obj_add_flag(ui_Image111, LV_OBJ_FLAG_HIDDEN);//little 1
		lv_obj_add_flag(ui_Image110, LV_OBJ_FLAG_HIDDEN);
		lv_obj_add_flag(ui_Image108, LV_OBJ_FLAG_HIDDEN);
		lv_obj_add_flag(ui_Image107, LV_OBJ_FLAG_HIDDEN);
		lv_obj_add_flag(ui_Image54, LV_OBJ_FLAG_HIDDEN);//little 2
		lv_obj_add_flag(ui_Image7, LV_OBJ_FLAG_HIDDEN);
		lv_obj_add_flag(ui_Image130, LV_OBJ_FLAG_HIDDEN);
		lv_obj_add_flag(ui_Image129, LV_OBJ_FLAG_HIDDEN);
		lv_obj_add_flag(ui_Image201, LV_OBJ_FLAG_HIDDEN);//little 3
		lv_obj_add_flag(ui_Image200, LV_OBJ_FLAG_HIDDEN);
		lv_obj_add_flag(ui_Image140, LV_OBJ_FLAG_HIDDEN);
		lv_obj_add_flag(ui_Image139, LV_OBJ_FLAG_HIDDEN);
		if( (theme[0] !=0)||(theme[1] !=0) ){
			lv_img_set_src(ui_Image138, &ui_img_b01_20_png);
			lv_img_set_src(ui_Image128, &ui_img_b01_20_png);
			lv_img_set_src(ui_Image106, &ui_img_b01_20_png);
			lv_img_set_src(ui_Image78, &ui_img_b01_20_png);
		}
		else{
			lv_img_set_src(ui_Image137, &ui_img_b01_20_png);
			lv_img_set_src(ui_Image127, &ui_img_b01_20_png);
			lv_img_set_src(ui_Image105, &ui_img_b01_20_png);
			lv_img_set_src(ui_Image67, &ui_img_b01_20_png);
		}
		
		//theme 3
		lv_obj_add_flag(ui_Image124, LV_OBJ_FLAG_HIDDEN);
		lv_obj_add_flag(ui_Image122, LV_OBJ_FLAG_HIDDEN);
		lv_obj_add_flag(ui_Image117, LV_OBJ_FLAG_HIDDEN);
		lv_obj_add_flag(ui_Image116, LV_OBJ_FLAG_HIDDEN);
		lv_obj_add_flag(ui_Image131, LV_OBJ_FLAG_HIDDEN);//2
		lv_obj_add_flag(ui_Image120, LV_OBJ_FLAG_HIDDEN);
		lv_obj_add_flag(ui_Image119, LV_OBJ_FLAG_HIDDEN);
		lv_obj_add_flag(ui_Image118, LV_OBJ_FLAG_HIDDEN);
		if( (theme[5] !=0)||(theme[4] !=0) ){
			lv_img_set_src(ui_Image114, &ui_img_b01_20_png);
			lv_img_set_src(ui_Image112, &ui_img_b01_20_png);//2
		}
		else{
			lv_img_set_src(ui_Image115, &ui_img_b01_20_png);
			lv_img_set_src(ui_Image113, &ui_img_b01_20_png);//2
		}
		//theme 4
		lv_obj_add_flag(ui_Image146, LV_OBJ_FLAG_HIDDEN);
		lv_obj_add_flag(ui_Image145, LV_OBJ_FLAG_HIDDEN);
		lv_obj_add_flag(ui_Image144, LV_OBJ_FLAG_HIDDEN);
		lv_obj_add_flag(ui_Image143, LV_OBJ_FLAG_HIDDEN);
		if( theme[5] !=0 ){
			lv_img_set_src(ui_Image142, &ui_img_b02_10_png);
			lv_img_set_src(ui_Image141, &ui_img_b01_20_png);
		}
		else{
			lv_img_set_src(ui_Image142, &ui_img_b01_20_png);
		}
		//theme 5
		lv_obj_add_flag(ui_Image148, LV_OBJ_FLAG_HIDDEN);
		lv_obj_add_flag(ui_Image163, LV_OBJ_FLAG_HIDDEN);
		lv_obj_add_flag(ui_Image147, LV_OBJ_FLAG_HIDDEN);
		lv_obj_add_flag(ui_Image162, LV_OBJ_FLAG_HIDDEN);
		lv_img_set_src(ui_Image161, &ui_img_b01_20_png);
	 }
	 else if(total_big==1){
		//theme 0
		lv_obj_add_flag(ui_Image165, LV_OBJ_FLAG_HIDDEN); 
		lv_obj_add_flag(ui_Image166, LV_OBJ_FLAG_HIDDEN);
		lv_obj_add_flag(ui_Image11, LV_OBJ_FLAG_HIDDEN); 
		lv_obj_add_flag(ui_Image10, LV_OBJ_FLAG_HIDDEN);
		lv_obj_add_flag(ui_Image9, LV_OBJ_FLAG_HIDDEN);
		lv_obj_add_flag(ui_Image174, LV_OBJ_FLAG_HIDDEN); //little 1
		lv_obj_add_flag(ui_Image173, LV_OBJ_FLAG_HIDDEN);
		lv_obj_add_flag(ui_Image172, LV_OBJ_FLAG_HIDDEN);
		lv_obj_add_flag(ui_Image167, LV_OBJ_FLAG_HIDDEN);
		lv_obj_add_flag(ui_Image171, LV_OBJ_FLAG_HIDDEN);
		//theme 1
		lv_obj_add_flag(ui_Image175, LV_OBJ_FLAG_HIDDEN);//little 0
		lv_obj_add_flag(ui_Image42, LV_OBJ_FLAG_HIDDEN);
		lv_obj_add_flag(ui_Image30, LV_OBJ_FLAG_HIDDEN);
		lv_obj_add_flag(ui_Image29, LV_OBJ_FLAG_HIDDEN);
		lv_obj_add_flag(ui_Image28, LV_OBJ_FLAG_HIDDEN);
		lv_img_set_src(ui_Image27, &ui_img_b01_20_png);
		lv_obj_add_flag(ui_Image177, LV_OBJ_FLAG_HIDDEN);//little 1
		lv_obj_add_flag(ui_Image176, LV_OBJ_FLAG_HIDDEN);
		lv_obj_add_flag(ui_Image41, LV_OBJ_FLAG_HIDDEN);
		lv_obj_add_flag(ui_Image40, LV_OBJ_FLAG_HIDDEN);
		lv_obj_add_flag(ui_Image39, LV_OBJ_FLAG_HIDDEN);
		lv_img_set_src(ui_Image38, &ui_img_b01_20_png);
		lv_obj_add_flag(ui_Image179, LV_OBJ_FLAG_HIDDEN);//little 2
		lv_obj_add_flag(ui_Image178, LV_OBJ_FLAG_HIDDEN);
		lv_obj_add_flag(ui_Image53, LV_OBJ_FLAG_HIDDEN);
		lv_obj_add_flag(ui_Image52, LV_OBJ_FLAG_HIDDEN);
		lv_obj_add_flag(ui_Image51, LV_OBJ_FLAG_HIDDEN);
		lv_img_set_src(ui_Image50, &ui_img_b01_20_png);
		lv_obj_add_flag(ui_Image181, LV_OBJ_FLAG_HIDDEN);//little 3
		lv_obj_add_flag(ui_Image180, LV_OBJ_FLAG_HIDDEN);
		lv_obj_add_flag(ui_Image65, LV_OBJ_FLAG_HIDDEN);
		lv_obj_add_flag(ui_Image64, LV_OBJ_FLAG_HIDDEN);
		lv_obj_add_flag(ui_Image63, LV_OBJ_FLAG_HIDDEN);
		lv_img_set_src(ui_Image62, &ui_img_b01_20_png);
		lv_obj_add_flag(ui_Image183, LV_OBJ_FLAG_HIDDEN);//little 4
		lv_obj_add_flag(ui_Image182, LV_OBJ_FLAG_HIDDEN);
		lv_obj_add_flag(ui_Image77, LV_OBJ_FLAG_HIDDEN);
		lv_obj_add_flag(ui_Image76, LV_OBJ_FLAG_HIDDEN);
		lv_obj_add_flag(ui_Image75, LV_OBJ_FLAG_HIDDEN);
		lv_img_set_src(ui_Image74, &ui_img_b01_20_png);
		lv_obj_add_flag(ui_Image185, LV_OBJ_FLAG_HIDDEN);//little 5
		lv_obj_add_flag(ui_Image184, LV_OBJ_FLAG_HIDDEN);
		lv_obj_add_flag(ui_Image89, LV_OBJ_FLAG_HIDDEN);
		lv_obj_add_flag(ui_Image88, LV_OBJ_FLAG_HIDDEN);
		lv_obj_add_flag(ui_Image87, LV_OBJ_FLAG_HIDDEN);
		lv_img_set_src(ui_Image86, &ui_img_b01_20_png);
		//theme 2
		lv_obj_add_flag(ui_Image187, LV_OBJ_FLAG_HIDDEN);//little 0
		lv_obj_add_flag(ui_Image186, LV_OBJ_FLAG_HIDDEN);
		lv_obj_add_flag(ui_Image97, LV_OBJ_FLAG_HIDDEN);
		lv_obj_add_flag(ui_Image79, LV_OBJ_FLAG_HIDDEN);
		lv_obj_add_flag(ui_Image78, LV_OBJ_FLAG_HIDDEN);
		lv_img_set_src(ui_Image67, &ui_img_b01_20_png);
		lv_obj_add_flag(ui_Image111, LV_OBJ_FLAG_HIDDEN);//little 1
		lv_obj_add_flag(ui_Image110, LV_OBJ_FLAG_HIDDEN);
		lv_obj_add_flag(ui_Image108, LV_OBJ_FLAG_HIDDEN);
		lv_obj_add_flag(ui_Image107, LV_OBJ_FLAG_HIDDEN);
		lv_obj_add_flag(ui_Image106, LV_OBJ_FLAG_HIDDEN);
		lv_img_set_src(ui_Image105, &ui_img_b01_20_png);
		lv_obj_add_flag(ui_Image54, LV_OBJ_FLAG_HIDDEN);//little 2
		lv_obj_add_flag(ui_Image7, LV_OBJ_FLAG_HIDDEN);
		lv_obj_add_flag(ui_Image130, LV_OBJ_FLAG_HIDDEN);
		lv_obj_add_flag(ui_Image129, LV_OBJ_FLAG_HIDDEN);
		lv_obj_add_flag(ui_Image128, LV_OBJ_FLAG_HIDDEN);
		lv_img_set_src(ui_Image127, &ui_img_b01_20_png);
		lv_obj_add_flag(ui_Image201, LV_OBJ_FLAG_HIDDEN);//little 3
		lv_obj_add_flag(ui_Image200, LV_OBJ_FLAG_HIDDEN);
		lv_obj_add_flag(ui_Image140, LV_OBJ_FLAG_HIDDEN);
		lv_obj_add_flag(ui_Image139, LV_OBJ_FLAG_HIDDEN);
		lv_obj_add_flag(ui_Image138, LV_OBJ_FLAG_HIDDEN);
		lv_img_set_src(ui_Image137, &ui_img_b01_20_png);
		//theme 3
		lv_obj_add_flag(ui_Image124, LV_OBJ_FLAG_HIDDEN);
		lv_obj_add_flag(ui_Image122, LV_OBJ_FLAG_HIDDEN);
		lv_obj_add_flag(ui_Image117, LV_OBJ_FLAG_HIDDEN);
		lv_obj_add_flag(ui_Image116, LV_OBJ_FLAG_HIDDEN);
		lv_obj_add_flag(ui_Image115, LV_OBJ_FLAG_HIDDEN);
		lv_img_set_src(ui_Image114, &ui_img_b01_20_png);
		lv_obj_add_flag(ui_Image131, LV_OBJ_FLAG_HIDDEN);//2
		lv_obj_add_flag(ui_Image120, LV_OBJ_FLAG_HIDDEN);
		lv_obj_add_flag(ui_Image119, LV_OBJ_FLAG_HIDDEN);
		lv_obj_add_flag(ui_Image118, LV_OBJ_FLAG_HIDDEN);
		lv_obj_add_flag(ui_Image113, LV_OBJ_FLAG_HIDDEN);
		lv_img_set_src(ui_Image112, &ui_img_b01_20_png);

		//theme 4
		lv_obj_add_flag(ui_Image146, LV_OBJ_FLAG_HIDDEN);
		lv_obj_add_flag(ui_Image145, LV_OBJ_FLAG_HIDDEN);
		lv_obj_add_flag(ui_Image144, LV_OBJ_FLAG_HIDDEN);
		lv_obj_add_flag(ui_Image143, LV_OBJ_FLAG_HIDDEN);
		lv_obj_add_flag(ui_Image142, LV_OBJ_FLAG_HIDDEN);
		lv_img_set_src(ui_Image141, &ui_img_b01_20_png);

		//theme 5
		lv_obj_add_flag(ui_Image148, LV_OBJ_FLAG_HIDDEN);
		lv_obj_add_flag(ui_Image163, LV_OBJ_FLAG_HIDDEN);
		lv_obj_add_flag(ui_Image147, LV_OBJ_FLAG_HIDDEN);
		lv_obj_add_flag(ui_Image162, LV_OBJ_FLAG_HIDDEN);
		lv_obj_add_flag(ui_Image161, LV_OBJ_FLAG_HIDDEN);
		lv_img_set_src(ui_Image160, &ui_img_b01_20_png);
	 }
	 
	 //lv_obj_add_flag(ui_Image4, LV_OBJ_FLAG_HIDDEN);
	 //lv_obj_add_flag(ui_Image5, LV_OBJ_FLAG_HIDDEN);
	 if( total_small[0] == 1 ){
		 if( theme[0] == 1 ){
			 lv_obj_add_flag(ui_Image4, LV_OBJ_FLAG_HIDDEN);
		 }
		 else{
		 	lv_obj_add_flag(ui_Image168, LV_OBJ_FLAG_HIDDEN);
		 	lv_img_set_src(ui_Image169, &ui_img_b01_20_png);
	 	 }
		 
	 }
	 if( total_small[3] == 1 ){
		 if( theme[3] == 1 ){
			 lv_obj_add_flag(ui_Image102, LV_OBJ_FLAG_HIDDEN);
		 }
		 else{
		 	lv_obj_add_flag(ui_Image100, LV_OBJ_FLAG_HIDDEN);
		 	lv_img_set_src(ui_Image98, &ui_img_b01_20_png);
	 	 }
		 
	 }
	 if( total_small[2] == 1 ){
		 if( theme[2] == 1 ){
			 //ui_Image43
			 lv_obj_add_flag(ui_Image66, LV_OBJ_FLAG_HIDDEN);
			 lv_obj_add_flag(ui_Image55, LV_OBJ_FLAG_HIDDEN);
			 lv_obj_add_flag(ui_Image3, LV_OBJ_FLAG_HIDDEN);
		 }
		 else if( theme[2] == 2 ){
			 lv_img_set_src(ui_Image109, &ui_img_b01_20_png);
			 lv_obj_add_flag(ui_Image104, LV_OBJ_FLAG_HIDDEN);
			 lv_obj_add_flag(ui_Image103, LV_OBJ_FLAG_HIDDEN);
			 lv_obj_add_flag(ui_Image99, LV_OBJ_FLAG_HIDDEN);
		 }
		 else if( theme[2] == 4 ){
			 lv_img_set_src(ui_Image123, &ui_img_b01_20_png);
			 lv_obj_add_flag(ui_Image126, LV_OBJ_FLAG_HIDDEN);
			 lv_obj_add_flag(ui_Image125, LV_OBJ_FLAG_HIDDEN);
			 lv_obj_add_flag(ui_Image121, LV_OBJ_FLAG_HIDDEN);
		 }
		 else{ //8
			 lv_img_set_src(ui_Image133, &ui_img_b01_20_png);
			 lv_obj_add_flag(ui_Image136, LV_OBJ_FLAG_HIDDEN);
			 lv_obj_add_flag(ui_Image135, LV_OBJ_FLAG_HIDDEN);
			 lv_obj_add_flag(ui_Image20, LV_OBJ_FLAG_HIDDEN);
	 	 } 
	 }
	 else if( total_small[2] == 2 ){
		 if( theme[2] == 3 ){
			 lv_obj_add_flag(ui_Image55, LV_OBJ_FLAG_HIDDEN);
			 lv_obj_add_flag(ui_Image3, LV_OBJ_FLAG_HIDDEN);
			 lv_obj_add_flag(ui_Image103, LV_OBJ_FLAG_HIDDEN);
			 lv_obj_add_flag(ui_Image99, LV_OBJ_FLAG_HIDDEN);
		 }
		 else if( theme[2] == 5 ){
			 lv_obj_add_flag(ui_Image55, LV_OBJ_FLAG_HIDDEN);
			 lv_obj_add_flag(ui_Image3, LV_OBJ_FLAG_HIDDEN);
			 lv_img_set_src(ui_Image126, &ui_img_b01_20_png);
			 lv_obj_add_flag(ui_Image125, LV_OBJ_FLAG_HIDDEN);
			 lv_obj_add_flag(ui_Image121, LV_OBJ_FLAG_HIDDEN);

		 }
		 else if( theme[2] == 6 ){
			 lv_img_set_src(ui_Image109, &ui_img_b01_20_png);
			 lv_img_set_src(ui_Image104, &ui_img_b02_10_png);
			 lv_obj_add_flag(ui_Image103, LV_OBJ_FLAG_HIDDEN);
			 lv_obj_add_flag(ui_Image99, LV_OBJ_FLAG_HIDDEN);
			 lv_img_set_src(ui_Image126, &ui_img_b01_20_png);
			 lv_obj_add_flag(ui_Image125, LV_OBJ_FLAG_HIDDEN);
			 lv_obj_add_flag(ui_Image121, LV_OBJ_FLAG_HIDDEN);
		 }
		 else if( theme[2] == 9 ){
			 lv_obj_add_flag(ui_Image55, LV_OBJ_FLAG_HIDDEN);
			 lv_obj_add_flag(ui_Image3, LV_OBJ_FLAG_HIDDEN);
			 lv_img_set_src(ui_Image136, &ui_img_b01_20_png);
			 lv_obj_add_flag(ui_Image135, LV_OBJ_FLAG_HIDDEN);
			 lv_obj_add_flag(ui_Image20, LV_OBJ_FLAG_HIDDEN);
		 }
		 else if( theme[2] == 10 ){
			 lv_img_set_src(ui_Image109, &ui_img_b01_20_png);
			 lv_img_set_src(ui_Image104, &ui_img_b02_10_png);
			 lv_obj_add_flag(ui_Image103, LV_OBJ_FLAG_HIDDEN);
			 lv_obj_add_flag(ui_Image99, LV_OBJ_FLAG_HIDDEN);
			 lv_img_set_src(ui_Image136, &ui_img_b01_20_png);
			 lv_obj_add_flag(ui_Image135, LV_OBJ_FLAG_HIDDEN);
			 lv_obj_add_flag(ui_Image20, LV_OBJ_FLAG_HIDDEN);
		 }
		 else{ //12
		 	 lv_img_set_src(ui_Image123, &ui_img_b01_20_png);
			 lv_obj_add_flag(ui_Image125, LV_OBJ_FLAG_HIDDEN);
			 lv_obj_add_flag(ui_Image121, LV_OBJ_FLAG_HIDDEN);
			 lv_img_set_src(ui_Image136, &ui_img_b01_20_png);
			 lv_obj_add_flag(ui_Image135, LV_OBJ_FLAG_HIDDEN);
			 lv_obj_add_flag(ui_Image20, LV_OBJ_FLAG_HIDDEN);
	 	 } 
	 }
	 else if( total_small[2] == 3 ){
		 if( theme[2] == 7 ){
			 lv_obj_add_flag(ui_Image3, LV_OBJ_FLAG_HIDDEN);
			 lv_obj_add_flag(ui_Image99, LV_OBJ_FLAG_HIDDEN);
			 lv_obj_add_flag(ui_Image121, LV_OBJ_FLAG_HIDDEN);
			 lv_obj_add_flag(ui_Image20, LV_OBJ_FLAG_HIDDEN);
		 }
		 else if( theme[2] == 11 ){//b
			 lv_obj_add_flag(ui_Image3, LV_OBJ_FLAG_HIDDEN);
			 lv_obj_add_flag(ui_Image99, LV_OBJ_FLAG_HIDDEN);
             lv_img_set_src(ui_Image135, &ui_img_b01_20_png);
			 lv_obj_add_flag(ui_Image20, LV_OBJ_FLAG_HIDDEN);			 
		 }
		 else if( theme[2] == 13 ){//d
			 lv_obj_add_flag(ui_Image3, LV_OBJ_FLAG_HIDDEN);
			 lv_img_set_src(ui_Image126, &ui_img_b01_20_png);
			 lv_img_set_src(ui_Image125, &ui_img_b02_10_png);
			 lv_obj_add_flag(ui_Image121, LV_OBJ_FLAG_HIDDEN);
			 lv_img_set_src(ui_Image135, &ui_img_b01_20_png);
			 lv_obj_add_flag(ui_Image20, LV_OBJ_FLAG_HIDDEN); 
		 }
		 else{ //14 e
		 	 lv_img_set_src(ui_Image109, &ui_img_b01_20_png);
		 	 lv_img_set_src(ui_Image104, &ui_img_b02_10_png);
		 	 lv_obj_add_flag(ui_Image99, LV_OBJ_FLAG_HIDDEN);
			 lv_img_set_src(ui_Image126, &ui_img_b01_20_png);
			 lv_img_set_src(ui_Image125, &ui_img_b02_10_png);
			 lv_obj_add_flag(ui_Image121, LV_OBJ_FLAG_HIDDEN);
			 lv_img_set_src(ui_Image135, &ui_img_b01_20_png);
			 lv_obj_add_flag(ui_Image20, LV_OBJ_FLAG_HIDDEN);
	 	 } 
	 }
	 
	 if( total_small[1] == 1 ){
		 if( theme[1] == 1 ){
			 //ui_Image21
			 lv_obj_add_flag(ui_Image22, LV_OBJ_FLAG_HIDDEN);
			 lv_obj_add_flag(ui_Image23, LV_OBJ_FLAG_HIDDEN);
			 lv_obj_add_flag(ui_Image24, LV_OBJ_FLAG_HIDDEN);
			 lv_obj_add_flag(ui_Image25, LV_OBJ_FLAG_HIDDEN);
			 lv_obj_add_flag(ui_Image26, LV_OBJ_FLAG_HIDDEN);
		 }
		 else if( theme[1] == 2 ){
			 lv_img_set_src(ui_Image12, &ui_img_b01_20_png);
			 lv_obj_add_flag(ui_Image33, LV_OBJ_FLAG_HIDDEN);
			 lv_obj_add_flag(ui_Image34, LV_OBJ_FLAG_HIDDEN);
			 lv_obj_add_flag(ui_Image35, LV_OBJ_FLAG_HIDDEN);
			 lv_obj_add_flag(ui_Image36, LV_OBJ_FLAG_HIDDEN);
			 lv_obj_add_flag(ui_Image37, LV_OBJ_FLAG_HIDDEN);
		 }
		 else if( theme[1] == 4 ){
			 lv_img_set_src(ui_Image44, &ui_img_b01_20_png);
			 lv_obj_add_flag(ui_Image45, LV_OBJ_FLAG_HIDDEN);
			 lv_obj_add_flag(ui_Image46, LV_OBJ_FLAG_HIDDEN);
			 lv_obj_add_flag(ui_Image47, LV_OBJ_FLAG_HIDDEN);
			 lv_obj_add_flag(ui_Image48, LV_OBJ_FLAG_HIDDEN);
			 lv_obj_add_flag(ui_Image49, LV_OBJ_FLAG_HIDDEN);
		 }
		 else if( theme[1] == 8 ){
			 lv_img_set_src(ui_Image56, &ui_img_b01_20_png);
			 lv_obj_add_flag(ui_Image57, LV_OBJ_FLAG_HIDDEN);
			 lv_obj_add_flag(ui_Image58, LV_OBJ_FLAG_HIDDEN);
			 lv_obj_add_flag(ui_Image59, LV_OBJ_FLAG_HIDDEN);
			 lv_obj_add_flag(ui_Image60, LV_OBJ_FLAG_HIDDEN);
			 lv_obj_add_flag(ui_Image61, LV_OBJ_FLAG_HIDDEN);
		 }
		 else if( theme[1] == 16 ){
			 lv_img_set_src(ui_Image68, &ui_img_b01_20_png);
			 lv_obj_add_flag(ui_Image69, LV_OBJ_FLAG_HIDDEN);
			 lv_obj_add_flag(ui_Image70, LV_OBJ_FLAG_HIDDEN);
			 lv_obj_add_flag(ui_Image71, LV_OBJ_FLAG_HIDDEN);
			 lv_obj_add_flag(ui_Image72, LV_OBJ_FLAG_HIDDEN);
			 lv_obj_add_flag(ui_Image73, LV_OBJ_FLAG_HIDDEN);
		 }
		 else{ //32
			 lv_img_set_src(ui_Image80, &ui_img_b01_20_png);
			 lv_obj_add_flag(ui_Image81, LV_OBJ_FLAG_HIDDEN);
			 lv_obj_add_flag(ui_Image82, LV_OBJ_FLAG_HIDDEN);
			 lv_obj_add_flag(ui_Image83, LV_OBJ_FLAG_HIDDEN);
			 lv_obj_add_flag(ui_Image84, LV_OBJ_FLAG_HIDDEN);
			 lv_obj_add_flag(ui_Image85, LV_OBJ_FLAG_HIDDEN);
	 	 } 
	 }
	 if( total_small[1] == 2 ){
		 lv_obj_add_flag(ui_Image23, LV_OBJ_FLAG_HIDDEN);//0
		 lv_obj_add_flag(ui_Image24, LV_OBJ_FLAG_HIDDEN);
	     lv_obj_add_flag(ui_Image25, LV_OBJ_FLAG_HIDDEN);
		 lv_obj_add_flag(ui_Image26, LV_OBJ_FLAG_HIDDEN);
		 lv_obj_add_flag(ui_Image34, LV_OBJ_FLAG_HIDDEN);//1
		 lv_obj_add_flag(ui_Image35, LV_OBJ_FLAG_HIDDEN);
		 lv_obj_add_flag(ui_Image36, LV_OBJ_FLAG_HIDDEN);
		 lv_obj_add_flag(ui_Image37, LV_OBJ_FLAG_HIDDEN);
		 lv_obj_add_flag(ui_Image46, LV_OBJ_FLAG_HIDDEN);//2
		 lv_obj_add_flag(ui_Image47, LV_OBJ_FLAG_HIDDEN);
		 lv_obj_add_flag(ui_Image48, LV_OBJ_FLAG_HIDDEN);
		 lv_obj_add_flag(ui_Image49, LV_OBJ_FLAG_HIDDEN);
		 lv_obj_add_flag(ui_Image58, LV_OBJ_FLAG_HIDDEN);//3
		 lv_obj_add_flag(ui_Image59, LV_OBJ_FLAG_HIDDEN);
		 lv_obj_add_flag(ui_Image60, LV_OBJ_FLAG_HIDDEN);
		 lv_obj_add_flag(ui_Image61, LV_OBJ_FLAG_HIDDEN);
		 lv_obj_add_flag(ui_Image70, LV_OBJ_FLAG_HIDDEN);//4
		 lv_obj_add_flag(ui_Image71, LV_OBJ_FLAG_HIDDEN);
		 lv_obj_add_flag(ui_Image72, LV_OBJ_FLAG_HIDDEN);
		 lv_obj_add_flag(ui_Image73, LV_OBJ_FLAG_HIDDEN);
		 lv_obj_add_flag(ui_Image82, LV_OBJ_FLAG_HIDDEN);//5
		 lv_obj_add_flag(ui_Image83, LV_OBJ_FLAG_HIDDEN);
		 lv_obj_add_flag(ui_Image84, LV_OBJ_FLAG_HIDDEN);
		 lv_obj_add_flag(ui_Image85, LV_OBJ_FLAG_HIDDEN);
		 if( theme[1] == 3 ){
			 lv_obj_add_flag(ui_Image85, LV_OBJ_FLAG_HIDDEN);
		 }
		 else if( theme[1] == 5 ){
			 lv_img_set_src(ui_Image45, &ui_img_b01_20_png);
			 
		 }
		 else if( theme[1] == 6 ){
			 lv_img_set_src(ui_Image44, &ui_img_b01_20_png);
			 lv_img_set_src(ui_Image45, &ui_img_b02_10_png);
			 lv_img_set_src(ui_Image57, &ui_img_b01_20_png);
			 
		 }
		 else if( theme[1] == 9 ){
			 lv_img_set_src(ui_Image57, &ui_img_b01_20_png);
		 }
		 else if( theme[1] == 10 ){ //a
			 lv_img_set_src(ui_Image12, &ui_img_b01_20_png);
			 lv_img_set_src(ui_Image33, &ui_img_b02_10_png);
			 lv_img_set_src(ui_Image57, &ui_img_b01_20_png);
		 }
		 else if( theme[1] == 12 ){ //c
			 lv_img_set_src(ui_Image44, &ui_img_b01_20_png);
			 lv_img_set_src(ui_Image57, &ui_img_b01_20_png);
		 }
		 else if( theme[1] == 17 ){ //11
			 lv_img_set_src(ui_Image69, &ui_img_b01_20_png);
		 }
		 else if( theme[1] == 18 ){ //12
			 lv_img_set_src(ui_Image12, &ui_img_b01_20_png);
			 lv_img_set_src(ui_Image33, &ui_img_b02_10_png);
			 lv_img_set_src(ui_Image69, &ui_img_b01_20_png);
		 }
		 else if( theme[1] == 20 ){ //14
			 lv_img_set_src(ui_Image44, &ui_img_b01_20_png);
			 lv_img_set_src(ui_Image69, &ui_img_b01_20_png);
			 
		 }
		 else if( theme[1] == 24 ){ //18
			 lv_img_set_src(ui_Image56, &ui_img_b01_20_png);
			 lv_img_set_src(ui_Image69, &ui_img_b01_20_png);
			 
		 }
		 else if( theme[1] == 33 ){  //21
		 	 lv_img_set_src(ui_Image81, &ui_img_b01_20_png);
	 	 }
	 	 else if( theme[1] == 34 ){ //22
			 lv_img_set_src(ui_Image12, &ui_img_b01_20_png);//2
			 lv_img_set_src(ui_Image33, &ui_img_b02_10_png);
			 lv_img_set_src(ui_Image81, &ui_img_b01_20_png);
			 
		 }
		 else if( theme[1] == 36 ){ //24
			 lv_img_set_src(ui_Image44, &ui_img_b01_20_png);//3
			 lv_img_set_src(ui_Image81, &ui_img_b01_20_png);//6
		 }
		 else if( theme[1] == 40 ){ //28
			 lv_img_set_src(ui_Image56, &ui_img_b01_20_png);//4
			 lv_img_set_src(ui_Image81, &ui_img_b01_20_png);//6
			 
		 }
		 else{ //48  0x30
			 lv_img_set_src(ui_Image68, &ui_img_b01_20_png);//5
			 lv_img_set_src(ui_Image81, &ui_img_b01_20_png);//6
		 }
	 }
	 
	 if( total_small[1] == 3 ){
		 //0
		 lv_obj_add_flag(ui_Image24, LV_OBJ_FLAG_HIDDEN);
	     lv_obj_add_flag(ui_Image25, LV_OBJ_FLAG_HIDDEN);
		 lv_obj_add_flag(ui_Image26, LV_OBJ_FLAG_HIDDEN);
		 //1
		 lv_obj_add_flag(ui_Image35, LV_OBJ_FLAG_HIDDEN);
		 lv_obj_add_flag(ui_Image36, LV_OBJ_FLAG_HIDDEN);
		 lv_obj_add_flag(ui_Image37, LV_OBJ_FLAG_HIDDEN);
		 //2
		 lv_obj_add_flag(ui_Image47, LV_OBJ_FLAG_HIDDEN);
		 lv_obj_add_flag(ui_Image48, LV_OBJ_FLAG_HIDDEN);
		 lv_obj_add_flag(ui_Image49, LV_OBJ_FLAG_HIDDEN);
		 //3
		 lv_obj_add_flag(ui_Image59, LV_OBJ_FLAG_HIDDEN);
		 lv_obj_add_flag(ui_Image60, LV_OBJ_FLAG_HIDDEN);
		 lv_obj_add_flag(ui_Image61, LV_OBJ_FLAG_HIDDEN);
		 //4
		 lv_obj_add_flag(ui_Image71, LV_OBJ_FLAG_HIDDEN);
		 lv_obj_add_flag(ui_Image72, LV_OBJ_FLAG_HIDDEN);
		 lv_obj_add_flag(ui_Image73, LV_OBJ_FLAG_HIDDEN);
		 //5
		 lv_obj_add_flag(ui_Image83, LV_OBJ_FLAG_HIDDEN);
		 lv_obj_add_flag(ui_Image84, LV_OBJ_FLAG_HIDDEN);
		 lv_obj_add_flag(ui_Image85, LV_OBJ_FLAG_HIDDEN);
		 if( theme[1] == 7 ){
			 lv_obj_add_flag(ui_Image85, LV_OBJ_FLAG_HIDDEN);
		 }
		 else if( theme[1] == 11 ){ //b
			 lv_img_set_src(ui_Image58, &ui_img_b01_20_png);
			 
		 }
		 else if( theme[1] == 13 ){ //d
			 lv_img_set_src(ui_Image45, &ui_img_b01_20_png);
			 lv_img_set_src(ui_Image46, &ui_img_b02_10_png);
			 lv_img_set_src(ui_Image58, &ui_img_b01_20_png);
			 
		 }
		 else if( theme[1] == 19 ){ //13
			 lv_img_set_src(ui_Image70, &ui_img_b01_20_png);
		 }
		 else if( theme[1] == 21 ){ //15
			 lv_img_set_src(ui_Image45, &ui_img_b01_20_png);
			 lv_img_set_src(ui_Image46, &ui_img_b02_10_png);
			 lv_img_set_src(ui_Image70, &ui_img_b01_20_png);
		 }
		 else if( theme[1] == 25 ){ //19
			 lv_img_set_src(ui_Image57, &ui_img_b01_20_png);
			 lv_img_set_src(ui_Image70, &ui_img_b01_20_png);
		 }
		 else if( theme[1] == 26 ){ //1a
		 	 lv_img_set_src(ui_Image12, &ui_img_b01_20_png);
			 lv_img_set_src(ui_Image33, &ui_img_b02_10_png);
			 lv_img_set_src(ui_Image57, &ui_img_b01_20_png);
			 lv_img_set_src(ui_Image70, &ui_img_b01_20_png);
		 }
		 else if( theme[1] == 28 ){ //1c
			 lv_img_set_src(ui_Image44, &ui_img_b01_20_png);
			 lv_img_set_src(ui_Image46, &ui_img_b02_10_png);
			 lv_img_set_src(ui_Image57, &ui_img_b01_20_png);
			 lv_img_set_src(ui_Image70, &ui_img_b01_20_png);
		 }
		 else if( theme[1] == 35 ){ //23
			 lv_img_set_src(ui_Image82, &ui_img_b01_20_png); //6
		 }
		 else if( theme[1] == 37 ){ //25
			 lv_img_set_src(ui_Image45, &ui_img_b01_20_png);
			 lv_img_set_src(ui_Image46, &ui_img_b02_10_png);
			 lv_img_set_src(ui_Image82, &ui_img_b01_20_png); //6
			 
		 }
		 else if( theme[1] == 38 ){  //26
		 	 lv_img_set_src(ui_Image12, &ui_img_b01_20_png);//2
			 lv_img_set_src(ui_Image33, &ui_img_b02_10_png);
		 	 lv_img_set_src(ui_Image45, &ui_img_b01_20_png);//3
			 lv_img_set_src(ui_Image46, &ui_img_b02_10_png);
			 lv_img_set_src(ui_Image82, &ui_img_b01_20_png); //6
	 	 }
	 	 else if( theme[1] == 41 ){ //29
			 lv_img_set_src(ui_Image57, &ui_img_b01_20_png);//4
			 lv_img_set_src(ui_Image82, &ui_img_b01_20_png); //6
			 
		 }
		 else if( theme[1] == 42 ){ //2a
		 	 lv_img_set_src(ui_Image12, &ui_img_b01_20_png);//2
			 lv_img_set_src(ui_Image33, &ui_img_b02_10_png);
			 lv_img_set_src(ui_Image57, &ui_img_b01_20_png);//4
			 lv_img_set_src(ui_Image82, &ui_img_b01_20_png); //6
		 }
		 else if( theme[1] == 44 ){ //2c
			 lv_img_set_src(ui_Image44, &ui_img_b01_20_png);//3
			 lv_img_set_src(ui_Image46, &ui_img_b02_10_png);
			 lv_img_set_src(ui_Image57, &ui_img_b01_20_png);//4
			 lv_img_set_src(ui_Image82, &ui_img_b01_20_png); //6
			 
		 }
		 else if( theme[1] == 49 ){ //31
			 lv_img_set_src(ui_Image69, &ui_img_b01_20_png);//5
			 lv_img_set_src(ui_Image82, &ui_img_b01_20_png);//6
			 
		 }
		 else if( theme[1] == 50 ){ //32
		 	 lv_img_set_src(ui_Image12, &ui_img_b01_20_png);//2
			 lv_img_set_src(ui_Image33, &ui_img_b02_10_png);
			 lv_img_set_src(ui_Image69, &ui_img_b01_20_png);//5
			 lv_img_set_src(ui_Image82, &ui_img_b01_20_png);//6
			 
		 }
		 else if( theme[1] == 52 ){ //34
			 lv_img_set_src(ui_Image44, &ui_img_b01_20_png);//3
			 lv_img_set_src(ui_Image46, &ui_img_b02_10_png);
			 lv_img_set_src(ui_Image69, &ui_img_b01_20_png);//5
			 lv_img_set_src(ui_Image82, &ui_img_b01_20_png);//6
			 
		 }
		 else{ //56  0x38
			 lv_img_set_src(ui_Image56, &ui_img_b01_20_png);//4
			 lv_img_set_src(ui_Image69, &ui_img_b01_20_png);//5
			 lv_img_set_src(ui_Image82, &ui_img_b01_20_png);//6
		 }
	 }

	 if( total_small[1] == 4 ){
	     lv_obj_add_flag(ui_Image25, LV_OBJ_FLAG_HIDDEN);//0
		 lv_obj_add_flag(ui_Image26, LV_OBJ_FLAG_HIDDEN);
		 lv_obj_add_flag(ui_Image36, LV_OBJ_FLAG_HIDDEN);//1
		 lv_obj_add_flag(ui_Image37, LV_OBJ_FLAG_HIDDEN);
		 lv_obj_add_flag(ui_Image48, LV_OBJ_FLAG_HIDDEN);//2
		 lv_obj_add_flag(ui_Image49, LV_OBJ_FLAG_HIDDEN);
		 lv_obj_add_flag(ui_Image60, LV_OBJ_FLAG_HIDDEN);//3
		 lv_obj_add_flag(ui_Image61, LV_OBJ_FLAG_HIDDEN);
		 lv_obj_add_flag(ui_Image72, LV_OBJ_FLAG_HIDDEN);//4
		 lv_obj_add_flag(ui_Image73, LV_OBJ_FLAG_HIDDEN);
		 lv_obj_add_flag(ui_Image84, LV_OBJ_FLAG_HIDDEN);//5
		 lv_obj_add_flag(ui_Image85, LV_OBJ_FLAG_HIDDEN);
		 if( theme[1] == 15 ){ //f
			 lv_obj_add_flag(ui_Image85, LV_OBJ_FLAG_HIDDEN);
		 }
		 else if( theme[1] == 23 ){ //17
			 lv_img_set_src(ui_Image71, &ui_img_b01_20_png);//5
			 lv_img_set_src(ui_Image72, &ui_img_b02_10_png);
			 
		 }
		 else if( theme[1] == 27 ){ //1b
			 lv_img_set_src(ui_Image58, &ui_img_b01_20_png);//4
			 lv_img_set_src(ui_Image59, &ui_img_b02_10_png);
			 lv_img_set_src(ui_Image71, &ui_img_b01_20_png);//5
			 lv_img_set_src(ui_Image72, &ui_img_b02_10_png);
			 
		 }
		 else if( theme[1] == 29 ){ //1d
		  	 lv_img_set_src(ui_Image45, &ui_img_b01_20_png);//3
			 lv_img_set_src(ui_Image46, &ui_img_b02_10_png);
			 lv_img_set_src(ui_Image58, &ui_img_b01_20_png);//4
			 lv_img_set_src(ui_Image59, &ui_img_b02_10_png);
			 lv_img_set_src(ui_Image71, &ui_img_b01_20_png);//5
			 lv_img_set_src(ui_Image72, &ui_img_b02_10_png);
		 }
		 else if( theme[1] == 30 ){ //1e
			 lv_img_set_src(ui_Image12, &ui_img_b01_20_png);//2
			 lv_img_set_src(ui_Image33, &ui_img_b02_10_png);
			 lv_img_set_src(ui_Image45, &ui_img_b01_20_png);//3
			 lv_img_set_src(ui_Image46, &ui_img_b02_10_png);
			 lv_img_set_src(ui_Image58, &ui_img_b01_20_png);//4
			 lv_img_set_src(ui_Image59, &ui_img_b02_10_png);
			 lv_img_set_src(ui_Image71, &ui_img_b01_20_png);//5
			 lv_img_set_src(ui_Image72, &ui_img_b02_10_png);
		 }
		 else if( theme[1] == 39 ){ //27
			 lv_img_set_src(ui_Image83, &ui_img_b01_20_png);//6
		 }
		 else if( theme[1] == 43 ){ //2b
		 	 lv_img_set_src(ui_Image58, &ui_img_b01_20_png);//4
			 lv_img_set_src(ui_Image59, &ui_img_b02_10_png);
			 lv_img_set_src(ui_Image83, &ui_img_b01_20_png);//6
		 }
		 else if( theme[1] == 45 ){ //2d
		  	 lv_img_set_src(ui_Image45, &ui_img_b01_20_png);//3
			 lv_img_set_src(ui_Image46, &ui_img_b02_10_png);
			 lv_img_set_src(ui_Image58, &ui_img_b01_20_png);//4
			 lv_img_set_src(ui_Image59, &ui_img_b02_10_png);
			 lv_img_set_src(ui_Image83, &ui_img_b01_20_png);//6
		 }
		 else if( theme[1] == 46 ){ //2e
			 lv_img_set_src(ui_Image12, &ui_img_b01_20_png);//2
			 lv_img_set_src(ui_Image33, &ui_img_b02_10_png);
			 lv_img_set_src(ui_Image45, &ui_img_b01_20_png);//3
			 lv_img_set_src(ui_Image46, &ui_img_b02_10_png);
			 lv_img_set_src(ui_Image58, &ui_img_b01_20_png);//4
			 lv_img_set_src(ui_Image59, &ui_img_b02_10_png);
			 lv_img_set_src(ui_Image83, &ui_img_b01_20_png);//6
		 }
		 else if( theme[1] == 51 ){ //33
			 lv_img_set_src(ui_Image70, &ui_img_b01_20_png);//5
			 lv_img_set_src(ui_Image72, &ui_img_b02_10_png);
			 lv_img_set_src(ui_Image83, &ui_img_b01_20_png);//6
		 }
		 else if( theme[1] == 53 ){  //35
		 	 lv_img_set_src(ui_Image45, &ui_img_b01_20_png);//3
			 lv_img_set_src(ui_Image46, &ui_img_b02_10_png);
		 	 lv_img_set_src(ui_Image70, &ui_img_b01_20_png);//5
			 lv_img_set_src(ui_Image72, &ui_img_b02_10_png);
			 lv_img_set_src(ui_Image83, &ui_img_b01_20_png);//6
	 	 }
	 	 else if( theme[1] == 54 ){ //36
			 lv_img_set_src(ui_Image12, &ui_img_b01_20_png);//2
			 lv_img_set_src(ui_Image33, &ui_img_b02_10_png);
			 lv_img_set_src(ui_Image45, &ui_img_b01_20_png);//3
			 lv_img_set_src(ui_Image46, &ui_img_b02_10_png);
			 lv_img_set_src(ui_Image70, &ui_img_b01_20_png);//5
			 lv_img_set_src(ui_Image72, &ui_img_b02_10_png);
			 lv_img_set_src(ui_Image83, &ui_img_b01_20_png);//6
			 
		 }
		 else if( theme[1] == 57 ){ //39
			 lv_img_set_src(ui_Image57, &ui_img_b01_20_png);//4
			 lv_img_set_src(ui_Image59, &ui_img_b02_10_png);
			 lv_img_set_src(ui_Image70, &ui_img_b01_20_png);//5
			 lv_img_set_src(ui_Image72, &ui_img_b02_10_png);
			 lv_img_set_src(ui_Image83, &ui_img_b01_20_png);//6
		 }
		 else if( theme[1] == 58 ){ //3a
			 lv_img_set_src(ui_Image12, &ui_img_b01_20_png);//2
			 lv_img_set_src(ui_Image33, &ui_img_b02_10_png);
			 lv_img_set_src(ui_Image57, &ui_img_b01_20_png);//4
			 lv_img_set_src(ui_Image59, &ui_img_b02_10_png);
			 lv_img_set_src(ui_Image70, &ui_img_b01_20_png);//5
			 lv_img_set_src(ui_Image72, &ui_img_b02_10_png);
			 lv_img_set_src(ui_Image83, &ui_img_b01_20_png);//6
			 
		 }
		 else{ //60  0x3c
			 lv_img_set_src(ui_Image44, &ui_img_b01_20_png);//3
			 lv_img_set_src(ui_Image46, &ui_img_b02_10_png);
			 lv_img_set_src(ui_Image57, &ui_img_b01_20_png);//4
			 lv_img_set_src(ui_Image59, &ui_img_b02_10_png);
			 lv_img_set_src(ui_Image70, &ui_img_b01_20_png);//5
			 lv_img_set_src(ui_Image72, &ui_img_b02_10_png);
			 lv_img_set_src(ui_Image83, &ui_img_b01_20_png);//6
		 }
	 }
	 
	 if( total_small[1] == 5 ){
		 lv_obj_add_flag(ui_Image26, LV_OBJ_FLAG_HIDDEN);
		 lv_obj_add_flag(ui_Image37, LV_OBJ_FLAG_HIDDEN);
		 lv_obj_add_flag(ui_Image49, LV_OBJ_FLAG_HIDDEN);
		 lv_obj_add_flag(ui_Image61, LV_OBJ_FLAG_HIDDEN);
		 lv_obj_add_flag(ui_Image73, LV_OBJ_FLAG_HIDDEN);
		 lv_obj_add_flag(ui_Image85, LV_OBJ_FLAG_HIDDEN);
		 if( theme[1] == 31 ){
			 lv_obj_add_flag(ui_Image85, LV_OBJ_FLAG_HIDDEN);
		 }
		 else if( theme[1] == 47 ){
			 lv_img_set_src(ui_Image84, &ui_img_b01_20_png);
			 
		 }
		 else if( theme[1] == 55 ){
			 lv_img_set_src(ui_Image71, &ui_img_b01_20_png);
			 lv_img_set_src(ui_Image72, &ui_img_b02_10_png);
			 lv_img_set_src(ui_Image84, &ui_img_b01_20_png);
		 }
		 else if( theme[1] == 59 ){ //3b
			 lv_img_set_src(ui_Image58, &ui_img_b01_20_png);
			 lv_img_set_src(ui_Image59, &ui_img_b02_10_png);
			 lv_img_set_src(ui_Image71, &ui_img_b01_20_png);
			 lv_img_set_src(ui_Image72, &ui_img_b02_10_png);
			 lv_img_set_src(ui_Image84, &ui_img_b01_20_png);
			 
		 }
		 else if( theme[1] == 61 ){  //3d
		 	 lv_img_set_src(ui_Image45, &ui_img_b01_20_png);
			 lv_img_set_src(ui_Image46, &ui_img_b02_10_png);
			 lv_img_set_src(ui_Image58, &ui_img_b01_20_png);
			 lv_img_set_src(ui_Image59, &ui_img_b02_10_png);
			 lv_img_set_src(ui_Image71, &ui_img_b01_20_png);
			 lv_img_set_src(ui_Image72, &ui_img_b02_10_png);
			 lv_img_set_src(ui_Image84, &ui_img_b01_20_png);
	 	 } 
		 else{ //62 3e
			 lv_img_set_src(ui_Image12, &ui_img_b01_20_png);
			 lv_img_set_src(ui_Image33, &ui_img_b02_10_png);
			 lv_img_set_src(ui_Image45, &ui_img_b01_20_png);
			 lv_img_set_src(ui_Image46, &ui_img_b02_10_png);
			 lv_img_set_src(ui_Image58, &ui_img_b01_20_png);
			 lv_img_set_src(ui_Image59, &ui_img_b02_10_png);
			 lv_img_set_src(ui_Image71, &ui_img_b01_20_png);
			 lv_img_set_src(ui_Image72, &ui_img_b02_10_png);
			 lv_img_set_src(ui_Image84, &ui_img_b01_20_png);
			 lv_img_set_src(ui_Image85, &ui_img_b02_10_png);
		 }
	 }
	 bsp_display_unlock();
}

const uart_port_t uart_troy = UART_NUM_1;
uart_config_t uart_config = {
    .baud_rate = 115200,
    .data_bits = UART_DATA_8_BITS,
    .parity = UART_PARITY_DISABLE,
    .stop_bits = UART_STOP_BITS_1,
    .flow_ctrl = UART_HW_FLOWCTRL_CTS_RTS,
    .rx_flow_ctrl_thresh = 122,
};


static void show_boot_time(void *arg)
{
    static uint64_t boot_time_us = 0;
    if (boot_time_us == 0) {
        boot_time_us = esp_timer_get_time();
    }

    uint64_t elapsed_time_us = esp_timer_get_time() - boot_time_us;
    uint32_t elapsed_time_s = elapsed_time_us / 1000000;

    uint32_t hours = elapsed_time_s / 3600;
    uint32_t minutes = (elapsed_time_s % 3600) / 60;
    uint32_t seconds = elapsed_time_s % 60;
//
//	ESP_LOGI(TAG, "----------------------------------------------\n");
    ESP_LOGI(TAG, "  Boot-up time: %d:%02d:%02d  \n", hours, minutes, seconds);
    //ESP_LOGI(TAG, "----------------------------------------------\n");
}

// Create a periodic timer to trigger every 5 seconds
const esp_timer_create_args_t timer_args = {
        .callback = show_boot_time,
        .arg = NULL,
        .name = "boot_time_timer"
};
esp_timer_handle_t timer_handle;

//void app_main(void)
extern "C" void app_main()
{
	ESP_ERROR_CHECK(bsp_i2c_init());
	
	vTaskDelay(10);
		//io expander
	esp_io_expander_new_i2c_tca95xx_16bit(I2C_NUM_1, ESP_IO_EXPANDER_I2C_TCA9555_ADDRESS_000, &io_expander);
	esp_io_expander_set_dir(io_expander, IO_EXPANDER_PIN_NUM_0 | IO_EXPANDER_PIN_NUM_1, IO_EXPANDER_INPUT);
	esp_io_expander_set_dir(io_expander, IO_EXPANDER_PIN_NUM_2 | IO_EXPANDER_PIN_NUM_3, IO_EXPANDER_INPUT);
	esp_io_expander_set_dir(io_expander, IO_EXPANDER_PIN_NUM_4 | IO_EXPANDER_PIN_NUM_5 | IO_EXPANDER_PIN_NUM_6 | IO_EXPANDER_PIN_NUM_7, IO_EXPANDER_OUTPUT);
	esp_io_expander_set_dir(io_expander, IO_EXPANDER_PIN_NUM_8 | IO_EXPANDER_PIN_NUM_9, IO_EXPANDER_OUTPUT);
	esp_io_expander_set_dir(io_expander, IO_EXPANDER_PIN_NUM_10 | IO_EXPANDER_PIN_NUM_11, IO_EXPANDER_OUTPUT);
	esp_io_expander_set_dir(io_expander, IO_EXPANDER_PIN_NUM_12 | IO_EXPANDER_PIN_NUM_13, IO_EXPANDER_OUTPUT);
	esp_io_expander_set_dir(io_expander, IO_EXPANDER_PIN_NUM_14 | IO_EXPANDER_PIN_NUM_15, IO_EXPANDER_OUTPUT);
	esp_io_expander_set_level(io_expander, IO_EXPANDER_PIN_NUM_12 | IO_EXPANDER_PIN_NUM_13, 1);
	esp_io_expander_set_level(io_expander, IO_EXPANDER_PIN_NUM_8 | IO_EXPANDER_PIN_NUM_9 | IO_EXPANDER_PIN_NUM_10 | IO_EXPANDER_PIN_NUM_11, 0);

	// Initialize camera power manager after IO expander is ready
	ESP_LOGI(TAG, "Initializing camera power manager...");
	camera_power_config_t camera_power_config = {
		.io_expander = io_expander,
		.auto_power_management = true,
		.power_on_delay_ms = 10,  // Optimized for fastest startup
		.reset_delay_ms = 10,     // Optimized for fastest startup
		.power_off_delay_ms = 5   // Optimized for fastest startup
	};
	ESP_ERROR_CHECK(camera_power_manager_init(&camera_power_config));

	//esp_io_expander_print_state(io_expander);

	// Initialize stability systems first
	ESP_LOGI(TAG, "Initializing stability systems...");

	// Initialize memory manager
	mem_config_t mem_config = {
		.enable_tracking = true,
		.enable_leak_detection = true,
		.enable_corruption_check = true,
		.max_tracked_allocations = 100
	};
	ESP_ERROR_CHECK(mem_manager_init(&mem_config));

	// Initialize CPU monitor
	cpu_monitor_config_t cpu_config = {
		.monitor_interval_ms = 1000,        // Report every second
		.cpu_threshold_percent = 90,
		.blocking_threshold_ms = 500,       // 500ms blocking threshold
		.enable_task_runtime_stats = true,
		.enable_blocking_detection = true,
		.enable_lvgl_monitoring = true
	};
	ESP_ERROR_CHECK(cpu_monitor_init(&cpu_config));

	// Initialize LVGL monitor
	ESP_ERROR_CHECK(lvgl_monitor_init());

	// Initialize screen change monitor
	ESP_ERROR_CHECK(screen_monitor_init());

	// Initialize error handler
	error_handler_config_t error_config = {
		.enable_logging = true,
		.enable_recovery = true,
		.enable_statistics = true,
		.max_error_records = 50,
		.max_retry_attempts = 3,
		.retry_delay_ms = 1000
	};
	ESP_ERROR_CHECK(error_handler_init(&error_config));

	// Initialize system monitor with more conservative settings
	monitor_config_t monitor_config = {
		.enable_task_monitoring = true,
		.enable_memory_monitoring = true,
		.enable_cpu_monitoring = false,  // Disable CPU monitoring for now
		.enable_temperature_monitoring = false,
		.enable_auto_recovery = false,  // Disable auto recovery initially
		.monitor_interval_ms = 10000,   // Increase interval to 10 seconds
		.max_monitored_tasks = 10       // Reduce max tasks
	};
	ESP_ERROR_CHECK(system_monitor_init(&monitor_config));

	// Enable auto recovery after system is fully initialized
	ESP_LOGI(TAG, "System monitor initialized, auto recovery will be enabled after full startup");

	// Start CPU monitoring
	ESP_ERROR_CHECK(cpu_monitor_start());

	ESP_LOGI(TAG, "Start NVS .........................................");
	esp_err_t err = nvs_flash_init();
	if (err == ESP_ERR_NVS_NO_FREE_PAGES || err == ESP_ERR_NVS_NEW_VERSION_FOUND) {
		// NVS partition was truncated and needs to be erased
		// Retry nvs_flash_init
		ERROR_CHECK_REPORT(nvs_flash_erase(), ERROR_SEVERITY_WARNING, ERROR_CATEGORY_FILESYSTEM, "NVS flash erase");
		err = nvs_flash_init();
	}
	ERROR_CHECK_REPORT(err, ERROR_SEVERITY_CRITICAL, ERROR_CATEGORY_FILESYSTEM, "NVS flash init");

	if(0){
		// Configure UART parameters
		ESP_ERROR_CHECK(uart_param_config(uart_troy, &uart_config));
		// Set UART pins(TX: IO4, RX: IO5, RTS: IO18, CTS: IO19)
		ESP_ERROR_CHECK(uart_set_pin(uart_troy, BSP_MB_UART_RXD, BSP_MB_UART_TXD, -1, -1));  //TX, RX, RTS, and CTS
		// Setup UART buffered IO with event queue
		const int uart_buffer_size = (1024 * 2);
		QueueHandle_t uart_queue;
		// Install UART driver using an event queue here
		ESP_ERROR_CHECK(uart_driver_install(uart_troy, uart_buffer_size, uart_buffer_size, 10, &uart_queue, 0));

		ESP_LOGI(TAG, "Writing to UART 1\n");
		uint8_t data[6];
		data[0] = 0x11;
		data[1] = 0x22;
		data[2] = 0x33;
		data[3] = 0x33;
		data[4] = 0x33;
		data[5] = 0x33;
		uart_write_bytes(uart_troy, (const char*)data, 6);

		// Read data from UART.
		ESP_LOGI(TAG, "Reading from UART 1\n");

		int length = 0;
		ESP_ERROR_CHECK(uart_get_buffered_data_len(uart_troy, (size_t*)&length));
		length = uart_read_bytes(uart_troy, data, length, 100);
		ESP_LOGI(TAG, "Reading from UART =%s\n",data);
		ESP_ERROR_CHECK(uart_driver_delete(uart_troy));
	}
	else
		vTaskDelay(10);

	nvs_open("storage", NVS_READWRITE, &nvs_handle_modbus);

	//err = nvs_set_i32(my_handle, "board_init", board_init);
	//ESP_LOGI(TAG, (err != ESP_OK) ? "Failed!\n" : "Done\n");
	//ESP_LOGI(TAG, "Committing board_init in NVS ... ");
	//err = nvs_commit(my_handle);

	// Read
	ESP_LOGI(TAG, "Reading board_init from NVS ... ");
	err = nvs_get_i32(nvs_handle_modbus, "board_init", &board_init);
	switch (err) {
	case ESP_OK:
		ESP_LOGI(TAG, "Done\n");
		ESP_LOGI(TAG, "board_init = %d\n", board_init);
		break;
	case ESP_ERR_NVS_NOT_FOUND:
		ESP_LOGI(TAG, "board_init is not initialized yet!\n");
		board_init = 0;
		break;
	default :
		ESP_LOGI(TAG, "Error (%s) reading!\n", esp_err_to_name(err));
	}

	err = nvs_get_i32(nvs_handle_modbus, "default_pw", &int_pw);
	switch (err) {
	case ESP_OK:
		ESP_LOGI(TAG, "int_pw = %d\n", int_pw);
		sprintf(default_pw, "%06d", int_pw);
		ESP_LOGI(TAG, "default_pw = %s\n", default_pw);
		break;
	case ESP_ERR_NVS_NOT_FOUND:
		ESP_LOGI(TAG, "default_pw is not initialized yet!\n");
		sprintf(default_pw, "%d", 123456);
		ESP_LOGI(TAG, "default_pw = %s\n", default_pw);
		break;
	default :
		ESP_LOGI(TAG, "Error (%s) reading!\n", esp_err_to_name(err));
	}

	nvs_get_i32(nvs_handle_modbus, "log_index", &log_index);
	nvs_get_i32(nvs_handle_modbus, "log_full", &log_full);
	nvs_get_i32(nvs_handle_modbus, "colli_index", &colli_index);

	///////////////////////////////////////////////////////////////////////////////////////////////////
	err = nvs_get_i32(nvs_handle_modbus, "modbus_mode", &modbus_mode);
	if( err != ESP_OK ){
		ESP_LOGI(TAG, "modbus_mode not initialized .........");
		modbus_mode=1; //set default mode   master 0 slave 1
	}
	else{
		modbus_mode_nvs=modbus_mode;
		ESP_LOGI(TAG, "Committing modbus_mode in NVS ... modbus_mode=%d",modbus_mode);
		nvs_set_i32(nvs_handle_modbus, "modbus_mode", modbus_mode);
		vTaskDelay(10);
		nvs_commit(nvs_handle_modbus);
		vTaskDelay(10);
	}

	//if( modbus_mode ){
		ESP_LOGI(TAG, "Start Modbus Slave.");
		modbus_mode_nvs=1;
		mobus_slave_stop = true;
		modbus_master_run=0;
		vTaskDelay(100);
		mobus_slave_stop = false;
		xTaskCreatePinnedToCore((TaskFunction_t)mb_slave, "mb_slave", 4096, NULL, 2, NULL, 0);
		//xHandle_MB = xTaskCreateStaticPinnedToCore((TaskFunction_t)mb_slave, "mb_slave", 4096, NULL, 2,xStackMB, &TaskTCB_MB, 1);
		vTaskDelay(100);
		//ESP_LOGI(TAG, "Modbus Slave ended.");
	//}
	
	default_screen = holding_reg_params_slave.holding_data10;

	ESP_LOGI(TAG, "Start Dispaly .........................................");
	/* Initialize display and LVGL */
	lvgl_disp = bsp_display_start();
	bsp_display_rotate( lvgl_disp, LV_DISP_ROT_90);
	//Add and show objects on display
	app_lvgl_display();
	
	update_background();
	// Turn on display backlight
	bsp_display_backlight_on();

	ESP_ERROR_CHECK(bsp_touch_init(&touch_handle));
	/* Add touch input (for selected screen) */
	const lvgl_port_touch_cfg_t touch_cfg = {
			.disp = lvgl_disp,
			.handle = touch_handle,
	};
	lvgl_touch_indev = lvgl_port_add_touch(&touch_cfg);
	/////////////////////////////////////////////////////////////////////////////////////////

	GetInitializedNVS();

	// Create ScreenChangeEvent task with PSRAM stack allocation
	BaseType_t ret = xTaskCreatePinnedToCore(ScreenChangeEvent, "ScreenChangeEvt", 1024 * 8, NULL, 8, NULL, 0);
	if (ret != pdPASS) {
		ESP_LOGE(TAG, "Failed to create ScreenChangeEvent task");
	}

	UpdateUserTable();

	/////////////////////////////////////////////////////////////////////////////////////////
		//logo page

	ESP_LOGI(TAG, "............................................firmware id=%d",holding_reg_params_slave.holding_data1);

	while(!bsp_display_lock(1000)){bsp_display_unlock();}
	lv_label_set_text_fmt(ui_Lab_ID, "%03d",holding_reg_params_slave.holding_data23);  //station id
	lv_label_set_text_fmt(ui_Lab_Ver, "%05d",holding_reg_params_slave.holding_data1); //Version
	//lv_img_set_src(ui_Image2, &ui_img_logo_png);

	//lv_label_set_text_fmt(ui_Lab_Ver, "%05d Demo",holding_reg_params_slave.holding_data1); //Version............
	//lv_img_set_src(ui_Image2, &ui_img_fusiontech_png);
	lv_obj_add_state(ui_BTN_Set_Time, LV_STATE_DISABLED);
	lv_obj_add_state(ui_BTN_Set_ModBusID, LV_STATE_DISABLED);
	lv_obj_add_state(ui_BTN_Set_ModBus_Mode, LV_STATE_DISABLED);
	lv_obj_add_state(ui_BTN_Set_PWD, LV_STATE_DISABLED);
	
	bsp_display_unlock();
	/////////////////////////////////////////////////////////////////////////////////////////////////////////////////

	AppCamera *camera = new AppCamera(PIXFORMAT_RGB565, FRAMESIZE_240X240, 2, xQueueFrame_0);
	AppFace *face = new AppFace(xQueueFrame_0);
	camera_p = camera;
	face_p = face;
	face_p->state = FACE_IDLE;
	// Camera will be started automatically when needed by camera_power_manager
	// camera_p->run();
	// vTaskDelay(10);
	// face_p->run();

	///////////////////////////////////      gpio interrupt
	ESP_LOGI(TAG, "initialize gpio interrupt.");
	//zero-initialize the config structure.
	gpio_config_t io_conf = {};

	//interrupt of rising edge
	io_conf.intr_type = GPIO_INTR_NEGEDGE;  // GPIO_INTR_ANYEDGE GPIO_INTR_POSEDGE GPIO_INTR_NEGEDGE
	//bit mask of the pins, use GPIO? here
	io_conf.pin_bit_mask = GPIO_INPUT_PIN_SEL;
	//set as input mode
	io_conf.mode = GPIO_MODE_INPUT;
	//enable pull-up mode
	io_conf.pull_up_en = (gpio_pullup_t) 1;
	gpio_config(&io_conf);
	//change gpio interrupt type for one pin
	//gpio_set_intr_type(GPIO_INPUT_IO_0, GPIO_INTR_ANYEDGE);
	//create a queue to handle gpio event from isr
	gpio_evt_queue = xQueueCreate(10, sizeof(uint32_t));
	//start gpio task
	//xTaskCreate(gpio_task, "gpio_task", 2048, NULL, 10, NULL);
	xTaskCreatePinnedToCore((TaskFunction_t)gpio_task, "gpio_task", 2048, NULL, 10, NULL,0);
	//install gpio isr service
	gpio_install_isr_service(ESP_INTR_FLAG_DEFAULT);
	//hook isr handler for specific gpio pin
	gpio_isr_handler_add(GPIO_INPUT_IO_0, gpio_isr_handler, (void*) GPIO_INPUT_IO_0);
	
	//gpio_isr_handler_add(GPIO_INPUT_IO_POWER, gpio_isr_handler2, (void*) GPIO_INPUT_IO_POWER);

	//////// RTC  /////////////////////////////////////////////////////////////////////////////////////////////
	//xTaskCreate(pcf8563, "pcf8563", configMINIMAL_STACK_SIZE*4, NULL, 5, NULL);  //configMAX_PRIORITIES – 1
	xHandle_RTC = xTaskCreateStaticPinnedToCore((TaskFunction_t)pcf8563, "pcf8563", 4096, NULL, 5,xStackRTC, &TaskTCB_RTC, 1);  // Increased stack size to 4096

	///////////ReadADC();/////////////////////////////////////////////
	//xTaskCreatePinnedToCore((TaskFunction_t)ReadADC, "ReadADC", 3072, NULL, 3, NULL, 0);
	xHandle_ADC = xTaskCreateStaticPinnedToCore((TaskFunction_t)ReadADC, "ReadADC", 4096, NULL, 3,xStackADC, &TaskTCB_ADC, 1);  // Increased stack size to 4096



	////////////////////////////////////////////////////////////////////////////////////////////
	ESP_LOGI(TAG, "System setup using Modbus slave parameters..........");

	exp_time.tm_year = holding_reg_params_slave.holding_data2 -1900;
	exp_time.tm_mon = holding_reg_params_slave.holding_data4 -1;
	exp_time.tm_mday = holding_reg_params_slave.holding_data5;
	sprintf(expire_date,"%04d/%02d/%02d", exp_time.tm_year + 1900, exp_time.tm_mon+1,exp_time.tm_mday);
	
	


	//①	9600 bps
	//②	19200 bps
	//③	38400 bps
	//④	57600 bps
	//⑤	115200 bps


	if(holding_reg_params_slave.holding_data24 == 5){
		baudrate=115200;
	}
	else if(holding_reg_params_slave.holding_data24 == 4){
		baudrate=57600;
	}
	else if(holding_reg_params_slave.holding_data24 == 3){
		baudrate=38400;
	}
	else if(holding_reg_params_slave.holding_data24 == 2){
		baudrate=19200;
	}
	else if(holding_reg_params_slave.holding_data24 == 1){
		baudrate=9600;
	}

	mb_slave_id = holding_reg_params_slave.holding_data23;
	permission_output = holding_reg_params_slave.holding_data15;
	
	ESP_LOGI(TAG, "Modbus slave baudrate = %d..........", baudrate);

	bsp_display_brightness_set(holding_reg_params_slave.holding_data21); //set brightness



	//perfmon_start();

	//ESP_LOGI(TAG, "Close nvs handle.");
	//nvs_close(nvs_handle_modbus); // Close



	

	theme_decode();

	vTaskDelay(110);
	face_p->stop();
	vTaskDelay(50);
	camera_p->stop();
	vTaskDelay(50);
}

// C wrapper function implementation
extern "C" bool is_camera_object_available(void) {
	return (camera_p != nullptr);
}

extern "C" bool initialize_camera_hardware(void) {
	if (camera_p) {
		esp_err_t err = camera_p->init_camera();
		if (err == ESP_OK) {
			ESP_LOGI(TAG, "Camera hardware initialized successfully during boot");
			return true;
		} else {
			ESP_LOGE(TAG, "Failed to initialize camera hardware during boot");
			return false;
		}
	}
	ESP_LOGE(TAG, "Camera object not available for initialization");
	return false;
	


	//if(1 ){
	if( !modbus_mode ){
		ESP_LOGW(TAG, "Switching to Modbus Master......");
		modbus_mode=0;
		mobus_slave_stop = true;
		modbus_master_run=1;
		destory_modbus_slave();

		vTaskDelay(500);
		setup_reg_data();
		mb_master();
		xTaskCreatePinnedToCore((TaskFunction_t)master_operation_func, "master_operation_func", 4096, NULL, 3, NULL, 1);

	}

	ESP_LOGI(TAG, "Ameter initialization done.");

	
	nvs_get_u8(nvs_handle_modbus, "abnormal_power", &abnormal_power);
	if( abnormal_power==1 ){
		ESP_LOGW(TAG, "abnormal_power occurred last time....");
		abnormal_power=0;
		nvs_set_u8(nvs_handle_modbus, "abnormal_power", 0);
	} 
	
	
	while(!bsp_display_lock(1000)){bsp_display_unlock();}
	if(holding_reg_params_slave.holding_data24 == 5){
		lv_label_set_text(ui_Label_baud, "115200");
	}
	else if(holding_reg_params_slave.holding_data24 == 4){
		lv_label_set_text(ui_Label_baud, "57600");
	}
	else if(holding_reg_params_slave.holding_data24 == 3){
		lv_label_set_text(ui_Label_baud, "38400");
	}
	else if(holding_reg_params_slave.holding_data24 == 2){
		lv_label_set_text(ui_Label_baud, "19200");
	}
	else if(holding_reg_params_slave.holding_data24 == 1){
		lv_label_set_text(ui_Label_baud, "9600");
	}		
	if( modbus_mode ){		
		lv_obj_clear_state(ui_BTN_ModBus_Master, LV_STATE_DISABLED);
		lv_obj_add_state(ui_BTN_ModBus_Slave, LV_STATE_DISABLED);
		lv_obj_clear_state(ui_BTN_Set_ModBusID, LV_STATE_DISABLED);
    }
    else{
		lv_obj_add_state(ui_BTN_ModBus_Master, LV_STATE_DISABLED);
		lv_obj_clear_state(ui_BTN_ModBus_Slave, LV_STATE_DISABLED);
		lv_obj_add_state(ui_BTN_Set_ModBusID, LV_STATE_DISABLED);
    }
    
    
	lv_obj_add_state(ui_BTN_Set_Time, LV_STATE_DISABLED);
	lv_obj_add_state(ui_BTN_Set_ModBusID, LV_STATE_DISABLED);
	lv_obj_add_state(ui_BTN_Set_ModBus_Mode, LV_STATE_DISABLED);
	lv_obj_add_state(ui_BTN_Set_PWD, LV_STATE_DISABLED);
    
	bsp_display_unlock();
	
	
	
	
	//xHandle_log = xTaskCreateStaticPinnedToCore((TaskFunction_t)log_mem_info, "log_mem_info", 3072, NULL, 3,xStacklog, &TaskTCB_log, 1);
	
    // Initialize the high-resolution timer
    //esp_timer_init();

    
    esp_timer_create(&timer_args, &timer_handle);

    // Start the timer
    esp_timer_start_periodic(timer_handle, 5000000); // 5 seconds
    
    
	//ESP_LOGW(TAG, "Starting Monkey test...............................");
	//lv_monkey_test();
	//ESP_LOGW(TAG, "Monkey test has started......................................");
	
	
    esp_io_expander_set_level(io_expander, (1ULL << 11), 0); // led off
	esp_io_expander_set_level(io_expander, (1ULL << 10), 0); // led off
	esp_io_expander_set_level(io_expander, (1ULL << 9), 0); // led off
	esp_io_expander_set_level(io_expander, (1ULL << 8), 0); // led off
	
	esp_io_expander_set_level(io_expander, (1ULL << 7), 0); // led off
	esp_io_expander_set_level(io_expander, (1ULL << 6), 0); // led off
	esp_io_expander_set_level(io_expander, (1ULL << 5), 0); // led off
	esp_io_expander_set_level(io_expander, (1ULL << 4), 0); // led off
	
	vTaskDelay(10);

	// Enable auto recovery now that system is fully initialized
	ESP_LOGI(TAG, "System fully initialized, enabling auto recovery");
	system_monitor_set_auto_recovery(true);

	// Print initial system report
	vTaskDelay(1000);  // Wait a bit for system to settle
	system_monitor_print_report();

	// Finalize camera power management
	camera_power_finalize_init();
	camera_power_print_status();

	ESP_LOGW(TAG, "Exit app_main.............................................................");
}
// *INDENT-ON*
