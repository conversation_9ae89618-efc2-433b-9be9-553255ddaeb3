#pragma once

// LVGL Memory Configuration for Screen Changes - PSRAM MAXIMIZED
// These settings use maximum PSRAM to ensure no memory shortage issues

// ===== LVGL INTERNAL MEMORY POOL - MAXIMIZED =====
#ifndef LV_MEM_SIZE
#define LV_MEM_SIZE (1024U * 1024U)  // 1MB LVGL internal memory pool (was 48KB)
#endif

// ===== LAYER BUFFERS - MAXIMIZED FOR COMPLEX SCREEN TRANSITIONS =====
#ifndef LV_LAYER_SIMPLE_BUF_SIZE
#define LV_LAYER_SIMPLE_BUF_SIZE (64 * 1024)   // 64KB layer buffer (was 24KB)
#endif

#ifndef LV_LAYER_SIMPLE_FALLBACK_BUF_SIZE
#define LV_LAYER_SIMPLE_FALLBACK_BUF_SIZE (32 * 1024)  // 32KB fallback buffer (was 3KB)
#endif

// ===== MEMORY BUFFER COUNT - MAXIMIZED =====
#ifndef LV_MEM_BUF_MAX_NUM
#define LV_MEM_BUF_MAX_NUM 64  // 64 memory buffers (was 16)
#endif

// ===== IMAGE CACHE - MAXIMIZED =====
#ifndef LV_IMG_CACHE_DEF_SIZE
#define LV_IMG_CACHE_DEF_SIZE 32  // 32 cached images (was 0)
#endif

// ===== DRAW BUFFER - MAXIMIZED =====
#ifndef LV_DISP_DEF_REFR_PERIOD
#define LV_DISP_DEF_REFR_PERIOD 16  // 60 FPS for smooth transitions
#endif

// Enable custom memory allocation for better control
#ifndef LV_MEM_CUSTOM
#define LV_MEM_CUSTOM 1
#endif

#if LV_MEM_CUSTOM
#include "esp_heap_caps.h"

// ===== PSRAM-FIRST ALLOCATORS - MAXIMIZED =====
// Custom allocators that prioritize PSRAM for ALL allocations
static inline void* lvgl_malloc(size_t size) {
    // Always try PSRAM first (even for small allocations)
    void* ptr = heap_caps_malloc(size, MALLOC_CAP_SPIRAM);
    if (ptr) {
        // ESP_LOGI("LVGL_MEM", "PSRAM alloc: %u bytes at %p", size, ptr);
        return ptr;
    }

    // Only use internal RAM if PSRAM is full
    ptr = heap_caps_malloc(size, MALLOC_CAP_INTERNAL);
    // Note: ESP_LOG functions not available in header static inline functions
    return ptr;
}

static inline void lvgl_free(void* ptr) {
    if (ptr) {
        heap_caps_free(ptr);
        // ESP_LOGD("LVGL_MEM", "Freed: %p", ptr);
    }
}

static inline void* lvgl_realloc(void* ptr, size_t size) {
    // Always prefer PSRAM for realloc
    void* new_ptr = heap_caps_realloc(ptr, size, MALLOC_CAP_SPIRAM);
    if (!new_ptr && size > 0) {
        // Fallback to internal RAM
        new_ptr = heap_caps_realloc(ptr, size, MALLOC_CAP_INTERNAL);
    }
    return new_ptr;
}

#define LV_MEM_CUSTOM_ALLOC   lvgl_malloc
#define LV_MEM_CUSTOM_FREE    lvgl_free
#define LV_MEM_CUSTOM_REALLOC lvgl_realloc
#endif

// Screen transition optimization (already defined above)

// Animation settings for stable screen changes
#ifndef LV_USE_ANIMATION
#define LV_USE_ANIMATION 1
#endif

#ifndef LV_ANIM_DEF_TIME
#define LV_ANIM_DEF_TIME 200  // Default animation time
#endif

// ===== ADDITIONAL PSRAM OPTIMIZATIONS =====

// Reduce memory fragmentation
#ifndef LV_MEM_POOL_EXPAND_SIZE_PERCENT
#define LV_MEM_POOL_EXPAND_SIZE_PERCENT 50  // Increase expansion for PSRAM
#endif

// ===== FONT AND TEXT RENDERING - MAXIMIZED =====
#ifndef LV_FONT_MONTSERRAT_48
#define LV_FONT_MONTSERRAT_48 1  // Enable large fonts
#endif

#ifndef LV_TXT_LINE_BREAK_LONG_LEN
#define LV_TXT_LINE_BREAK_LONG_LEN 256  // Increase text line buffer
#endif

// ===== ANIMATION SETTINGS - MAXIMIZED =====
#ifndef LV_ANIM_DEF_TIME
#define LV_ANIM_DEF_TIME 300  // Longer animations for smoother transitions
#endif

#ifndef LV_USE_ANIMATION
#define LV_USE_ANIMATION 1
#endif

// ===== GRADIENT SETTINGS - MAXIMIZED =====
#ifndef LV_GRADIENT_MAX_STOPS
#define LV_GRADIENT_MAX_STOPS 8  // More gradient stops (was 2)
#endif

// ===== STYLE SETTINGS - MAXIMIZED =====
#ifndef LV_STYLE_PROP_CNT
#define LV_STYLE_PROP_CNT 128  // More style properties
#endif

// ===== OBJECT SETTINGS - MAXIMIZED =====
#ifndef LV_OBJ_STYLE_CACHE_SIZE
#define LV_OBJ_STYLE_CACHE_SIZE 64  // Larger style cache
#endif

// ===== THEME SETTINGS - MAXIMIZED =====
#ifndef LV_THEME_DEFAULT_DARK
#define LV_THEME_DEFAULT_DARK 1  // Enable dark theme
#endif

#ifndef LV_THEME_DEFAULT_GROW
#define LV_THEME_DEFAULT_GROW 1  // Enable grow animations
#endif

// ===== WIDGET SPECIFIC MAXIMIZATIONS =====
#ifndef LV_TEXTAREA_DEF_CURSOR_BLINK_TIME
#define LV_TEXTAREA_DEF_CURSOR_BLINK_TIME 400
#endif

#ifndef LV_CHART_POINT_CNT_DEF
#define LV_CHART_POINT_CNT_DEF 256  // More chart points
#endif

// ===== MEMORY DEBUGGING =====
#ifndef LV_USE_MEM_MONITOR
#define LV_USE_MEM_MONITOR 1  // Enable memory monitoring
#endif

#ifndef LV_MEM_ADD_JUNK
#define LV_MEM_ADD_JUNK 0  // Disable for performance
#endif
