#include "memory_manager.h"
#include <string.h>
#include <time.h>

static const char* TAG = "MEM_MGR";

// Global memory manager state
static struct {
    mem_config_t config;
    mem_stats_t stats;
    mem_alloc_info_t* allocations;
    SemaphoreHandle_t mutex;
    bool initialized;
} mem_mgr = {0};

// Memory corruption detection pattern
#define MEM_GUARD_PATTERN 0xDEADBEEF
#define MEM_GUARD_SIZE 4

esp_err_t mem_manager_init(const mem_config_t* config) {
    if (mem_mgr.initialized) {
        ESP_LOGW(TAG, "Memory manager already initialized");
        return ESP_OK;
    }

    if (!config) {
        ESP_LOGE(TAG, "Invalid configuration");
        return ESP_ERR_INVALID_ARG;
    }

    // Copy configuration
    memcpy(&mem_mgr.config, config, sizeof(mem_config_t));

    // Initialize mutex
    mem_mgr.mutex = xSemaphoreCreateMutex();
    if (!mem_mgr.mutex) {
        ESP_LOGE(TAG, "Failed to create mutex");
        return ESP_ERR_NO_MEM;
    }

    // Allocate tracking array in PSRAM if enabled to save internal RAM
    if (config->enable_tracking && config->max_tracked_allocations > 0) {
        size_t alloc_size = config->max_tracked_allocations * sizeof(mem_alloc_info_t);
        // Try PSRAM first, fallback to internal RAM if needed
        mem_mgr.allocations = heap_caps_malloc(alloc_size, MALLOC_CAP_SPIRAM);
        if (!mem_mgr.allocations) {
            ESP_LOGW(TAG, "PSRAM allocation failed, trying internal RAM");
            mem_mgr.allocations = heap_caps_malloc(alloc_size, MALLOC_CAP_INTERNAL);
        }
        if (!mem_mgr.allocations) {
            ESP_LOGE(TAG, "Failed to allocate tracking array");
            vSemaphoreDelete(mem_mgr.mutex);
            return ESP_ERR_NO_MEM;
        }
        memset(mem_mgr.allocations, 0, alloc_size);
        ESP_LOGI(TAG, "Memory tracking array allocated in %s",
                 heap_caps_get_allocated_size(mem_mgr.allocations) ? "PSRAM" : "Internal RAM");
    }

    // Initialize statistics
    memset(&mem_mgr.stats, 0, sizeof(mem_stats_t));

    mem_mgr.initialized = true;
    ESP_LOGI(TAG, "Memory manager initialized (tracking: %s, leak detection: %s)",
             config->enable_tracking ? "ON" : "OFF",
             config->enable_leak_detection ? "ON" : "OFF");

    return ESP_OK;
}

static void add_allocation_record(void* ptr, size_t size, const char* file, int line) {
    if (!mem_mgr.config.enable_tracking || !mem_mgr.allocations) {
        return;
    }

    // Find empty slot
    for (uint32_t i = 0; i < mem_mgr.config.max_tracked_allocations; i++) {
        if (mem_mgr.allocations[i].ptr == NULL) {
            mem_mgr.allocations[i].ptr = ptr;
            mem_mgr.allocations[i].size = size;
            mem_mgr.allocations[i].file = file;
            mem_mgr.allocations[i].line = line;
            mem_mgr.allocations[i].timestamp = xTaskGetTickCount();
            return;
        }
    }

    ESP_LOGW(TAG, "Allocation tracking table full");
}

static void remove_allocation_record(void* ptr) {
    if (!mem_mgr.config.enable_tracking || !mem_mgr.allocations) {
        return;
    }

    for (uint32_t i = 0; i < mem_mgr.config.max_tracked_allocations; i++) {
        if (mem_mgr.allocations[i].ptr == ptr) {
            memset(&mem_mgr.allocations[i], 0, sizeof(mem_alloc_info_t));
            return;
        }
    }

    ESP_LOGW(TAG, "Allocation record not found for ptr %p", ptr);
}

void* mem_safe_malloc(size_t size, uint32_t caps, const char* file, int line) {
    if (!mem_mgr.initialized) {
        ESP_LOGE(TAG, "Memory manager not initialized");
        return NULL;
    }

    if (size == 0) {
        ESP_LOGW(TAG, "Zero size allocation requested");
        return NULL;
    }

    // Add guard bytes if corruption checking is enabled
    size_t actual_size = size;
    if (mem_mgr.config.enable_corruption_check) {
        actual_size += 2 * MEM_GUARD_SIZE;
    }

    void* ptr = heap_caps_malloc(actual_size, caps);
    
    if (xSemaphoreTake(mem_mgr.mutex, pdMS_TO_TICKS(100)) == pdTRUE) {
        if (ptr) {
            // Add guard patterns
            if (mem_mgr.config.enable_corruption_check) {
                uint32_t* guard_start = (uint32_t*)ptr;
                uint32_t* guard_end = (uint32_t*)((uint8_t*)ptr + actual_size - MEM_GUARD_SIZE);
                *guard_start = MEM_GUARD_PATTERN;
                *guard_end = MEM_GUARD_PATTERN;
                ptr = (uint8_t*)ptr + MEM_GUARD_SIZE;
            }

            // Update statistics
            mem_mgr.stats.total_allocated += size;
            mem_mgr.stats.current_allocated += size;
            mem_mgr.stats.allocation_count++;
            
            if (mem_mgr.stats.current_allocated > mem_mgr.stats.peak_allocated) {
                mem_mgr.stats.peak_allocated = mem_mgr.stats.current_allocated;
            }

            // Add tracking record
            add_allocation_record(ptr, size, file, line);
        } else {
            mem_mgr.stats.failed_allocations++;
            ESP_LOGE(TAG, "Failed to allocate %zu bytes (caps: 0x%x) at %s:%d", 
                     size, caps, file, line);
        }
        
        xSemaphoreGive(mem_mgr.mutex);
    }

    return ptr;
}

void mem_safe_free(void* ptr, const char* file, int line) {
    if (!mem_mgr.initialized) {
        ESP_LOGE(TAG, "Memory manager not initialized");
        return;
    }

    if (!ptr) {
        ESP_LOGW(TAG, "Attempt to free NULL pointer at %s:%d", file, line);
        return;
    }

    if (xSemaphoreTake(mem_mgr.mutex, pdMS_TO_TICKS(100)) == pdTRUE) {
        // Find allocation record to get size
        size_t size = 0;
        if (mem_mgr.config.enable_tracking && mem_mgr.allocations) {
            for (uint32_t i = 0; i < mem_mgr.config.max_tracked_allocations; i++) {
                if (mem_mgr.allocations[i].ptr == ptr) {
                    size = mem_mgr.allocations[i].size;
                    break;
                }
            }
        }

        // Check guard patterns
        void* actual_ptr = ptr;
        if (mem_mgr.config.enable_corruption_check) {
            actual_ptr = (uint8_t*)ptr - MEM_GUARD_SIZE;
            uint32_t* guard_start = (uint32_t*)actual_ptr;
            
            if (*guard_start != MEM_GUARD_PATTERN) {
                ESP_LOGE(TAG, "Memory corruption detected (start guard) at %p, freed at %s:%d", 
                         ptr, file, line);
            }
        }

        // Update statistics
        if (size > 0) {
            mem_mgr.stats.current_allocated -= size;
        }
        mem_mgr.stats.free_count++;

        // Remove tracking record
        remove_allocation_record(ptr);

        xSemaphoreGive(mem_mgr.mutex);
    }

    // Free the actual memory
    if (mem_mgr.config.enable_corruption_check) {
        heap_caps_free((uint8_t*)ptr - MEM_GUARD_SIZE);
    } else {
        heap_caps_free(ptr);
    }
}

void* mem_safe_realloc(void* ptr, size_t size, uint32_t caps, const char* file, int line) {
    if (!ptr) {
        return mem_safe_malloc(size, caps, file, line);
    }

    if (size == 0) {
        mem_safe_free(ptr, file, line);
        return NULL;
    }

    // Find original size from tracking records
    size_t old_size = 0;
    if (mem_mgr.config.enable_tracking && mem_mgr.allocations) {
        for (uint32_t i = 0; i < mem_mgr.config.max_tracked_allocations; i++) {
            if (mem_mgr.allocations[i].ptr == ptr) {
                old_size = mem_mgr.allocations[i].size;
                break;
            }
        }
    }

    void* new_ptr = mem_safe_malloc(size, caps, file, line);
    if (new_ptr && ptr) {
        // Copy old data using the smaller of old_size or new size
        size_t copy_size = (old_size > 0 && old_size < size) ? old_size : size;
        memcpy(new_ptr, ptr, copy_size);
        mem_safe_free(ptr, file, line);
    }

    return new_ptr;
}

void mem_get_stats(mem_stats_t* stats) {
    if (!mem_mgr.initialized || !stats) {
        return;
    }

    if (xSemaphoreTake(mem_mgr.mutex, pdMS_TO_TICKS(100)) == pdTRUE) {
        memcpy(stats, &mem_mgr.stats, sizeof(mem_stats_t));
        xSemaphoreGive(mem_mgr.mutex);
    }
}

uint32_t mem_check_leaks(void) {
    if (!mem_mgr.initialized || !mem_mgr.config.enable_leak_detection || !mem_mgr.allocations) {
        return 0;
    }

    uint32_t leak_count = 0;
    
    if (xSemaphoreTake(mem_mgr.mutex, pdMS_TO_TICKS(100)) == pdTRUE) {
        ESP_LOGI(TAG, "=== Memory Leak Report ===");
        
        for (uint32_t i = 0; i < mem_mgr.config.max_tracked_allocations; i++) {
            if (mem_mgr.allocations[i].ptr != NULL) {
                ESP_LOGW(TAG, "LEAK: %zu bytes at %p, allocated at %s:%d (tick: %u)",
                         mem_mgr.allocations[i].size,
                         mem_mgr.allocations[i].ptr,
                         mem_mgr.allocations[i].file,
                         mem_mgr.allocations[i].line,
                         mem_mgr.allocations[i].timestamp);
                leak_count++;
            }
        }
        
        if (leak_count == 0) {
            ESP_LOGI(TAG, "No memory leaks detected");
        } else {
            ESP_LOGW(TAG, "Total leaks: %u", leak_count);
        }
        
        xSemaphoreGive(mem_mgr.mutex);
    }

    return leak_count;
}

void mem_print_report(void) {
    if (!mem_mgr.initialized) {
        ESP_LOGE(TAG, "Memory manager not initialized");
        return;
    }

    mem_stats_t stats;
    mem_get_stats(&stats);

    ESP_LOGI(TAG, "=== Memory Usage Report ===");
    ESP_LOGI(TAG, "Total allocated: %zu bytes", stats.total_allocated);
    ESP_LOGI(TAG, "Peak allocated: %zu bytes", stats.peak_allocated);
    ESP_LOGI(TAG, "Current allocated: %zu bytes", stats.current_allocated);
    ESP_LOGI(TAG, "Allocation count: %zu", stats.allocation_count);
    ESP_LOGI(TAG, "Free count: %zu", stats.free_count);
    ESP_LOGI(TAG, "Failed allocations: %zu", stats.failed_allocations);

    // Print heap info
    multi_heap_info_t heap_info;
    heap_caps_get_info(&heap_info, MALLOC_CAP_INTERNAL);
    ESP_LOGI(TAG, "Internal heap - Free: %zu, Largest: %zu", 
             heap_info.total_free_bytes, heap_info.largest_free_block);

    heap_caps_get_info(&heap_info, MALLOC_CAP_SPIRAM);
    ESP_LOGI(TAG, "PSRAM heap - Free: %zu, Largest: %zu", 
             heap_info.total_free_bytes, heap_info.largest_free_block);
}

bool mem_check_corruption(void) {
    if (!mem_mgr.initialized || !mem_mgr.config.enable_corruption_check || !mem_mgr.allocations) {
        return false;
    }

    bool corruption_found = false;

    if (xSemaphoreTake(mem_mgr.mutex, pdMS_TO_TICKS(100)) == pdTRUE) {
        for (uint32_t i = 0; i < mem_mgr.config.max_tracked_allocations; i++) {
            if (mem_mgr.allocations[i].ptr != NULL) {
                uint8_t* ptr = (uint8_t*)mem_mgr.allocations[i].ptr;
                uint32_t* guard_start = (uint32_t*)(ptr - MEM_GUARD_SIZE);
                uint32_t* guard_end = (uint32_t*)(ptr + mem_mgr.allocations[i].size);

                if (*guard_start != MEM_GUARD_PATTERN || *guard_end != MEM_GUARD_PATTERN) {
                    ESP_LOGE(TAG, "Memory corruption detected at %p (allocated at %s:%d)",
                             mem_mgr.allocations[i].ptr,
                             mem_mgr.allocations[i].file,
                             mem_mgr.allocations[i].line);
                    corruption_found = true;
                }
            }
        }
        xSemaphoreGive(mem_mgr.mutex);
    }

    return corruption_found;
}
