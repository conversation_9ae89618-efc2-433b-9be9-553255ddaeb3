{"version": "1.2", "project_name": "ameter_s3", "project_version": "1", "project_path": "D:/ESP-Project/ameter00", "idf_path": "D:/ESPRESSIF/v5.4.1/esp-idf", "build_dir": "D:/ESP-Project/ameter00/build", "config_file": "D:/ESP-Project/ameter00/sdkconfig", "config_defaults": "D:/ESP-Project/ameter00/sdkconfig.defaults", "bootloader_elf": "D:/ESP-Project/ameter00/build/bootloader/bootloader.elf", "app_elf": "ameter_s3.elf", "app_bin": "ameter_s3.bin", "build_type": "flash_app", "git_revision": "v5.4.1", "target": "esp32s3", "rev": "", "min_rev": "2", "max_rev": "99", "phy_data_partition": "", "monitor_baud": "115200", "monitor_toolprefix": "xtensa-esp32s3-elf-", "c_compiler": "D:/ESP-IDF/tools/xtensa-esp-elf/esp-14.2.0_20241119/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc.exe", "config_environment": {"COMPONENT_KCONFIGS": "D:/ESPRESSIF/v5.4.1/esp-idf/components/app_trace/Kconfig;D:/ESPRESSIF/v5.4.1/esp-idf/components/bt/Kconfig;D:/ESPRESSIF/v5.4.1/esp-idf/components/console/Kconfig;D:/ESPRESSIF/v5.4.1/esp-idf/components/driver/Kconfig;D:/ESPRESSIF/v5.4.1/esp-idf/components/efuse/Kconfig;D:/ESPRESSIF/v5.4.1/esp-idf/components/esp-tls/Kconfig;D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_adc/Kconfig;D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_coex/Kconfig;D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_common/Kconfig;D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_driver_ana_cmpr/Kconfig;D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_driver_cam/Kconfig;D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_driver_dac/Kconfig;D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_driver_gpio/Kconfig;D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_driver_gptimer/Kconfig;D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_driver_i2c/Kconfig;D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_driver_i2s/Kconfig;D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_driver_isp/Kconfig;D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_driver_jpeg/Kconfig;D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_driver_ledc/Kconfig;D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_driver_mcpwm/Kconfig;D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_driver_parlio/Kconfig;D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_driver_pcnt/Kconfig;D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_driver_rmt/Kconfig;D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_driver_sdm/Kconfig;D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_driver_spi/Kconfig;D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_driver_touch_sens/Kconfig;D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_driver_tsens/Kconfig;D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_driver_uart/Kconfig;D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_driver_usb_serial_jtag/Kconfig;D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_eth/Kconfig;D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_event/Kconfig;D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_gdbstub/Kconfig;D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_hid/Kconfig;D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_http_client/Kconfig;D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_http_server/Kconfig;D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_https_ota/Kconfig;D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_https_server/Kconfig;D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_hw_support/Kconfig;D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_lcd/Kconfig;D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_mm/Kconfig;D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_netif/Kconfig;D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_partition/Kconfig;D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_phy/Kconfig;D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_pm/Kconfig;D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_psram/Kconfig;D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_ringbuf/Kconfig;D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_security/Kconfig;D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_system/Kconfig;D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_timer/Kconfig;D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_wifi/Kconfig;D:/ESPRESSIF/v5.4.1/esp-idf/components/espcoredump/Kconfig;D:/ESPRESSIF/v5.4.1/esp-idf/components/fatfs/Kconfig;D:/ESPRESSIF/v5.4.1/esp-idf/components/freertos/Kconfig;D:/ESPRESSIF/v5.4.1/esp-idf/components/hal/Kconfig;D:/ESPRESSIF/v5.4.1/esp-idf/components/heap/Kconfig;D:/ESPRESSIF/v5.4.1/esp-idf/components/ieee802154/Kconfig;D:/ESPRESSIF/v5.4.1/esp-idf/components/log/Kconfig;D:/ESPRESSIF/v5.4.1/esp-idf/components/lwip/Kconfig;D:/ESPRESSIF/v5.4.1/esp-idf/components/mbedtls/Kconfig;D:/ESPRESSIF/v5.4.1/esp-idf/components/mqtt/esp-mqtt/Kconfig;D:/ESPRESSIF/v5.4.1/esp-idf/components/newlib/Kconfig;D:/ESPRESSIF/v5.4.1/esp-idf/components/nvs_flash/Kconfig;D:/ESPRESSIF/v5.4.1/esp-idf/components/nvs_sec_provider/Kconfig;D:/ESPRESSIF/v5.4.1/esp-idf/components/openthread/Kconfig;D:/ESPRESSIF/v5.4.1/esp-idf/components/protocomm/Kconfig;D:/ESPRESSIF/v5.4.1/esp-idf/components/pthread/Kconfig;D:/ESPRESSIF/v5.4.1/esp-idf/components/soc/Kconfig;D:/ESPRESSIF/v5.4.1/esp-idf/components/spi_flash/Kconfig;D:/ESPRESSIF/v5.4.1/esp-idf/components/spiffs/Kconfig;D:/ESPRESSIF/v5.4.1/esp-idf/components/tcp_transport/Kconfig;D:/ESPRESSIF/v5.4.1/esp-idf/components/ulp/Kconfig;D:/ESPRESSIF/v5.4.1/esp-idf/components/unity/Kconfig;D:/ESPRESSIF/v5.4.1/esp-idf/components/usb/Kconfig;D:/ESPRESSIF/v5.4.1/esp-idf/components/vfs/Kconfig;D:/ESPRESSIF/v5.4.1/esp-idf/components/wear_levelling/Kconfig;D:/ESPRESSIF/v5.4.1/esp-idf/components/wifi_provisioning/Kconfig;D:/ESP-Project/ameter00/components/bus/Kconfig;D:/ESP-Project/ameter00/components/espressif__button/Kconfig;D:/ESP-Project/ameter00/managed_components/espressif__cmake_utilities/Kconfig;D:/ESP-Project/ameter00/components/espressif__esp32-camera/Kconfig;D:/ESP-Project/ameter00/components/espressif__esp_codec_dev/Kconfig;D:/ESP-Project/ameter00/components/espressif__esp_lcd_touch/Kconfig;D:/ESP-Project/ameter00/components/fusion_ameter_s3/Kconfig;D:/ESP-Project/ameter00/components/i2cdev/Kconfig;D:/ESP-Project/ameter00/components/modules/Kconfig;D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/Kconfig;D:/ESP-Project/ameter00/managed_components/espressif__esp-modbus/Kconfig;D:/ESP-Project/ameter00/managed_components/espressif__esp32_s3_eye/Kconfig;D:/ESP-Project/ameter00/managed_components/espressif__esp_lvgl_port/Kconfig;D:/ESP-Project/ameter00/managed_components/espressif__mdns/Kconfig;D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/Kconfig", "COMPONENT_KCONFIGS_PROJBUILD": "D:/ESPRESSIF/v5.4.1/esp-idf/components/bootloader/Kconfig.projbuild;D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_app_format/Kconfig.projbuild;D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_rom/Kconfig.projbuild;D:/ESPRESSIF/v5.4.1/esp-idf/components/esptool_py/Kconfig.projbuild;D:/ESPRESSIF/v5.4.1/esp-idf/components/partition_table/Kconfig.projbuild;D:/ESP-Project/ameter00/managed_components/espressif__esp-sr/Kconfig.projbuild"}, "common_component_reqs": ["cxx", "newlib", "freertos", "esp_hw_support", "heap", "log", "soc", "hal", "esp_rom", "esp_common", "esp_system", "xtensa"], "build_components": ["app_trace", "app_update", "bootloader", "bootloader_support", "bt", "bus", "cmock", "console", "cxx", "driver", "efuse", "esp-dl", "esp-tls", "esp_adc", "esp_app_format", "esp_bootloader_format", "esp_coex", "esp_common", "esp_driver_ana_cmpr", "esp_driver_cam", "esp_driver_dac", "esp_driver_gpio", "esp_driver_gptimer", "esp_driver_i2c", "esp_driver_i2s", "esp_driver_isp", "esp_driver_jpeg", "esp_driver_ledc", "esp_driver_mcpwm", "esp_driver_parlio", "esp_driver_pcnt", "esp_driver_ppa", "esp_driver_rmt", "esp_driver_sdio", "esp_driver_sdm", "esp_driver_sdmmc", "esp_driver_sdspi", "esp_driver_spi", "esp_driver_touch_sens", "esp_driver_tsens", "esp_driver_uart", "esp_driver_usb_serial_jtag", "esp_eth", "esp_event", "esp_gdbstub", "esp_hid", "esp_http_client", "esp_http_server", "esp_https_ota", "esp_https_server", "esp_hw_support", "esp_lcd", "esp_local_ctrl", "esp_mm", "esp_netif", "esp_netif_stack", "esp_partition", "esp_phy", "esp_pm", "esp_psram", "esp_ringbuf", "esp_rom", "esp_security", "esp_system", "esp_timer", "esp_vfs_console", "esp_wifi", "espcoredump", "espressif__button", "espressif__cmake_utilities", "espressif__esp-dsp", "espressif__esp-modbus", "espressif__esp-sr", "espressif__esp32-camera", "espressif__esp32_s3_eye", "espressif__esp_codec_dev", "espressif__esp_io_expander", "espressif__esp_io_expander_tca95xx_16bit", "espressif__esp_lcd_touch", "espressif__esp_lcd_touch_cst816s", "espressif__esp_lvgl_port", "espressif__mdns", "esptool_py", "fatfs", "fb_gfx", "freertos", "fusion_ameter_s3", "hal", "heap", "http_parser", "i2cdev", "idf_test", "ieee802154", "json", "log", "lvgl__lvgl", "lwip", "main", "mbedtls", "modules", "mqtt", "newlib", "nvs_flash", "nvs_sec_provider", "openthread", "partition_table", "pcf8563", "perfmon", "protobuf-c", "protocomm", "pthread", "rt", "sdmmc", "soc", "spi_flash", "spiffs", "tcp_transport", "touch_element", "ulp", "unity", "usb", "vfs", "wear_levelling", "wifi_provisioning", "wpa_supplicant", "xtensa", ""], "build_component_paths": ["D:/ESPRESSIF/v5.4.1/esp-idf/components/app_trace", "D:/ESPRESSIF/v5.4.1/esp-idf/components/app_update", "D:/ESPRESSIF/v5.4.1/esp-idf/components/bootloader", "D:/ESPRESSIF/v5.4.1/esp-idf/components/bootloader_support", "D:/ESPRESSIF/v5.4.1/esp-idf/components/bt", "D:/ESP-Project/ameter00/components/bus", "D:/ESPRESSIF/v5.4.1/esp-idf/components/cmock", "D:/ESPRESSIF/v5.4.1/esp-idf/components/console", "D:/ESPRESSIF/v5.4.1/esp-idf/components/cxx", "D:/ESPRESSIF/v5.4.1/esp-idf/components/driver", "D:/ESPRESSIF/v5.4.1/esp-idf/components/efuse", "D:/ESP-Project/ameter00/components/esp-dl", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp-tls", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_adc", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_app_format", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_bootloader_format", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_coex", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_common", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_driver_ana_cmpr", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_driver_cam", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_driver_dac", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_driver_gpio", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_driver_gptimer", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_driver_i2c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_driver_i2s", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_driver_isp", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_driver_jpeg", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_driver_ledc", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_driver_mcpwm", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_driver_parlio", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_driver_pcnt", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_driver_ppa", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_driver_rmt", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_driver_sdio", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_driver_sdm", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_driver_sdmmc", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_driver_sdspi", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_driver_spi", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_driver_touch_sens", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_driver_tsens", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_driver_uart", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_driver_usb_serial_jtag", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_eth", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_event", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_gdbstub", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_hid", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_http_client", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_http_server", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_https_ota", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_https_server", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_hw_support", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_lcd", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_local_ctrl", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_mm", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_netif", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_netif_stack", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_partition", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_phy", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_pm", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_psram", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_ringbuf", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_rom", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_security", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_system", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_timer", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_vfs_console", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_wifi", "D:/ESPRESSIF/v5.4.1/esp-idf/components/espcoredump", "D:/ESP-Project/ameter00/components/espressif__button", "D:/ESP-Project/ameter00/managed_components/espressif__cmake_utilities", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp", "D:/ESP-Project/ameter00/managed_components/espressif__esp-modbus", "D:/ESP-Project/ameter00/managed_components/espressif__esp-sr", "D:/ESP-Project/ameter00/components/espressif__esp32-camera", "D:/ESP-Project/ameter00/managed_components/espressif__esp32_s3_eye", "D:/ESP-Project/ameter00/components/espressif__esp_codec_dev", "D:/ESP-Project/ameter00/components/espressif__esp_io_expander", "D:/ESP-Project/ameter00/components/espressif__esp_io_expander_tca95xx_16bit", "D:/ESP-Project/ameter00/components/espressif__esp_lcd_touch", "D:/ESP-Project/ameter00/components/espressif__esp_lcd_touch_cst816s", "D:/ESP-Project/ameter00/managed_components/espressif__esp_lvgl_port", "D:/ESP-Project/ameter00/managed_components/espressif__mdns", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esptool_py", "D:/ESPRESSIF/v5.4.1/esp-idf/components/fatfs", "D:/ESP-Project/ameter00/components/fb_gfx", "D:/ESPRESSIF/v5.4.1/esp-idf/components/freertos", "D:/ESP-Project/ameter00/components/fusion_ameter_s3", "D:/ESPRESSIF/v5.4.1/esp-idf/components/hal", "D:/ESPRESSIF/v5.4.1/esp-idf/components/heap", "D:/ESPRESSIF/v5.4.1/esp-idf/components/http_parser", "D:/ESP-Project/ameter00/components/i2cdev", "D:/ESPRESSIF/v5.4.1/esp-idf/components/idf_test", "D:/ESPRESSIF/v5.4.1/esp-idf/components/ieee802154", "D:/ESPRESSIF/v5.4.1/esp-idf/components/json", "D:/ESPRESSIF/v5.4.1/esp-idf/components/log", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl", "D:/ESPRESSIF/v5.4.1/esp-idf/components/lwip", "D:/ESP-Project/ameter00/main", "D:/ESPRESSIF/v5.4.1/esp-idf/components/mbedtls", "D:/ESP-Project/ameter00/components/modules", "D:/ESPRESSIF/v5.4.1/esp-idf/components/mqtt", "D:/ESPRESSIF/v5.4.1/esp-idf/components/newlib", "D:/ESPRESSIF/v5.4.1/esp-idf/components/nvs_flash", "D:/ESPRESSIF/v5.4.1/esp-idf/components/nvs_sec_provider", "D:/ESPRESSIF/v5.4.1/esp-idf/components/openthread", "D:/ESPRESSIF/v5.4.1/esp-idf/components/partition_table", "D:/ESP-Project/ameter00/components/pcf8563", "D:/ESPRESSIF/v5.4.1/esp-idf/components/perfmon", "D:/ESPRESSIF/v5.4.1/esp-idf/components/protobuf-c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/protocomm", "D:/ESPRESSIF/v5.4.1/esp-idf/components/pthread", "D:/ESPRESSIF/v5.4.1/esp-idf/components/rt", "D:/ESPRESSIF/v5.4.1/esp-idf/components/sdmmc", "D:/ESPRESSIF/v5.4.1/esp-idf/components/soc", "D:/ESPRESSIF/v5.4.1/esp-idf/components/spi_flash", "D:/ESPRESSIF/v5.4.1/esp-idf/components/spiffs", "D:/ESPRESSIF/v5.4.1/esp-idf/components/tcp_transport", "D:/ESPRESSIF/v5.4.1/esp-idf/components/touch_element", "D:/ESPRESSIF/v5.4.1/esp-idf/components/ulp", "D:/ESPRESSIF/v5.4.1/esp-idf/components/unity", "D:/ESPRESSIF/v5.4.1/esp-idf/components/usb", "D:/ESPRESSIF/v5.4.1/esp-idf/components/vfs", "D:/ESPRESSIF/v5.4.1/esp-idf/components/wear_levelling", "D:/ESPRESSIF/v5.4.1/esp-idf/components/wifi_provisioning", "D:/ESPRESSIF/v5.4.1/esp-idf/components/wpa_supplicant", "D:/ESPRESSIF/v5.4.1/esp-idf/components/xtensa", ""], "build_component_info": {"app_trace": {"alias": "idf::app_trace", "target": "___idf_app_trace", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/app_trace", "type": "LIBRARY", "lib": "__idf_app_trace", "reqs": ["esp_timer"], "priv_reqs": ["esp_driver_gptimer", "esp_driver_gpio", "esp_driver_uart"], "managed_reqs": [], "managed_priv_reqs": [], "file": "D:/ESP-Project/ameter00/build/esp-idf/app_trace/libapp_trace.a", "sources": ["D:/ESPRESSIF/v5.4.1/esp-idf/components/app_trace/app_trace.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/app_trace/app_trace_util.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/app_trace/host_file_io.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/app_trace/port/port_uart.c"], "include_dirs": ["include"]}, "app_update": {"alias": "idf::app_update", "target": "___idf_app_update", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/app_update", "type": "LIBRARY", "lib": "__idf_app_update", "reqs": ["partition_table", "bootloader_support", "esp_app_format", "esp_bootloader_format", "esp_partition"], "priv_reqs": ["esptool_py", "efuse", "spi_flash"], "managed_reqs": [], "managed_priv_reqs": [], "file": "D:/ESP-Project/ameter00/build/esp-idf/app_update/libapp_update.a", "sources": ["D:/ESPRESSIF/v5.4.1/esp-idf/components/app_update/esp_ota_ops.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/app_update/esp_ota_app_desc.c"], "include_dirs": ["include"]}, "bootloader": {"alias": "idf::bootloader", "target": "___idf_bootloader", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/bootloader", "type": "CONFIG_ONLY", "lib": "__idf_bootloader", "reqs": [], "priv_reqs": ["partition_table", "esptool_py"], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": []}, "bootloader_support": {"alias": "idf::bootloader_support", "target": "___idf_bootloader_support", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/bootloader_support", "type": "LIBRARY", "lib": "__idf_bootloader_support", "reqs": ["soc"], "priv_reqs": ["spi_flash", "mbedtls", "efuse", "heap", "esp_bootloader_format", "esp_app_format"], "managed_reqs": [], "managed_priv_reqs": [], "file": "D:/ESP-Project/ameter00/build/esp-idf/bootloader_support/libbootloader_support.a", "sources": ["D:/ESPRESSIF/v5.4.1/esp-idf/components/bootloader_support/src/bootloader_common.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/bootloader_support/src/bootloader_common_loader.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/bootloader_support/src/bootloader_clock_init.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/bootloader_support/src/bootloader_mem.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/bootloader_support/src/bootloader_random.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/bootloader_support/src/bootloader_efuse.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/bootloader_support/src/flash_encrypt.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/bootloader_support/src/secure_boot.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/bootloader_support/src/bootloader_random_esp32s3.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/bootloader_support/bootloader_flash/src/bootloader_flash.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/bootloader_support/bootloader_flash/src/flash_qio_mode.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/bootloader_support/bootloader_flash/src/bootloader_flash_config_esp32s3.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/bootloader_support/src/bootloader_utility.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/bootloader_support/src/flash_partitions.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/bootloader_support/src/esp_image_format.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/bootloader_support/src/idf/bootloader_sha.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/bootloader_support/src/esp32s3/secure_boot_secure_features.c"], "include_dirs": ["include", "bootloader_flash/include"]}, "bt": {"alias": "idf::bt", "target": "___idf_bt", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/bt", "type": "CONFIG_ONLY", "lib": "__idf_bt", "reqs": ["esp_timer", "esp_wifi"], "priv_reqs": ["nvs_flash", "soc", "esp_pm", "esp_phy", "esp_coex", "mbedtls", "esp_driver_uart", "vfs", "esp_ringbuf", "esp_driver_spi", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": []}, "bus": {"alias": "idf::bus", "target": "___idf_bus", "prefix": "idf", "dir": "D:/ESP-Project/ameter00/components/bus", "type": "LIBRARY", "lib": "__idf_bus", "reqs": ["driver"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "D:/ESP-Project/ameter00/build/esp-idf/bus/libbus.a", "sources": ["D:/ESP-Project/ameter00/components/bus/8080_lcd_esp32s3.c", "D:/ESP-Project/ameter00/components/bus/i2c_bus.c", "D:/ESP-Project/ameter00/components/bus/i2s_lcd_esp32_driver.c", "D:/ESP-Project/ameter00/components/bus/i2s_lcd_esp32s2_driver.c", "D:/ESP-Project/ameter00/components/bus/spi_bus.c"], "include_dirs": ["include"]}, "cmock": {"alias": "idf::cmock", "target": "___idf_cmock", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/cmock", "type": "LIBRARY", "lib": "__idf_cmock", "reqs": ["unity"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "D:/ESP-Project/ameter00/build/esp-idf/cmock/libcmock.a", "sources": ["D:/ESPRESSIF/v5.4.1/esp-idf/components/cmock/CMock/src/cmock.c"], "include_dirs": ["CMock/src"]}, "console": {"alias": "idf::console", "target": "___idf_console", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/console", "type": "LIBRARY", "lib": "__idf_console", "reqs": ["vfs", "esp_vfs_console"], "priv_reqs": ["esp_driver_uart", "esp_driver_usb_serial_jtag"], "managed_reqs": [], "managed_priv_reqs": [], "file": "D:/ESP-Project/ameter00/build/esp-idf/console/libconsole.a", "sources": ["D:/ESPRESSIF/v5.4.1/esp-idf/components/console/commands.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/console/esp_console_common.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/console/split_argv.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/console/linenoise/linenoise.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/console/esp_console_repl_chip.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/console/argtable3/arg_cmd.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/console/argtable3/arg_date.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/console/argtable3/arg_dbl.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/console/argtable3/arg_dstr.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/console/argtable3/arg_end.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/console/argtable3/arg_file.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/console/argtable3/arg_hashtable.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/console/argtable3/arg_int.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/console/argtable3/arg_lit.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/console/argtable3/arg_rem.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/console/argtable3/arg_rex.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/console/argtable3/arg_str.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/console/argtable3/arg_utils.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/console/argtable3/argtable3.c"], "include_dirs": ["D:/ESPRESSIF/v5.4.1/esp-idf/components/console"]}, "cxx": {"alias": "idf::cxx", "target": "___idf_cxx", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/cxx", "type": "LIBRARY", "lib": "__idf_cxx", "reqs": [], "priv_reqs": ["pthread", "esp_system"], "managed_reqs": [], "managed_priv_reqs": [], "file": "D:/ESP-Project/ameter00/build/esp-idf/cxx/libcxx.a", "sources": ["D:/ESPRESSIF/v5.4.1/esp-idf/components/cxx/cxx_exception_stubs.cpp", "D:/ESPRESSIF/v5.4.1/esp-idf/components/cxx/cxx_guards.cpp", "D:/ESPRESSIF/v5.4.1/esp-idf/components/cxx/cxx_init.cpp"], "include_dirs": []}, "driver": {"alias": "idf::driver", "target": "___idf_driver", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/driver", "type": "LIBRARY", "lib": "__idf_driver", "reqs": ["esp_pm", "esp_ringbuf", "freertos", "soc", "hal", "esp_hw_support", "esp_driver_gpio", "esp_driver_pcnt", "esp_driver_gptimer", "esp_driver_spi", "esp_driver_mcpwm", "esp_driver_ana_cmpr", "esp_driver_i2s", "esp_driver_sdmmc", "esp_driver_sdspi", "esp_driver_sdio", "esp_driver_dac", "esp_driver_rmt", "esp_driver_tsens", "esp_driver_sdm", "esp_driver_i2c", "esp_driver_uart", "esp_driver_ledc", "esp_driver_parlio", "esp_driver_usb_serial_jtag"], "priv_reqs": ["efuse", "esp_timer", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "file": "D:/ESP-Project/ameter00/build/esp-idf/driver/libdriver.a", "sources": ["D:/ESPRESSIF/v5.4.1/esp-idf/components/driver/deprecated/adc_legacy.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/driver/deprecated/adc_dma_legacy.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/driver/deprecated/timer_legacy.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/driver/i2c/i2c.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/driver/deprecated/i2s_legacy.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/driver/deprecated/mcpwm_legacy.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/driver/deprecated/pcnt_legacy.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/driver/deprecated/rmt_legacy.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/driver/deprecated/sigma_delta_legacy.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/driver/deprecated/rtc_temperature_legacy.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/driver/touch_sensor/touch_sensor_common.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/driver/touch_sensor/esp32s3/touch_sensor.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/driver/twai/twai.c"], "include_dirs": ["deprecated", "i2c/include", "touch_sensor/include", "twai/include", "touch_sensor/esp32s3/include"]}, "efuse": {"alias": "idf::efuse", "target": "___idf_efuse", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/efuse", "type": "LIBRARY", "lib": "__idf_efuse", "reqs": [], "priv_reqs": ["bootloader_support", "soc", "spi_flash", "esp_system", "esp_partition", "esp_app_format"], "managed_reqs": [], "managed_priv_reqs": [], "file": "D:/ESP-Project/ameter00/build/esp-idf/efuse/libefuse.a", "sources": ["D:/ESPRESSIF/v5.4.1/esp-idf/components/efuse/esp32s3/esp_efuse_table.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/efuse/esp32s3/esp_efuse_fields.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/efuse/esp32s3/esp_efuse_rtc_calib.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/efuse/esp32s3/esp_efuse_utility.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/efuse/src/esp_efuse_api.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/efuse/src/esp_efuse_fields.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/efuse/src/esp_efuse_utility.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/efuse/src/efuse_controller/keys/with_key_purposes/esp_efuse_api_key.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/efuse/src/esp_efuse_startup.c"], "include_dirs": ["include", "esp32s3/include"]}, "esp-dl": {"alias": "idf::esp-dl", "target": "___idf_esp-dl", "prefix": "idf", "dir": "D:/ESP-Project/ameter00/components/esp-dl", "type": "CONFIG_ONLY", "lib": "__idf_esp-dl", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": ["include/", "include/tool", "include/typedef", "include/image", "include/math", "include/nn", "include/tvm", "include/layer", "include/detect", "include/model_zoo"]}, "esp-tls": {"alias": "idf::esp-tls", "target": "___idf_esp-tls", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp-tls", "type": "LIBRARY", "lib": "__idf_esp-tls", "reqs": ["mbedtls"], "priv_reqs": ["http_parser", "esp_timer", "lwip"], "managed_reqs": [], "managed_priv_reqs": [], "file": "D:/ESP-Project/ameter00/build/esp-idf/esp-tls/libesp-tls.a", "sources": ["D:/ESPRESSIF/v5.4.1/esp-idf/components/esp-tls/esp_tls.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp-tls/esp-tls-crypto/esp_tls_crypto.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp-tls/esp_tls_error_capture.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp-tls/esp_tls_platform_port.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp-tls/esp_tls_mbedtls.c"], "include_dirs": ["D:/ESPRESSIF/v5.4.1/esp-idf/components/esp-tls", "esp-tls-crypto"]}, "esp_adc": {"alias": "idf::esp_adc", "target": "___idf_esp_adc", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_adc", "type": "LIBRARY", "lib": "__idf_esp_adc", "reqs": [], "priv_reqs": ["driver", "esp_driver_gpio", "efuse", "esp_pm", "esp_ringbuf", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "file": "D:/ESP-Project/ameter00/build/esp-idf/esp_adc/libesp_adc.a", "sources": ["D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_adc/adc_oneshot.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_adc/adc_common.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_adc/adc_cali.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_adc/adc_cali_curve_fitting.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_adc/deprecated/esp_adc_cal_common_legacy.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_adc/adc_continuous.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_adc/adc_monitor.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_adc/gdma/adc_dma.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_adc/adc_filter.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_adc/esp32s3/curve_fitting_coefficients.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_adc/deprecated/esp32s3/esp_adc_cal_legacy.c"], "include_dirs": ["include", "interface", "esp32s3/include", "deprecated/include"]}, "esp_app_format": {"alias": "idf::esp_app_format", "target": "___idf_esp_app_format", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_app_format", "type": "LIBRARY", "lib": "__idf_esp_app_format", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "D:/ESP-Project/ameter00/build/esp-idf/esp_app_format/libesp_app_format.a", "sources": ["D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_app_format/esp_app_desc.c"], "include_dirs": ["include"]}, "esp_bootloader_format": {"alias": "idf::esp_bootloader_format", "target": "___idf_esp_bootloader_format", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_bootloader_format", "type": "LIBRARY", "lib": "__idf_esp_bootloader_format", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "D:/ESP-Project/ameter00/build/esp-idf/esp_bootloader_format/libesp_bootloader_format.a", "sources": ["D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_bootloader_format/esp_bootloader_desc.c"], "include_dirs": ["include"]}, "esp_coex": {"alias": "idf::esp_coex", "target": "___idf_esp_coex", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_coex", "type": "LIBRARY", "lib": "__idf_esp_coex", "reqs": [], "priv_reqs": ["esp_timer", "driver", "esp_event"], "managed_reqs": [], "managed_priv_reqs": [], "file": "D:/ESP-Project/ameter00/build/esp-idf/esp_coex/libesp_coex.a", "sources": ["D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_coex/esp32s3/esp_coex_adapter.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_coex/src/coexist_debug_diagram.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_coex/src/coexist_debug.c"], "include_dirs": ["include"]}, "esp_common": {"alias": "idf::esp_common", "target": "___idf_esp_common", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_common", "type": "LIBRARY", "lib": "__idf_esp_common", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "D:/ESP-Project/ameter00/build/esp-idf/esp_common/libesp_common.a", "sources": ["D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_common/src/esp_err_to_name.c"], "include_dirs": ["include"]}, "esp_driver_ana_cmpr": {"alias": "idf::esp_driver_ana_cmpr", "target": "___idf_esp_driver_ana_cmpr", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_driver_ana_cmpr", "type": "CONFIG_ONLY", "lib": "__idf_esp_driver_ana_cmpr", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": ["include"]}, "esp_driver_cam": {"alias": "idf::esp_driver_cam", "target": "___idf_esp_driver_cam", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_driver_cam", "type": "LIBRARY", "lib": "__idf_esp_driver_cam", "reqs": ["esp_driver_isp", "esp_mm"], "priv_reqs": ["esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "file": "D:/ESP-Project/ameter00/build/esp-idf/esp_driver_cam/libesp_driver_cam.a", "sources": ["D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_driver_cam/esp_cam_ctlr.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_driver_cam/dvp_share_ctrl.c"], "include_dirs": ["include", "interface"]}, "esp_driver_dac": {"alias": "idf::esp_driver_dac", "target": "___idf_esp_driver_dac", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_driver_dac", "type": "CONFIG_ONLY", "lib": "__idf_esp_driver_dac", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": ["./include"]}, "esp_driver_gpio": {"alias": "idf::esp_driver_gpio", "target": "___idf_esp_driver_gpio", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_driver_gpio", "type": "LIBRARY", "lib": "__idf_esp_driver_gpio", "reqs": [], "priv_reqs": ["esp_pm"], "managed_reqs": [], "managed_priv_reqs": [], "file": "D:/ESP-Project/ameter00/build/esp-idf/esp_driver_gpio/libesp_driver_gpio.a", "sources": ["D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_driver_gpio/src/gpio.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_driver_gpio/src/gpio_glitch_filter_ops.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_driver_gpio/src/rtc_io.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_driver_gpio/src/dedic_gpio.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_driver_gpio/src/gpio_pin_glitch_filter.c"], "include_dirs": ["include"]}, "esp_driver_gptimer": {"alias": "idf::esp_driver_gptimer", "target": "___idf_esp_driver_gptimer", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_driver_gptimer", "type": "LIBRARY", "lib": "__idf_esp_driver_gptimer", "reqs": ["esp_pm"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "D:/ESP-Project/ameter00/build/esp-idf/esp_driver_gptimer/libesp_driver_gptimer.a", "sources": ["D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_driver_gptimer/src/gptimer.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_driver_gptimer/src/gptimer_common.c"], "include_dirs": ["include"]}, "esp_driver_i2c": {"alias": "idf::esp_driver_i2c", "target": "___idf_esp_driver_i2c", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_driver_i2c", "type": "LIBRARY", "lib": "__idf_esp_driver_i2c", "reqs": [], "priv_reqs": ["esp_driver_gpio", "esp_pm", "esp_ringbuf"], "managed_reqs": [], "managed_priv_reqs": [], "file": "D:/ESP-Project/ameter00/build/esp-idf/esp_driver_i2c/libesp_driver_i2c.a", "sources": ["D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_driver_i2c/i2c_master.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_driver_i2c/i2c_common.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_driver_i2c/i2c_slave.c"], "include_dirs": ["include"]}, "esp_driver_i2s": {"alias": "idf::esp_driver_i2s", "target": "___idf_esp_driver_i2s", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_driver_i2s", "type": "LIBRARY", "lib": "__idf_esp_driver_i2s", "reqs": [], "priv_reqs": ["esp_driver_gpio", "esp_pm", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "file": "D:/ESP-Project/ameter00/build/esp-idf/esp_driver_i2s/libesp_driver_i2s.a", "sources": ["D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_driver_i2s/i2s_common.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_driver_i2s/i2s_std.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_driver_i2s/i2s_pdm.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_driver_i2s/i2s_tdm.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_driver_i2s/i2s_platform.c"], "include_dirs": ["include"]}, "esp_driver_isp": {"alias": "idf::esp_driver_isp", "target": "___idf_esp_driver_isp", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_driver_isp", "type": "CONFIG_ONLY", "lib": "__idf_esp_driver_isp", "reqs": ["esp_mm"], "priv_reqs": ["esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": ["include"]}, "esp_driver_jpeg": {"alias": "idf::esp_driver_jpeg", "target": "___idf_esp_driver_jpeg", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_driver_jpeg", "type": "CONFIG_ONLY", "lib": "__idf_esp_driver_jpeg", "reqs": [], "priv_reqs": ["esp_mm", "esp_pm"], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": ["include"]}, "esp_driver_ledc": {"alias": "idf::esp_driver_ledc", "target": "___idf_esp_driver_ledc", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_driver_ledc", "type": "LIBRARY", "lib": "__idf_esp_driver_ledc", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "file": "D:/ESP-Project/ameter00/build/esp-idf/esp_driver_ledc/libesp_driver_ledc.a", "sources": ["D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_driver_ledc/src/ledc.c"], "include_dirs": ["include"]}, "esp_driver_mcpwm": {"alias": "idf::esp_driver_mcpwm", "target": "___idf_esp_driver_mcpwm", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_driver_mcpwm", "type": "LIBRARY", "lib": "__idf_esp_driver_mcpwm", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "file": "D:/ESP-Project/ameter00/build/esp-idf/esp_driver_mcpwm/libesp_driver_mcpwm.a", "sources": ["D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_driver_mcpwm/src/mcpwm_cap.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_driver_mcpwm/src/mcpwm_cmpr.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_driver_mcpwm/src/mcpwm_com.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_driver_mcpwm/src/mcpwm_fault.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_driver_mcpwm/src/mcpwm_gen.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_driver_mcpwm/src/mcpwm_oper.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_driver_mcpwm/src/mcpwm_sync.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_driver_mcpwm/src/mcpwm_timer.c"], "include_dirs": ["include"]}, "esp_driver_parlio": {"alias": "idf::esp_driver_parlio", "target": "___idf_esp_driver_parlio", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_driver_parlio", "type": "CONFIG_ONLY", "lib": "__idf_esp_driver_parlio", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": ["include"]}, "esp_driver_pcnt": {"alias": "idf::esp_driver_pcnt", "target": "___idf_esp_driver_pcnt", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_driver_pcnt", "type": "LIBRARY", "lib": "__idf_esp_driver_pcnt", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "file": "D:/ESP-Project/ameter00/build/esp-idf/esp_driver_pcnt/libesp_driver_pcnt.a", "sources": ["D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_driver_pcnt/src/pulse_cnt.c"], "include_dirs": ["include"]}, "esp_driver_ppa": {"alias": "idf::esp_driver_ppa", "target": "___idf_esp_driver_ppa", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_driver_ppa", "type": "CONFIG_ONLY", "lib": "__idf_esp_driver_ppa", "reqs": [], "priv_reqs": ["esp_mm", "esp_pm"], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": ["include"]}, "esp_driver_rmt": {"alias": "idf::esp_driver_rmt", "target": "___idf_esp_driver_rmt", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_driver_rmt", "type": "LIBRARY", "lib": "__idf_esp_driver_rmt", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "file": "D:/ESP-Project/ameter00/build/esp-idf/esp_driver_rmt/libesp_driver_rmt.a", "sources": ["D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_driver_rmt/src/rmt_common.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_driver_rmt/src/rmt_encoder.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_driver_rmt/src/rmt_rx.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_driver_rmt/src/rmt_tx.c"], "include_dirs": ["include"]}, "esp_driver_sdio": {"alias": "idf::esp_driver_sdio", "target": "___idf_esp_driver_sdio", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_driver_sdio", "type": "CONFIG_ONLY", "lib": "__idf_esp_driver_sdio", "reqs": [], "priv_reqs": ["esp_driver_gpio", "esp_ringbuf"], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": ["include"]}, "esp_driver_sdm": {"alias": "idf::esp_driver_sdm", "target": "___idf_esp_driver_sdm", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_driver_sdm", "type": "LIBRARY", "lib": "__idf_esp_driver_sdm", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "file": "D:/ESP-Project/ameter00/build/esp-idf/esp_driver_sdm/libesp_driver_sdm.a", "sources": ["D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_driver_sdm/src/sdm.c"], "include_dirs": ["include"]}, "esp_driver_sdmmc": {"alias": "idf::esp_driver_sdmmc", "target": "___idf_esp_driver_sdmmc", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_driver_sdmmc", "type": "LIBRARY", "lib": "__idf_esp_driver_sdmmc", "reqs": ["sdmmc", "esp_driver_gpio"], "priv_reqs": ["esp_timer", "esp_pm", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "file": "D:/ESP-Project/ameter00/build/esp-idf/esp_driver_sdmmc/libesp_driver_sdmmc.a", "sources": ["D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_driver_sdmmc/src/sdmmc_transaction.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_driver_sdmmc/src/sdmmc_host.c"], "include_dirs": ["include"]}, "esp_driver_sdspi": {"alias": "idf::esp_driver_sdspi", "target": "___idf_esp_driver_sdspi", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_driver_sdspi", "type": "LIBRARY", "lib": "__idf_esp_driver_sdspi", "reqs": ["sdmmc", "esp_driver_spi", "esp_driver_gpio"], "priv_reqs": ["esp_timer"], "managed_reqs": [], "managed_priv_reqs": [], "file": "D:/ESP-Project/ameter00/build/esp-idf/esp_driver_sdspi/libesp_driver_sdspi.a", "sources": ["D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_driver_sdspi/src/sdspi_crc.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_driver_sdspi/src/sdspi_host.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_driver_sdspi/src/sdspi_transaction.c"], "include_dirs": ["include"]}, "esp_driver_spi": {"alias": "idf::esp_driver_spi", "target": "___idf_esp_driver_spi", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_driver_spi", "type": "LIBRARY", "lib": "__idf_esp_driver_spi", "reqs": ["esp_pm"], "priv_reqs": ["esp_timer", "esp_mm", "esp_driver_gpio", "esp_ringbuf"], "managed_reqs": [], "managed_priv_reqs": [], "file": "D:/ESP-Project/ameter00/build/esp-idf/esp_driver_spi/libesp_driver_spi.a", "sources": ["D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_driver_spi/src/gpspi/spi_common.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_driver_spi/src/gpspi/spi_master.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_driver_spi/src/gpspi/spi_slave.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_driver_spi/src/gpspi/spi_dma.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_driver_spi/src/gpspi/spi_slave_hd.c"], "include_dirs": ["include"]}, "esp_driver_touch_sens": {"alias": "idf::esp_driver_touch_sens", "target": "___idf_esp_driver_touch_sens", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_driver_touch_sens", "type": "CONFIG_ONLY", "lib": "__idf_esp_driver_touch_sens", "reqs": [], "priv_reqs": ["esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": []}, "esp_driver_tsens": {"alias": "idf::esp_driver_tsens", "target": "___idf_esp_driver_tsens", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_driver_tsens", "type": "LIBRARY", "lib": "__idf_esp_driver_tsens", "reqs": [], "priv_reqs": ["efuse"], "managed_reqs": [], "managed_priv_reqs": [], "file": "D:/ESP-Project/ameter00/build/esp-idf/esp_driver_tsens/libesp_driver_tsens.a", "sources": ["D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_driver_tsens/src/temperature_sensor.c"], "include_dirs": ["include"]}, "esp_driver_uart": {"alias": "idf::esp_driver_uart", "target": "___idf_esp_driver_uart", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_driver_uart", "type": "LIBRARY", "lib": "__idf_esp_driver_uart", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio", "esp_ringbuf"], "managed_reqs": [], "managed_priv_reqs": [], "file": "D:/ESP-Project/ameter00/build/esp-idf/esp_driver_uart/libesp_driver_uart.a", "sources": ["D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_driver_uart/src/uart.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_driver_uart/src/uart_vfs.c"], "include_dirs": ["include"]}, "esp_driver_usb_serial_jtag": {"alias": "idf::esp_driver_usb_serial_jtag", "target": "___idf_esp_driver_usb_serial_jtag", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_driver_usb_serial_jtag", "type": "LIBRARY", "lib": "__idf_esp_driver_usb_serial_jtag", "reqs": [], "priv_reqs": ["esp_driver_gpio", "esp_ringbuf", "esp_pm", "esp_timer"], "managed_reqs": [], "managed_priv_reqs": [], "file": "D:/ESP-Project/ameter00/build/esp-idf/esp_driver_usb_serial_jtag/libesp_driver_usb_serial_jtag.a", "sources": ["D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_driver_usb_serial_jtag/src/usb_serial_jtag.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_driver_usb_serial_jtag/src/usb_serial_jtag_connection_monitor.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_driver_usb_serial_jtag/src/usb_serial_jtag_vfs.c"], "include_dirs": ["include"]}, "esp_eth": {"alias": "idf::esp_eth", "target": "___idf_esp_eth", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_eth", "type": "CONFIG_ONLY", "lib": "__idf_esp_eth", "reqs": ["esp_event"], "priv_reqs": ["log", "esp_timer", "esp_driver_spi", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": []}, "esp_event": {"alias": "idf::esp_event", "target": "___idf_esp_event", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_event", "type": "LIBRARY", "lib": "__idf_esp_event", "reqs": ["log", "esp_common", "freertos"], "priv_reqs": ["esp_timer"], "managed_reqs": [], "managed_priv_reqs": [], "file": "D:/ESP-Project/ameter00/build/esp-idf/esp_event/libesp_event.a", "sources": ["D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_event/default_event_loop.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_event/esp_event.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_event/esp_event_private.c"], "include_dirs": ["include"]}, "esp_gdbstub": {"alias": "idf::esp_gdbstub", "target": "___idf_esp_gdbstub", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_gdbstub", "type": "LIBRARY", "lib": "__idf_esp_gdbstub", "reqs": ["freertos"], "priv_reqs": ["soc", "esp_rom", "esp_system"], "managed_reqs": [], "managed_priv_reqs": [], "file": "D:/ESP-Project/ameter00/build/esp-idf/esp_gdbstub/libesp_gdbstub.a", "sources": ["D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_gdbstub/src/gdbstub.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_gdbstub/src/gdbstub_transport.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_gdbstub/src/packet.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_gdbstub/src/port/xtensa/gdbstub_xtensa.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_gdbstub/src/port/xtensa/gdbstub-entry.S", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_gdbstub/src/port/xtensa/xt_debugexception.S"], "include_dirs": ["include"]}, "esp_hid": {"alias": "idf::esp_hid", "target": "___idf_esp_hid", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_hid", "type": "LIBRARY", "lib": "__idf_esp_hid", "reqs": ["esp_event", "bt"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "D:/ESP-Project/ameter00/build/esp-idf/esp_hid/libesp_hid.a", "sources": ["D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_hid/src/esp_hidd.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_hid/src/esp_hidh.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_hid/src/esp_hid_common.c"], "include_dirs": ["include"]}, "esp_http_client": {"alias": "idf::esp_http_client", "target": "___idf_esp_http_client", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_http_client", "type": "LIBRARY", "lib": "__idf_esp_http_client", "reqs": ["lwip", "esp_event"], "priv_reqs": ["tcp_transport", "http_parser"], "managed_reqs": [], "managed_priv_reqs": [], "file": "D:/ESP-Project/ameter00/build/esp-idf/esp_http_client/libesp_http_client.a", "sources": ["D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_http_client/esp_http_client.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_http_client/lib/http_auth.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_http_client/lib/http_header.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_http_client/lib/http_utils.c"], "include_dirs": ["include"]}, "esp_http_server": {"alias": "idf::esp_http_server", "target": "___idf_esp_http_server", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_http_server", "type": "LIBRARY", "lib": "__idf_esp_http_server", "reqs": ["http_parser", "esp_event"], "priv_reqs": ["mbedtls", "lwip", "esp_timer"], "managed_reqs": [], "managed_priv_reqs": [], "file": "D:/ESP-Project/ameter00/build/esp-idf/esp_http_server/libesp_http_server.a", "sources": ["D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_http_server/src/httpd_main.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_http_server/src/httpd_parse.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_http_server/src/httpd_sess.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_http_server/src/httpd_txrx.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_http_server/src/httpd_uri.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_http_server/src/httpd_ws.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_http_server/src/util/ctrl_sock.c"], "include_dirs": ["include"]}, "esp_https_ota": {"alias": "idf::esp_https_ota", "target": "___idf_esp_https_ota", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_https_ota", "type": "LIBRARY", "lib": "__idf_esp_https_ota", "reqs": ["esp_http_client", "bootloader_support", "esp_app_format", "esp_event"], "priv_reqs": ["log", "app_update"], "managed_reqs": [], "managed_priv_reqs": [], "file": "D:/ESP-Project/ameter00/build/esp-idf/esp_https_ota/libesp_https_ota.a", "sources": ["D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_https_ota/src/esp_https_ota.c"], "include_dirs": ["include"]}, "esp_https_server": {"alias": "idf::esp_https_server", "target": "___idf_esp_https_server", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_https_server", "type": "LIBRARY", "lib": "__idf_esp_https_server", "reqs": ["esp_http_server", "esp-tls", "esp_event"], "priv_reqs": ["lwip"], "managed_reqs": [], "managed_priv_reqs": [], "file": "D:/ESP-Project/ameter00/build/esp-idf/esp_https_server/libesp_https_server.a", "sources": ["D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_https_server/src/https_server.c"], "include_dirs": ["include"]}, "esp_hw_support": {"alias": "idf::esp_hw_support", "target": "___idf_esp_hw_support", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_hw_support", "type": "LIBRARY", "lib": "__idf_esp_hw_support", "reqs": ["soc"], "priv_reqs": ["efuse", "spi_flash", "bootloader_support", "esp_security", "esp_driver_gpio", "esp_timer", "esp_pm", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "file": "D:/ESP-Project/ameter00/build/esp-idf/esp_hw_support/libesp_hw_support.a", "sources": ["D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_hw_support/cpu.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_hw_support/port/esp32s3/esp_cpu_intr.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_hw_support/esp_memory_utils.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_hw_support/port/esp32s3/cpu_region_protect.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_hw_support/esp_clk.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_hw_support/clk_ctrl_os.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_hw_support/hw_random.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_hw_support/intr_alloc.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_hw_support/mac_addr.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_hw_support/periph_ctrl.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_hw_support/revision.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_hw_support/rtc_module.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_hw_support/sleep_modem.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_hw_support/sleep_modes.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_hw_support/sleep_console.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_hw_support/sleep_usb.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_hw_support/sleep_gpio.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_hw_support/sleep_event.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_hw_support/regi2c_ctrl.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_hw_support/esp_gpio_reserve.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_hw_support/sar_periph_ctrl_common.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_hw_support/port/esp32s3/io_mux.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_hw_support/port/esp32s3/esp_clk_tree.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_hw_support/port/esp_clk_tree_common.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_hw_support/dma/esp_dma_utils.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_hw_support/dma/gdma_link.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_hw_support/spi_share_hw_ctrl.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_hw_support/spi_bus_lock.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_hw_support/clk_utils.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_hw_support/adc_share_hw_ctrl.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_hw_support/dma/gdma.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_hw_support/deprecated/gdma_legacy.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_hw_support/dma/esp_async_memcpy.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_hw_support/dma/async_memcpy_gdma.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_hw_support/port/esp32s3/systimer.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_hw_support/mspi_timing_tuning.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_hw_support/mspi_timing_by_mspi_delay.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_hw_support/sleep_wake_stub.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_hw_support/esp_clock_output.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_hw_support/port/esp32s3/rtc_clk.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_hw_support/port/esp32s3/rtc_clk_init.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_hw_support/port/esp32s3/rtc_init.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_hw_support/port/esp32s3/rtc_sleep.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_hw_support/port/esp32s3/rtc_time.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_hw_support/port/esp32s3/chip_info.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_hw_support/port/esp32s3/sar_periph_ctrl.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_hw_support/port/esp32s3/mspi_timing_config.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_hw_support/port/esp32s3/esp_memprot.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_hw_support/port/esp_memprot_conv.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_hw_support/lowpower"], "include_dirs": ["include", "include/soc", "include/soc/esp32s3", "dma/include", "ldo/include", "debug_probe/include"]}, "esp_lcd": {"alias": "idf::esp_lcd", "target": "___idf_esp_lcd", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_lcd", "type": "LIBRARY", "lib": "__idf_esp_lcd", "reqs": ["driver", "esp_driver_gpio", "esp_driver_i2c", "esp_driver_spi"], "priv_reqs": ["esp_mm", "esp_psram", "esp_pm", "esp_driver_i2s"], "managed_reqs": [], "managed_priv_reqs": [], "file": "D:/ESP-Project/ameter00/build/esp-idf/esp_lcd/libesp_lcd.a", "sources": ["D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_lcd/src/esp_lcd_common.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_lcd/src/esp_lcd_panel_io.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_lcd/src/esp_lcd_panel_nt35510.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_lcd/src/esp_lcd_panel_ssd1306.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_lcd/src/esp_lcd_panel_st7789.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_lcd/src/esp_lcd_panel_ops.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_lcd/i2c/esp_lcd_panel_io_i2c_v1.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_lcd/i2c/esp_lcd_panel_io_i2c_v2.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_lcd/spi/esp_lcd_panel_io_spi.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_lcd/i80/esp_lcd_panel_io_i80.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_lcd/rgb/esp_lcd_panel_rgb.c"], "include_dirs": ["include", "interface", "rgb/include"]}, "esp_local_ctrl": {"alias": "idf::esp_local_ctrl", "target": "___idf_esp_local_ctrl", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_local_ctrl", "type": "LIBRARY", "lib": "__idf_esp_local_ctrl", "reqs": ["protocomm", "esp_https_server"], "priv_reqs": ["protobuf-c"], "managed_reqs": [], "managed_priv_reqs": [], "file": "D:/ESP-Project/ameter00/build/esp-idf/esp_local_ctrl/libesp_local_ctrl.a", "sources": ["D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_local_ctrl/src/esp_local_ctrl.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_local_ctrl/src/esp_local_ctrl_handler.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_local_ctrl/proto-c/esp_local_ctrl.pb-c.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_local_ctrl/src/esp_local_ctrl_transport_httpd.c"], "include_dirs": ["include"]}, "esp_mm": {"alias": "idf::esp_mm", "target": "___idf_esp_mm", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_mm", "type": "LIBRARY", "lib": "__idf_esp_mm", "reqs": [], "priv_reqs": ["heap", "spi_flash"], "managed_reqs": [], "managed_priv_reqs": [], "file": "D:/ESP-Project/ameter00/build/esp-idf/esp_mm/libesp_mm.a", "sources": ["D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_mm/esp_mmu_map.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_mm/port/esp32s3/ext_mem_layout.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_mm/esp_cache.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_mm/heap_align_hw.c"], "include_dirs": ["include"]}, "esp_netif": {"alias": "idf::esp_netif", "target": "___idf_esp_netif", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_netif", "type": "LIBRARY", "lib": "__idf_esp_netif", "reqs": ["esp_event"], "priv_reqs": ["esp_netif_stack"], "managed_reqs": [], "managed_priv_reqs": [], "file": "D:/ESP-Project/ameter00/build/esp-idf/esp_netif/libesp_netif.a", "sources": ["D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_netif/esp_netif_handlers.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_netif/esp_netif_objects.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_netif/esp_netif_defaults.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_netif/lwip/esp_netif_lwip.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_netif/lwip/esp_netif_sntp.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_netif/lwip/esp_netif_lwip_defaults.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_netif/lwip/netif/wlanif.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_netif/lwip/netif/ethernetif.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_netif/lwip/netif/esp_pbuf_ref.c"], "include_dirs": ["include"]}, "esp_netif_stack": {"alias": "idf::esp_netif_stack", "target": "___idf_esp_netif_stack", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_netif_stack", "type": "CONFIG_ONLY", "lib": "__idf_esp_netif_stack", "reqs": ["lwip"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": []}, "esp_partition": {"alias": "idf::esp_partition", "target": "___idf_esp_partition", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_partition", "type": "LIBRARY", "lib": "__idf_esp_partition", "reqs": [], "priv_reqs": ["esp_system", "spi_flash", "partition_table", "bootloader_support", "app_update"], "managed_reqs": [], "managed_priv_reqs": [], "file": "D:/ESP-Project/ameter00/build/esp-idf/esp_partition/libesp_partition.a", "sources": ["D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_partition/partition.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_partition/partition_target.c"], "include_dirs": ["include"]}, "esp_phy": {"alias": "idf::esp_phy", "target": "___idf_esp_phy", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_phy", "type": "LIBRARY", "lib": "__idf_esp_phy", "reqs": [], "priv_reqs": ["nvs_flash", "driver", "efuse", "esp_timer", "esp_wifi"], "managed_reqs": [], "managed_priv_reqs": [], "file": "D:/ESP-Project/ameter00/build/esp-idf/esp_phy/libesp_phy.a", "sources": ["D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_phy/src/phy_override.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_phy/src/lib_printf.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_phy/src/phy_common.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_phy/src/phy_init.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_phy/esp32s3/phy_init_data.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_phy/src/btbb_init.c"], "include_dirs": ["include", "esp32s3/include"]}, "esp_pm": {"alias": "idf::esp_pm", "target": "___idf_esp_pm", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_pm", "type": "LIBRARY", "lib": "__idf_esp_pm", "reqs": [], "priv_reqs": ["esp_system", "esp_driver_gpio", "esp_timer"], "managed_reqs": [], "managed_priv_reqs": [], "file": "D:/ESP-Project/ameter00/build/esp-idf/esp_pm/libesp_pm.a", "sources": ["D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_pm/pm_locks.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_pm/pm_trace.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_pm/pm_impl.c"], "include_dirs": ["include"]}, "esp_psram": {"alias": "idf::esp_psram", "target": "___idf_esp_psram", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_psram", "type": "LIBRARY", "lib": "__idf_esp_psram", "reqs": [], "priv_reqs": ["heap", "spi_flash", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "file": "D:/ESP-Project/ameter00/build/esp-idf/esp_psram/libesp_psram.a", "sources": ["D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_psram/esp_psram.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_psram/mmu_psram_flash.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_psram/esp32s3/esp_psram_impl_octal.c"], "include_dirs": ["include"]}, "esp_ringbuf": {"alias": "idf::esp_ringbuf", "target": "___idf_esp_ringbuf", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_ringbuf", "type": "LIBRARY", "lib": "__idf_esp_ringbuf", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "D:/ESP-Project/ameter00/build/esp-idf/esp_ringbuf/libesp_ringbuf.a", "sources": ["D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_ringbuf/ringbuf.c"], "include_dirs": ["include"]}, "esp_rom": {"alias": "idf::esp_rom", "target": "___idf_esp_rom", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_rom", "type": "LIBRARY", "lib": "__idf_esp_rom", "reqs": [], "priv_reqs": ["soc", "hal"], "managed_reqs": [], "managed_priv_reqs": [], "file": "D:/ESP-Project/ameter00/build/esp-idf/esp_rom/libesp_rom.a", "sources": ["D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_rom/patches/esp_rom_sys.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_rom/patches/esp_rom_print.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_rom/patches/esp_rom_crc.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_rom/patches/esp_rom_uart.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_rom/patches/esp_rom_spiflash.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_rom/patches/esp_rom_efuse.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_rom/patches/esp_rom_gpio.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_rom/patches/esp_rom_longjmp.S", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_rom/patches/esp_rom_systimer.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_rom/patches/esp_rom_wdt.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_rom/patches/esp_rom_cache_esp32s2_esp32s3.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_rom/patches/esp_rom_cache_writeback_esp32s3.S"], "include_dirs": ["include", "esp32s3/include", "esp32s3/include/esp32s3", "esp32s3"]}, "esp_security": {"alias": "idf::esp_security", "target": "___idf_esp_security", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_security", "type": "LIBRARY", "lib": "__idf_esp_security", "reqs": [], "priv_reqs": ["efuse", "esp_hw_support", "esp_system", "esp_timer"], "managed_reqs": [], "managed_priv_reqs": [], "file": "D:/ESP-Project/ameter00/build/esp-idf/esp_security/libesp_security.a", "sources": ["D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_security/src/init.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_security/src/esp_hmac.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_security/src/esp_ds.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_security/src/esp_crypto_lock.c"], "include_dirs": ["include"]}, "esp_system": {"alias": "idf::esp_system", "target": "___idf_esp_system", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_system", "type": "LIBRARY", "lib": "__idf_esp_system", "reqs": [], "priv_reqs": ["spi_flash", "esp_timer", "esp_mm", "bootloader_support", "esp_pm"], "managed_reqs": [], "managed_priv_reqs": [], "file": "D:/ESP-Project/ameter00/build/esp-idf/esp_system/libesp_system.a", "sources": ["D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_system/esp_err.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_system/crosscore_int.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_system/esp_ipc.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_system/freertos_hooks.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_system/int_wdt.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_system/panic.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_system/esp_system.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_system/startup.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_system/startup_funcs.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_system/system_time.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_system/stack_check.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_system/ubsan.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_system/xt_wdt.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_system/task_wdt/task_wdt.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_system/task_wdt/task_wdt_impl_timergroup.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_system/port/cpu_start.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_system/port/panic_handler.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_system/port/esp_system_chip.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_system/port/image_process.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_system/port/brownout.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_system/port/esp_ipc_isr.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_system/port/arch/xtensa/esp_ipc_isr_port.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_system/port/arch/xtensa/esp_ipc_isr_handler.S", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_system/port/arch/xtensa/esp_ipc_isr_routines.S", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_system/port/arch/xtensa/panic_arch.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_system/port/arch/xtensa/panic_handler_asm.S", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_system/port/arch/xtensa/expression_with_stack.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_system/port/arch/xtensa/expression_with_stack_asm.S", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_system/port/arch/xtensa/debug_helpers.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_system/port/arch/xtensa/debug_helpers_asm.S", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_system/port/arch/xtensa/debug_stubs.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_system/port/arch/xtensa/trax.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_system/port/soc/esp32s3/highint_hdl.S", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_system/port/soc/esp32s3/clk.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_system/port/soc/esp32s3/reset_reason.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_system/port/soc/esp32s3/system_internal.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_system/port/soc/esp32s3/cache_err_int.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_system/port/soc/esp32s3/apb_backup_dma.c"], "include_dirs": ["include"]}, "esp_timer": {"alias": "idf::esp_timer", "target": "___idf_esp_timer", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_timer", "type": "LIBRARY", "lib": "__idf_esp_timer", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "D:/ESP-Project/ameter00/build/esp-idf/esp_timer/libesp_timer.a", "sources": ["D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_timer/src/esp_timer.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_timer/src/esp_timer_init.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_timer/src/ets_timer_legacy.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_timer/src/system_time.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_timer/src/esp_timer_impl_common.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_timer/src/esp_timer_impl_systimer.c"], "include_dirs": ["include"]}, "esp_vfs_console": {"alias": "idf::esp_vfs_console", "target": "___idf_esp_vfs_console", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_vfs_console", "type": "LIBRARY", "lib": "__idf_esp_vfs_console", "reqs": [], "priv_reqs": ["vfs", "esp_driver_uart", "esp_driver_usb_serial_jtag"], "managed_reqs": [], "managed_priv_reqs": [], "file": "D:/ESP-Project/ameter00/build/esp-idf/esp_vfs_console/libesp_vfs_console.a", "sources": ["D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_vfs_console/vfs_console.c"], "include_dirs": ["include"]}, "esp_wifi": {"alias": "idf::esp_wifi", "target": "___idf_esp_wifi", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_wifi", "type": "LIBRARY", "lib": "__idf_esp_wifi", "reqs": ["esp_event", "esp_phy", "esp_netif"], "priv_reqs": ["driver", "esptool_py", "esp_pm", "esp_timer", "nvs_flash", "wpa_supplicant", "hal", "lwip", "esp_coex"], "managed_reqs": [], "managed_priv_reqs": [], "file": "D:/ESP-Project/ameter00/build/esp-idf/esp_wifi/libesp_wifi.a", "sources": ["D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_wifi/src/lib_printf.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_wifi/src/mesh_event.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_wifi/src/smartconfig.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_wifi/src/wifi_init.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_wifi/src/wifi_default.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_wifi/src/wifi_netif.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_wifi/src/wifi_default_ap.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_wifi/esp32s3/esp_adapter.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_wifi/src/smartconfig_ack.c"], "include_dirs": ["include", "include/local", "wifi_apps/include", "wifi_apps/nan_app/include"]}, "espcoredump": {"alias": "idf::espcoredump", "target": "___idf_espcoredump", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/espcoredump", "type": "LIBRARY", "lib": "__idf_espcoredump", "reqs": [], "priv_reqs": ["esp_partition", "spi_flash", "bootloader_support", "mbedtls", "esp_rom", "soc", "esp_system", "esp_driver_gpio", "driver"], "managed_reqs": [], "managed_priv_reqs": [], "file": "D:/ESP-Project/ameter00/build/esp-idf/espcoredump/libespcoredump.a", "sources": ["D:/ESPRESSIF/v5.4.1/esp-idf/components/espcoredump/src/core_dump_init.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/espcoredump/src/core_dump_common.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/espcoredump/src/core_dump_flash.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/espcoredump/src/core_dump_uart.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/espcoredump/src/core_dump_elf.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/espcoredump/src/core_dump_binary.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/espcoredump/src/core_dump_sha.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/espcoredump/src/core_dump_crc.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/espcoredump/src/port/xtensa/core_dump_port.c"], "include_dirs": ["include", "include/port/xtensa"]}, "espressif__button": {"alias": "idf::espressif__button", "target": "___idf_espressif__button", "prefix": "idf", "dir": "D:/ESP-Project/ameter00/components/espressif__button", "type": "LIBRARY", "lib": "__idf_espressif__button", "reqs": ["driver", "esp_adc"], "priv_reqs": ["esp_timer", "espressif__cmake_utilities"], "managed_reqs": [], "managed_priv_reqs": ["espressif__cmake_utilities"], "file": "D:/ESP-Project/ameter00/build/esp-idf/espressif__button/libespressif__button.a", "sources": ["D:/ESP-Project/ameter00/components/espressif__button/button_adc.c", "D:/ESP-Project/ameter00/components/espressif__button/button_gpio.c", "D:/ESP-Project/ameter00/components/espressif__button/iot_button.c", "D:/ESP-Project/ameter00/components/espressif__button/button_matrix.c"], "include_dirs": ["include"]}, "espressif__cmake_utilities": {"alias": "idf::espressif__cmake_utilities", "target": "___idf_espressif__cmake_utilities", "prefix": "idf", "dir": "D:/ESP-Project/ameter00/managed_components/espressif__cmake_utilities", "type": "CONFIG_ONLY", "lib": "__idf_espressif__cmake_utilities", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": []}, "espressif__esp-dsp": {"alias": "idf::espressif__esp-dsp", "target": "___idf_espressif__esp-dsp", "prefix": "idf", "dir": "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp", "type": "LIBRARY", "lib": "__idf_espressif__esp-dsp", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "D:/ESP-Project/ameter00/build/esp-idf/espressif__esp-dsp/libespressif__esp-dsp.a", "sources": ["D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/common/misc/dsps_pwroftwo.cpp", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/common/misc/aes3_tie_log.c", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/dotprod/float/dsps_dotprod_f32_ae32.S", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/dotprod/float/dsps_dotprod_f32_m_ae32.S", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/dotprod/float/dsps_dotprode_f32_ae32.S", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/dotprod/float/dsps_dotprode_f32_m_ae32.S", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/dotprod/float/dsps_dotprod_f32_ansi.c", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/dotprod/float/dsps_dotprode_f32_ansi.c", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/dotprod/float/dsps_dotprod_f32_aes3.S", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/dotprod/float/dsps_dotprod_f32_arp4.S", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/dotprod/float/dsps_dotprode_f32_arp4.S", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/dotprod/fixed/dsps_dotprod_s16_ae32.S", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/dotprod/fixed/dsps_dotprod_s16_m_ae32.S", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/dotprod/fixed/dsps_dotprod_s16_ansi.c", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/dotprod/fixed/dsps_dotprod_s16_arp4.S", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/dotprod/float/dspi_dotprod_f32_ansi.c", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/dotprod/float/dspi_dotprod_off_f32_ansi.c", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/dotprod/fixed/dspi_dotprod_s16_ansi.c", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/dotprod/fixed/dspi_dotprod_u16_ansi.c", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/dotprod/fixed/dspi_dotprod_s8_ansi.c", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/dotprod/fixed/dspi_dotprod_u8_ansi.c", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/dotprod/fixed/dspi_dotprod_off_s16_ansi.c", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/dotprod/fixed/dspi_dotprod_off_u16_ansi.c", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/dotprod/fixed/dspi_dotprod_off_s8_ansi.c", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/dotprod/fixed/dspi_dotprod_off_u8_ansi.c", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/dotprod/fixed/dspi_dotprod_s16_aes3.S", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/dotprod/fixed/dspi_dotprod_u16_aes3.S", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/dotprod/fixed/dspi_dotprod_off_s16_aes3.S", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/dotprod/fixed/dspi_dotprod_off_u16_aes3.S", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/dotprod/fixed/dspi_dotprod_s8_aes3.S", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/dotprod/fixed/dspi_dotprod_u8_aes3.S", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/dotprod/fixed/dspi_dotprod_off_u8_aes3.S", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/dotprod/fixed/dspi_dotprod_off_s8_aes3.S", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/dotprod/fixed/dspi_dotprod_s16_arp4.S", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/dotprod/fixed/dspi_dotprod_s8_arp4.S", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/dotprod/fixed/dspi_dotprod_u16_arp4.S", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/dotprod/fixed/dspi_dotprod_u8_arp4.S", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/dotprod/fixed/dspi_dotprod_off_s16_arp4.S", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/dotprod/fixed/dspi_dotprod_off_u16_arp4.S", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/dotprod/fixed/dspi_dotprod_off_s8_arp4.S", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/dotprod/fixed/dspi_dotprod_off_u8_arp4.S", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/matrix/mul/float/dspm_mult_3x3x1_f32_ae32.S", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/matrix/mul/float/dspm_mult_3x3x3_f32_ae32.S", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/matrix/mul/float/dspm_mult_4x4x1_f32_ae32.S", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/matrix/mul/float/dspm_mult_4x4x4_f32_ae32.S", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/matrix/mul/float/dspm_mult_f32_ae32.S", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/matrix/mul/float/dspm_mult_f32_aes3.S", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/matrix/mul/float/dspm_mult_f32_ansi.c", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/matrix/mul/float/dspm_mult_f32_arp4.S", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/matrix/mul/float/dspm_mult_ex_f32_ansi.c", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/matrix/mul/float/dspm_mult_ex_f32_ae32.S", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/matrix/mul/float/dspm_mult_ex_f32_arp4.S", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/matrix/mul/float/dspm_mult_ex_f32_aes3.S", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/matrix/mul/fixed/dspm_mult_s16_ae32.S", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/matrix/mul/fixed/dspm_mult_s16_m_ae32_vector.S", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/matrix/mul/fixed/dspm_mult_s16_m_ae32.S", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/matrix/mul/fixed/dspm_mult_s16_ansi.c", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/matrix/mul/fixed/dspm_mult_s16_aes3.S", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/matrix/mul/fixed/dspm_mult_s16_arp4.S", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/matrix/add/float/dspm_add_f32_ansi.c", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/matrix/add/float/dspm_add_f32_ae32.S", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/matrix/addc/float/dspm_addc_f32_ansi.c", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/matrix/addc/float/dspm_addc_f32_ae32.S", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/matrix/mulc/float/dspm_mulc_f32_ansi.c", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/matrix/mulc/float/dspm_mulc_f32_ae32.S", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/matrix/sub/float/dspm_sub_f32_ansi.c", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/matrix/sub/float/dspm_sub_f32_ae32.S", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/matrix/mat/mat.cpp", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/math/mulc/float/dsps_mulc_f32_ansi.c", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/math/addc/float/dsps_addc_f32_ansi.c", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/math/mulc/fixed/dsps_mulc_s16_ansi.c", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/math/mulc/fixed/dsps_mulc_s16_ae32.S", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/math/add/float/dsps_add_f32_ansi.c", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/math/add/fixed/dsps_add_s16_ansi.c", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/math/add/fixed/dsps_add_s16_ae32.S", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/math/add/fixed/dsps_add_s16_aes3.S", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/math/add/fixed/dsps_add_s8_ansi.c", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/math/add/fixed/dsps_add_s8_aes3.S", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/math/sub/float/dsps_sub_f32_ansi.c", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/math/sub/fixed/dsps_sub_s16_ansi.c", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/math/sub/fixed/dsps_sub_s16_ae32.S", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/math/sub/fixed/dsps_sub_s16_aes3.S", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/math/sub/fixed/dsps_sub_s8_ansi.c", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/math/sub/fixed/dsps_sub_s8_aes3.S", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/math/mul/float/dsps_mul_f32_ansi.c", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/math/mul/fixed/dsps_mul_s16_ansi.c", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/math/mul/fixed/dsps_mul_s16_ae32.S", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/math/mul/fixed/dsps_mul_s16_aes3.S", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/math/mul/fixed/dsps_mul_s8_ansi.c", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/math/mul/fixed/dsps_mul_s8_aes3.S", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/math/mulc/float/dsps_mulc_f32_ae32.S", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/math/addc/float/dsps_addc_f32_ae32.S", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/math/add/float/dsps_add_f32_ae32.S", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/math/sub/float/dsps_sub_f32_ae32.S", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/math/mul/float/dsps_mul_f32_ae32.S", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/math/sqrt/float/dsps_sqrt_f32_ansi.c", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/fft/float/dsps_fft2r_fc32_ae32_.S", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/fft/float/dsps_fft2r_fc32_aes3_.S", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/fft/float/dsps_fft2r_fc32_arp4.S", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/fft/float/dsps_fft2r_fc32_ansi.c", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/fft/float/dsps_fft2r_fc32_ae32.c", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/fft/float/dsps_bit_rev_lookup_fc32_aes3.S", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/fft/float/dsps_fft4r_fc32_ansi.c", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/fft/float/dsps_fft4r_fc32_ae32.c", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/fft/float/dsps_fft4r_fc32_ae32_.S", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/fft/float/dsps_fft4r_fc32_aes3_.S", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/fft/float/dsps_fft4r_fc32_arp4.S", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/fft/float/dsps_fft2r_bitrev_tables_fc32.c", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/fft/float/dsps_fft4r_bitrev_tables_fc32.c", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/fft/fixed/dsps_fft2r_sc16_ae32.S", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/fft/fixed/dsps_fft2r_sc16_ansi.c", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/fft/fixed/dsps_fft2r_sc16_aes3.S", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/fft/fixed/dsps_fft2r_sc16_arp4.S", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/dct/float/dsps_dct_f32.c", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/dct/float/dsps_dctiv_f32.c", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/dct/float/dsps_dstiv_f32.c", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/support/snr/float/dsps_snr_f32.cpp", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/support/sfdr/float/dsps_sfdr_f32.cpp", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/support/misc/dsps_d_gen.c", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/support/misc/dsps_h_gen.c", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/support/misc/dsps_tone_gen.c", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/support/cplx_gen/dsps_cplx_gen.c", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/support/cplx_gen/dsps_cplx_gen.S", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/support/cplx_gen/dsps_cplx_gen_init.c", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/support/mem/esp32s3/dsps_memset_aes3.S", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/support/mem/esp32s3/dsps_memcpy_aes3.S", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/support/view/dsps_view.cpp", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/windows/hann/float/dsps_wind_hann_f32.c", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/windows/blackman/float/dsps_wind_blackman_f32.c", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/windows/blackman_harris/float/dsps_wind_blackman_harris_f32.c", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/windows/blackman_nuttall/float/dsps_wind_blackman_nuttall_f32.c", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/windows/nuttall/float/dsps_wind_nuttall_f32.c", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/windows/flat_top/float/dsps_wind_flat_top_f32.c", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/conv/float/dsps_conv_f32_ansi.c", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/conv/float/dspi_conv_f32_ansi.c", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/conv/float/dsps_conv_f32_ae32.S", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/conv/float/dsps_corr_f32_ansi.c", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/conv/float/dsps_corr_f32_ae32.S", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/conv/float/dsps_ccorr_f32_ansi.c", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/conv/float/dsps_ccorr_f32_ae32.S", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/iir/biquad/dsps_biquad_f32_ae32.S", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/iir/biquad/dsps_biquad_sf32_ae32.S", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/iir/biquad/dsps_biquad_f32_aes3.S", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/iir/biquad/dsps_biquad_f32_arp4.S", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/iir/biquad/dsps_biquad_sf32_arp4.S", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/iir/biquad/dsps_biquad_f32_ansi.c", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/iir/biquad/dsps_biquad_sf32_ansi.c", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/iir/biquad/dsps_biquad_gen_f32.c", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/fir/float/dsps_fir_f32_ae32.S", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/fir/float/dsps_fir_f32_aes3.S", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/fir/float/dsps_fird_f32_ae32.S", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/fir/float/dsps_fird_f32_aes3.S", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/fir/float/dsps_fird_f32_arp4.S", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/fir/float/dsps_fir_f32_ansi.c", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/fir/float/dsps_fir_init_f32.c", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/fir/float/dsps_fird_f32_ansi.c", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/fir/float/dsps_fird_init_f32.c", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/fir/fixed/dsps_fird_init_s16.c", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/fir/fixed/dsps_fird_s16_ansi.c", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/fir/fixed/dsps_fird_s16_ae32.S", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/fir/fixed/dsps_fir_s16_m_ae32.S", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/fir/fixed/dsps_fird_s16_aes3.S", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/fir/fixed/dsps_fird_s16_arp4.S", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/fir/float/dsps_firmr_init_f32.c", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/fir/float/dsps_firmr_f32_ansi.c", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/fir/fixed/dsps_firmr_init_s16.c", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/fir/fixed/dsps_firmr_s16_ansi.c", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/fir/resampler/dsps_resampler_mr.c", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/fir/resampler/dsps_resampler_ph.c", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/kalman/ekf/common/ekf.cpp", "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp/modules/kalman/ekf_imu13states/ekf_imu13states.cpp"], "include_dirs": ["modules/dotprod/include", "modules/support/include", "modules/support/mem/include", "modules/windows/include", "modules/windows/hann/include", "modules/windows/blackman/include", "modules/windows/blackman_harris/include", "modules/windows/blackman_nuttall/include", "modules/windows/nuttall/include", "modules/windows/flat_top/include", "modules/iir/include", "modules/fir/include", "modules/math/include", "modules/math/add/include", "modules/math/sub/include", "modules/math/mul/include", "modules/math/addc/include", "modules/math/mulc/include", "modules/math/sqrt/include", "modules/matrix/mul/include", "modules/matrix/add/include", "modules/matrix/addc/include", "modules/matrix/mulc/include", "modules/matrix/sub/include", "modules/matrix/include", "modules/fft/include", "modules/dct/include", "modules/conv/include", "modules/common/include", "modules/matrix/mul/test/include", "modules/kalman/ekf/include", "modules/kalman/ekf_imu13states/include"]}, "espressif__esp-modbus": {"alias": "idf::espressif__esp-modbus", "target": "___idf_espressif__esp-modbus", "prefix": "idf", "dir": "D:/ESP-Project/ameter00/managed_components/espressif__esp-modbus", "type": "LIBRARY", "lib": "__idf_espressif__esp-modbus", "reqs": ["driver", "lwip", "esp_timer"], "priv_reqs": ["esp_netif"], "managed_reqs": [], "managed_priv_reqs": [], "file": "D:/ESP-Project/ameter00/build/esp-idf/espressif__esp-modbus/libespressif__esp-modbus.a", "sources": ["D:/ESP-Project/ameter00/managed_components/espressif__esp-modbus/freemodbus/common/esp_modbus_master.c", "D:/ESP-Project/ameter00/managed_components/espressif__esp-modbus/freemodbus/common/esp_modbus_slave.c", "D:/ESP-Project/ameter00/managed_components/espressif__esp-modbus/freemodbus/modbus/mb.c", "D:/ESP-Project/ameter00/managed_components/espressif__esp-modbus/freemodbus/modbus/mb_m.c", "D:/ESP-Project/ameter00/managed_components/espressif__esp-modbus/freemodbus/modbus/ascii/mbascii.c", "D:/ESP-Project/ameter00/managed_components/espressif__esp-modbus/freemodbus/modbus/ascii/mbascii_m.c", "D:/ESP-Project/ameter00/managed_components/espressif__esp-modbus/freemodbus/modbus/rtu/mbrtu_m.c", "D:/ESP-Project/ameter00/managed_components/espressif__esp-modbus/freemodbus/modbus/rtu/mbrtu.c", "D:/ESP-Project/ameter00/managed_components/espressif__esp-modbus/freemodbus/modbus/rtu/mbcrc.c", "D:/ESP-Project/ameter00/managed_components/espressif__esp-modbus/freemodbus/modbus/tcp/mbtcp.c", "D:/ESP-Project/ameter00/managed_components/espressif__esp-modbus/freemodbus/modbus/tcp/mbtcp_m.c", "D:/ESP-Project/ameter00/managed_components/espressif__esp-modbus/freemodbus/port/port.c", "D:/ESP-Project/ameter00/managed_components/espressif__esp-modbus/freemodbus/port/portevent.c", "D:/ESP-Project/ameter00/managed_components/espressif__esp-modbus/freemodbus/port/portevent_m.c", "D:/ESP-Project/ameter00/managed_components/espressif__esp-modbus/freemodbus/port/portother.c", "D:/ESP-Project/ameter00/managed_components/espressif__esp-modbus/freemodbus/port/portother_m.c", "D:/ESP-Project/ameter00/managed_components/espressif__esp-modbus/freemodbus/port/portserial.c", "D:/ESP-Project/ameter00/managed_components/espressif__esp-modbus/freemodbus/port/portserial_m.c", "D:/ESP-Project/ameter00/managed_components/espressif__esp-modbus/freemodbus/port/porttimer.c", "D:/ESP-Project/ameter00/managed_components/espressif__esp-modbus/freemodbus/port/porttimer_m.c", "D:/ESP-Project/ameter00/managed_components/espressif__esp-modbus/freemodbus/modbus/functions/mbfunccoils.c", "D:/ESP-Project/ameter00/managed_components/espressif__esp-modbus/freemodbus/modbus/functions/mbfunccoils_m.c", "D:/ESP-Project/ameter00/managed_components/espressif__esp-modbus/freemodbus/modbus/functions/mbfuncdiag.c", "D:/ESP-Project/ameter00/managed_components/espressif__esp-modbus/freemodbus/modbus/functions/mbfuncdisc.c", "D:/ESP-Project/ameter00/managed_components/espressif__esp-modbus/freemodbus/modbus/functions/mbfuncdisc_m.c", "D:/ESP-Project/ameter00/managed_components/espressif__esp-modbus/freemodbus/modbus/functions/mbfuncholding.c", "D:/ESP-Project/ameter00/managed_components/espressif__esp-modbus/freemodbus/modbus/functions/mbfuncholding_m.c", "D:/ESP-Project/ameter00/managed_components/espressif__esp-modbus/freemodbus/modbus/functions/mbfuncinput.c", "D:/ESP-Project/ameter00/managed_components/espressif__esp-modbus/freemodbus/modbus/functions/mbfuncinput_m.c", "D:/ESP-Project/ameter00/managed_components/espressif__esp-modbus/freemodbus/modbus/functions/mbfuncother.c", "D:/ESP-Project/ameter00/managed_components/espressif__esp-modbus/freemodbus/modbus/functions/mbutils.c", "D:/ESP-Project/ameter00/managed_components/espressif__esp-modbus/freemodbus/serial_slave/modbus_controller/mbc_serial_slave.c", "D:/ESP-Project/ameter00/managed_components/espressif__esp-modbus/freemodbus/serial_master/modbus_controller/mbc_serial_master.c", "D:/ESP-Project/ameter00/managed_components/espressif__esp-modbus/freemodbus/tcp_slave/port/port_tcp_slave.c", "D:/ESP-Project/ameter00/managed_components/espressif__esp-modbus/freemodbus/tcp_slave/modbus_controller/mbc_tcp_slave.c", "D:/ESP-Project/ameter00/managed_components/espressif__esp-modbus/freemodbus/tcp_master/modbus_controller/mbc_tcp_master.c", "D:/ESP-Project/ameter00/managed_components/espressif__esp-modbus/freemodbus/tcp_master/port/port_tcp_master.c", "D:/ESP-Project/ameter00/managed_components/espressif__esp-modbus/freemodbus/common/esp_modbus_master_tcp.c", "D:/ESP-Project/ameter00/managed_components/espressif__esp-modbus/freemodbus/common/esp_modbus_slave_tcp.c", "D:/ESP-Project/ameter00/managed_components/espressif__esp-modbus/freemodbus/common/esp_modbus_master_serial.c", "D:/ESP-Project/ameter00/managed_components/espressif__esp-modbus/freemodbus/common/esp_modbus_slave_serial.c"], "include_dirs": ["D:/ESP-Project/ameter00/managed_components/espressif__esp-modbus/freemodbus/common/include"]}, "espressif__esp-sr": {"alias": "idf::espressif__esp-sr", "target": "___idf_espressif__esp-sr", "prefix": "idf", "dir": "D:/ESP-Project/ameter00/managed_components/espressif__esp-sr", "type": "LIBRARY", "lib": "__idf_espressif__esp-sr", "reqs": ["json", "spiffs", "esp_partition"], "priv_reqs": ["spi_flash", "espressif__esp-dsp"], "managed_reqs": [], "managed_priv_reqs": ["espressif__esp-dsp"], "file": "D:/ESP-Project/ameter00/build/esp-idf/espressif__esp-sr/libespressif__esp-sr.a", "sources": ["D:/ESP-Project/ameter00/managed_components/espressif__esp-sr/src/model_path.c", "D:/ESP-Project/ameter00/managed_components/espressif__esp-sr/src/esp_mn_speech_commands.c", "D:/ESP-Project/ameter00/managed_components/espressif__esp-sr/src/esp_process_sdkconfig.c"], "include_dirs": ["src/include", "esp-tts/esp_tts_chinese/include", "include/esp32s3"]}, "espressif__esp32-camera": {"alias": "idf::espressif__esp32-camera", "target": "___idf_espressif__esp32-camera", "prefix": "idf", "dir": "D:/ESP-Project/ameter00/components/espressif__esp32-camera", "type": "LIBRARY", "lib": "__idf_espressif__esp32-camera", "reqs": ["driver"], "priv_reqs": ["freertos", "nvs_flash", "esp_timer"], "managed_reqs": [], "managed_priv_reqs": [], "file": "D:/ESP-Project/ameter00/build/esp-idf/espressif__esp32-camera/libespressif__esp32-camera.a", "sources": ["D:/ESP-Project/ameter00/components/espressif__esp32-camera/conversions/yuv.c", "D:/ESP-Project/ameter00/components/espressif__esp32-camera/conversions/to_jpg.cpp", "D:/ESP-Project/ameter00/components/espressif__esp32-camera/conversions/to_bmp.c", "D:/ESP-Project/ameter00/components/espressif__esp32-camera/conversions/jpge.cpp", "D:/ESP-Project/ameter00/components/espressif__esp32-camera/conversions/esp_jpg_decode.c", "D:/ESP-Project/ameter00/components/espressif__esp32-camera/driver/esp_camera.c", "D:/ESP-Project/ameter00/components/espressif__esp32-camera/driver/cam_hal.c", "D:/ESP-Project/ameter00/components/espressif__esp32-camera/driver/sccb.c", "D:/ESP-Project/ameter00/components/espressif__esp32-camera/driver/sensor.c", "D:/ESP-Project/ameter00/components/espressif__esp32-camera/sensors/ov2640.c", "D:/ESP-Project/ameter00/components/espressif__esp32-camera/sensors/ov3660.c", "D:/ESP-Project/ameter00/components/espressif__esp32-camera/sensors/ov5640.c", "D:/ESP-Project/ameter00/components/espressif__esp32-camera/sensors/ov7725.c", "D:/ESP-Project/ameter00/components/espressif__esp32-camera/sensors/ov7670.c", "D:/ESP-Project/ameter00/components/espressif__esp32-camera/sensors/nt99141.c", "D:/ESP-Project/ameter00/components/espressif__esp32-camera/sensors/gc0308.c", "D:/ESP-Project/ameter00/components/espressif__esp32-camera/sensors/gc2145.c", "D:/ESP-Project/ameter00/components/espressif__esp32-camera/sensors/gc032a.c", "D:/ESP-Project/ameter00/components/espressif__esp32-camera/sensors/bf3005.c", "D:/ESP-Project/ameter00/components/espressif__esp32-camera/sensors/bf20a6.c", "D:/ESP-Project/ameter00/components/espressif__esp32-camera/sensors/sc101iot.c", "D:/ESP-Project/ameter00/components/espressif__esp32-camera/sensors/sc030iot.c", "D:/ESP-Project/ameter00/components/espressif__esp32-camera/sensors/sc031gs.c", "D:/ESP-Project/ameter00/components/espressif__esp32-camera/target/esp32s3/ll_cam.c"], "include_dirs": ["driver/include", "conversions/include"]}, "espressif__esp32_s3_eye": {"alias": "idf::espressif__esp32_s3_eye", "target": "___idf_espressif__esp32_s3_eye", "prefix": "idf", "dir": "D:/ESP-Project/ameter00/managed_components/espressif__esp32_s3_eye", "type": "LIBRARY", "lib": "__idf_espressif__esp32_s3_eye", "reqs": ["driver", "spiffs", "espressif__button", "espressif__esp32-camera", "espressif__esp_codec_dev", "espressif__esp_lvgl_port"], "priv_reqs": ["fatfs", "esp_lcd"], "managed_reqs": ["espressif__button", "espressif__esp32-camera", "espressif__esp_codec_dev", "espressif__esp_lvgl_port"], "managed_priv_reqs": [], "file": "D:/ESP-Project/ameter00/build/esp-idf/espressif__esp32_s3_eye/libespressif__esp32_s3_eye.a", "sources": ["D:/ESP-Project/ameter00/managed_components/espressif__esp32_s3_eye/esp32_s3_eye.c", "D:/ESP-Project/ameter00/managed_components/espressif__esp32_s3_eye/esp32_s3_eye_idf5.c"], "include_dirs": ["include"]}, "espressif__esp_codec_dev": {"alias": "idf::espressif__esp_codec_dev", "target": "___idf_espressif__esp_codec_dev", "prefix": "idf", "dir": "D:/ESP-Project/ameter00/components/espressif__esp_codec_dev", "type": "LIBRARY", "lib": "__idf_espressif__esp_codec_dev", "reqs": ["driver"], "priv_reqs": ["freertos"], "managed_reqs": [], "managed_priv_reqs": [], "file": "D:/ESP-Project/ameter00/build/esp-idf/espressif__esp_codec_dev/libespressif__esp_codec_dev.a", "sources": ["D:/ESP-Project/ameter00/components/espressif__esp_codec_dev/esp_codec_dev.c", "D:/ESP-Project/ameter00/components/espressif__esp_codec_dev/esp_codec_dev_vol.c", "D:/ESP-Project/ameter00/components/espressif__esp_codec_dev/esp_codec_dev_if.c", "D:/ESP-Project/ameter00/components/espressif__esp_codec_dev/audio_codec_sw_vol.c", "D:/ESP-Project/ameter00/components/espressif__esp_codec_dev/platform/audio_codec_gpio.c", "D:/ESP-Project/ameter00/components/espressif__esp_codec_dev/platform/audio_codec_ctrl_i2c.c", "D:/ESP-Project/ameter00/components/espressif__esp_codec_dev/platform/audio_codec_data_i2s.c", "D:/ESP-Project/ameter00/components/espressif__esp_codec_dev/platform/audio_codec_ctrl_spi.c", "D:/ESP-Project/ameter00/components/espressif__esp_codec_dev/platform/esp_codec_dev_os.c", "D:/ESP-Project/ameter00/components/espressif__esp_codec_dev/device/es8311/es8311.c", "D:/ESP-Project/ameter00/components/espressif__esp_codec_dev/device/es8156/es8156.c", "D:/ESP-Project/ameter00/components/espressif__esp_codec_dev/device/es7243e/es7243e.c", "D:/ESP-Project/ameter00/components/espressif__esp_codec_dev/device/es7210/es7210.c", "D:/ESP-Project/ameter00/components/espressif__esp_codec_dev/device/es7243/es7243.c", "D:/ESP-Project/ameter00/components/espressif__esp_codec_dev/device/es8388/es8388.c", "D:/ESP-Project/ameter00/components/espressif__esp_codec_dev/device/tas5805m/tas5805m.c", "D:/ESP-Project/ameter00/components/espressif__esp_codec_dev/device/es8374/es8374.c"], "include_dirs": ["include", "interface", "device/include"]}, "espressif__esp_io_expander": {"alias": "idf::espressif__esp_io_expander", "target": "___idf_espressif__esp_io_expander", "prefix": "idf", "dir": "D:/ESP-Project/ameter00/components/espressif__esp_io_expander", "type": "LIBRARY", "lib": "__idf_espressif__esp_io_expander", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "D:/ESP-Project/ameter00/build/esp-idf/espressif__esp_io_expander/libespressif__esp_io_expander.a", "sources": ["D:/ESP-Project/ameter00/components/espressif__esp_io_expander/esp_io_expander.c"], "include_dirs": ["include"]}, "espressif__esp_io_expander_tca95xx_16bit": {"alias": "idf::espressif__esp_io_expander_tca95xx_16bit", "target": "___idf_espressif__esp_io_expander_tca95xx_16bit", "prefix": "idf", "dir": "D:/ESP-Project/ameter00/components/espressif__esp_io_expander_tca95xx_16bit", "type": "LIBRARY", "lib": "__idf_espressif__esp_io_expander_tca95xx_16bit", "reqs": ["driver"], "priv_reqs": ["espressif__esp_io_expander"], "managed_reqs": [], "managed_priv_reqs": ["espressif__esp_io_expander"], "file": "D:/ESP-Project/ameter00/build/esp-idf/espressif__esp_io_expander_tca95xx_16bit/libespressif__esp_io_expander_tca95xx_16bit.a", "sources": ["D:/ESP-Project/ameter00/components/espressif__esp_io_expander_tca95xx_16bit/esp_io_expander_tca95xx_16bit.c"], "include_dirs": ["include"]}, "espressif__esp_lcd_touch": {"alias": "idf::espressif__esp_lcd_touch", "target": "___idf_espressif__esp_lcd_touch", "prefix": "idf", "dir": "D:/ESP-Project/ameter00/components/espressif__esp_lcd_touch", "type": "LIBRARY", "lib": "__idf_espressif__esp_lcd_touch", "reqs": ["driver", "esp_lcd"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "D:/ESP-Project/ameter00/build/esp-idf/espressif__esp_lcd_touch/libespressif__esp_lcd_touch.a", "sources": ["D:/ESP-Project/ameter00/components/espressif__esp_lcd_touch/esp_lcd_touch.c"], "include_dirs": ["include"]}, "espressif__esp_lcd_touch_cst816s": {"alias": "idf::espressif__esp_lcd_touch_cst816s", "target": "___idf_espressif__esp_lcd_touch_cst816s", "prefix": "idf", "dir": "D:/ESP-Project/ameter00/components/espressif__esp_lcd_touch_cst816s", "type": "LIBRARY", "lib": "__idf_espressif__esp_lcd_touch_cst816s", "reqs": ["esp_lcd", "espressif__esp_lcd_touch"], "priv_reqs": [], "managed_reqs": ["espressif__esp_lcd_touch"], "managed_priv_reqs": [], "file": "D:/ESP-Project/ameter00/build/esp-idf/espressif__esp_lcd_touch_cst816s/libespressif__esp_lcd_touch_cst816s.a", "sources": ["D:/ESP-Project/ameter00/components/espressif__esp_lcd_touch_cst816s/esp_lcd_touch_cst816s.c"], "include_dirs": ["include"]}, "espressif__esp_lvgl_port": {"alias": "idf::espressif__esp_lvgl_port", "target": "___idf_espressif__esp_lvgl_port", "prefix": "idf", "dir": "D:/ESP-Project/ameter00/managed_components/espressif__esp_lvgl_port", "type": "CONFIG_ONLY", "lib": "__idf_espressif__esp_lvgl_port", "reqs": ["esp_lcd", "lvgl__lvgl"], "priv_reqs": [], "managed_reqs": ["lvgl__lvgl"], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": ["include"]}, "espressif__mdns": {"alias": "idf::espressif__mdns", "target": "___idf_espressif__mdns", "prefix": "idf", "dir": "D:/ESP-Project/ameter00/managed_components/espressif__mdns", "type": "LIBRARY", "lib": "__idf_espressif__mdns", "reqs": ["lwip", "console", "esp_netif"], "priv_reqs": ["esp_timer", "esp_wifi"], "managed_reqs": [], "managed_priv_reqs": [], "file": "D:/ESP-Project/ameter00/build/esp-idf/espressif__mdns/libespressif__mdns.a", "sources": ["D:/ESP-Project/ameter00/managed_components/espressif__mdns/mdns.c", "D:/ESP-Project/ameter00/managed_components/espressif__mdns/mdns_networking_lwip.c", "D:/ESP-Project/ameter00/managed_components/espressif__mdns/mdns_console.c"], "include_dirs": ["include"]}, "esptool_py": {"alias": "idf::esptool_py", "target": "___idf_esptool_py", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/esptool_py", "type": "CONFIG_ONLY", "lib": "__idf_esptool_py", "reqs": ["bootloader"], "priv_reqs": ["partition_table"], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": []}, "fatfs": {"alias": "idf::fatfs", "target": "___idf_fatfs", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/fatfs", "type": "LIBRARY", "lib": "__idf_fatfs", "reqs": ["wear_levelling", "sdmmc", "esp_driver_sdmmc", "esp_driver_sdspi"], "priv_reqs": ["vfs", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "file": "D:/ESP-Project/ameter00/build/esp-idf/fatfs/libfatfs.a", "sources": ["D:/ESPRESSIF/v5.4.1/esp-idf/components/fatfs/diskio/diskio.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/fatfs/diskio/diskio_rawflash.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/fatfs/diskio/diskio_wl.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/fatfs/src/ff.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/fatfs/src/ffunicode.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/fatfs/port/freertos/ffsystem.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/fatfs/diskio/diskio_sdmmc.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/fatfs/vfs/vfs_fat.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/fatfs/vfs/vfs_fat_sdmmc.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/fatfs/vfs/vfs_fat_spiflash.c"], "include_dirs": ["diskio", "src", "vfs"]}, "fb_gfx": {"alias": "idf::fb_gfx", "target": "___idf_fb_gfx", "prefix": "idf", "dir": "D:/ESP-Project/ameter00/components/fb_gfx", "type": "LIBRARY", "lib": "__idf_fb_gfx", "reqs": ["espressif__esp32-camera"], "priv_reqs": [], "managed_reqs": ["espressif__esp32-camera"], "managed_priv_reqs": [], "file": "D:/ESP-Project/ameter00/build/esp-idf/fb_gfx/libfb_gfx.a", "sources": ["D:/ESP-Project/ameter00/components/fb_gfx/fb_gfx.c"], "include_dirs": [".", "./include"]}, "freertos": {"alias": "idf::freertos", "target": "___idf_freertos", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/freertos", "type": "LIBRARY", "lib": "__idf_freertos", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "D:/ESP-Project/ameter00/build/esp-idf/freertos/libfreertos.a", "sources": ["D:/ESPRESSIF/v5.4.1/esp-idf/components/freertos/heap_idf.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/freertos/app_startup.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/freertos/port_common.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/freertos/port_systick.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/freertos/FreeRTOS-Kernel/list.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/freertos/FreeRTOS-Kernel/queue.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/freertos/FreeRTOS-Kernel/tasks.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/freertos/FreeRTOS-Kernel/timers.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/freertos/FreeRTOS-Kernel/event_groups.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/freertos/FreeRTOS-Kernel/stream_buffer.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/freertos/FreeRTOS-Kernel/portable/xtensa/port.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/freertos/FreeRTOS-Kernel/portable/xtensa/portasm.S", "D:/ESPRESSIF/v5.4.1/esp-idf/components/freertos/FreeRTOS-Kernel/portable/xtensa/xtensa_init.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/freertos/FreeRTOS-Kernel/portable/xtensa/xtensa_overlay_os_hook.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/freertos/esp_additions/freertos_compatibility.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/freertos/esp_additions/idf_additions_event_groups.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/freertos/esp_additions/idf_additions.c"], "include_dirs": ["config/include", "config/include/freertos", "config/xtensa/include", "FreeRTOS-Kernel/include", "FreeRTOS-Kernel/portable/xtensa/include", "FreeRTOS-Kernel/portable/xtensa/include/freertos", "esp_additions/include"]}, "fusion_ameter_s3": {"alias": "idf::fusion_ameter_s3", "target": "___idf_fusion_ameter_s3", "prefix": "idf", "dir": "D:/ESP-Project/ameter00/components/fusion_ameter_s3", "type": "LIBRARY", "lib": "__idf_fusion_ameter_s3", "reqs": ["driver", "spiffs", "espressif__button", "espressif__esp32-camera", "espressif__esp_codec_dev", "espressif__esp_lvgl_port"], "priv_reqs": ["fatfs", "esp_lcd", "espressif__esp_io_expander", "espressif__esp_io_expander_tca95xx_16bit", "espressif__esp_lcd_touch_cst816s"], "managed_reqs": ["espressif__button", "espressif__esp32-camera", "espressif__esp_codec_dev", "espressif__esp_lvgl_port"], "managed_priv_reqs": ["espressif__esp_io_expander", "espressif__esp_io_expander_tca95xx_16bit", "espressif__esp_lcd_touch_cst816s"], "file": "D:/ESP-Project/ameter00/build/esp-idf/fusion_ameter_s3/libfusion_ameter_s3.a", "sources": ["D:/ESP-Project/ameter00/components/fusion_ameter_s3/ameter_s3.c", "D:/ESP-Project/ameter00/components/fusion_ameter_s3/ameter_s3_idf5.c"], "include_dirs": ["include"]}, "hal": {"alias": "idf::hal", "target": "___idf_hal", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/hal", "type": "LIBRARY", "lib": "__idf_hal", "reqs": ["soc", "esp_rom"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "D:/ESP-Project/ameter00/build/esp-idf/hal/libhal.a", "sources": ["D:/ESPRESSIF/v5.4.1/esp-idf/components/hal/hal_utils.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/hal/mpu_hal.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/hal/efuse_hal.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/hal/esp32s3/efuse_hal.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/hal/mmu_hal.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/hal/cache_hal.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/hal/color_hal.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/hal/spi_flash_hal.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/hal/spi_flash_hal_iram.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/hal/spi_flash_encrypt_hal_iram.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/hal/esp32s3/clk_tree_hal.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/hal/systimer_hal.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/hal/uart_hal.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/hal/uart_hal_iram.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/hal/gpio_hal.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/hal/rtc_io_hal.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/hal/timer_hal.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/hal/ledc_hal.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/hal/ledc_hal_iram.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/hal/i2c_hal.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/hal/i2c_hal_iram.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/hal/rmt_hal.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/hal/pcnt_hal.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/hal/mcpwm_hal.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/hal/twai_hal.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/hal/twai_hal_iram.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/hal/gdma_hal_top.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/hal/gdma_hal_ahb_v1.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/hal/i2s_hal.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/hal/sdm_hal.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/hal/sdmmc_hal.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/hal/adc_hal_common.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/hal/adc_oneshot_hal.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/hal/adc_hal.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/hal/lcd_hal.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/hal/mpi_hal.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/hal/sha_hal.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/hal/aes_hal.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/hal/brownout_hal.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/hal/spi_hal.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/hal/spi_hal_iram.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/hal/spi_slave_hal.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/hal/spi_slave_hal_iram.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/hal/spi_slave_hd_hal.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/hal/spi_flash_hal_gpspi.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/hal/hmac_hal.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/hal/ds_hal.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/hal/usb_serial_jtag_hal.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/hal/usb_dwc_hal.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/hal/usb_wrap_hal.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/hal/esp32s3/touch_sensor_hal.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/hal/touch_sensor_hal.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/hal/xt_wdt_hal.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/hal/esp32s3/rtc_cntl_hal.c"], "include_dirs": ["platform_port/include", "esp32s3/include", "include"]}, "heap": {"alias": "idf::heap", "target": "___idf_heap", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/heap", "type": "LIBRARY", "lib": "__idf_heap", "reqs": [], "priv_reqs": ["soc"], "managed_reqs": [], "managed_priv_reqs": [], "file": "D:/ESP-Project/ameter00/build/esp-idf/heap/libheap.a", "sources": ["D:/ESPRESSIF/v5.4.1/esp-idf/components/heap/heap_caps_base.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/heap/heap_caps.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/heap/heap_caps_init.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/heap/multi_heap.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/heap/tlsf/tlsf.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/heap/port/memory_layout_utils.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/heap/port/esp32s3/memory_layout.c"], "include_dirs": ["include", "tlsf"]}, "http_parser": {"alias": "idf::http_parser", "target": "___idf_http_parser", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/http_parser", "type": "LIBRARY", "lib": "__idf_http_parser", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "D:/ESP-Project/ameter00/build/esp-idf/http_parser/libhttp_parser.a", "sources": ["D:/ESPRESSIF/v5.4.1/esp-idf/components/http_parser/http_parser.c"], "include_dirs": ["."]}, "i2cdev": {"alias": "idf::i2cdev", "target": "___idf_i2cdev", "prefix": "idf", "dir": "D:/ESP-Project/ameter00/components/i2cdev", "type": "LIBRARY", "lib": "__idf_i2cdev", "reqs": ["driver", "freertos"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "D:/ESP-Project/ameter00/build/esp-idf/i2cdev/libi2cdev.a", "sources": ["D:/ESP-Project/ameter00/components/i2cdev/i2cdev.c"], "include_dirs": ["."]}, "idf_test": {"alias": "idf::idf_test", "target": "___idf_idf_test", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/idf_test", "type": "CONFIG_ONLY", "lib": "__idf_idf_test", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": ["include", "include/esp32s3"]}, "ieee802154": {"alias": "idf::ieee802154", "target": "___idf_ieee802154", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/ieee802154", "type": "CONFIG_ONLY", "lib": "__idf_ieee802154", "reqs": ["esp_coex"], "priv_reqs": ["esp_phy", "driver", "esp_timer", "soc", "hal"], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": ["include"]}, "json": {"alias": "idf::json", "target": "___idf_json", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/json", "type": "LIBRARY", "lib": "__idf_json", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "D:/ESP-Project/ameter00/build/esp-idf/json/libjson.a", "sources": ["D:/ESPRESSIF/v5.4.1/esp-idf/components/json/cJSON/cJSON.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/json/cJSON/cJSON_Utils.c"], "include_dirs": ["cJSON"]}, "log": {"alias": "idf::log", "target": "___idf_log", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/log", "type": "LIBRARY", "lib": "__idf_log", "reqs": [], "priv_reqs": ["soc", "hal", "esp_hw_support"], "managed_reqs": [], "managed_priv_reqs": [], "file": "D:/ESP-Project/ameter00/build/esp-idf/log/liblog.a", "sources": ["D:/ESPRESSIF/v5.4.1/esp-idf/components/log/src/os/log_timestamp.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/log/src/log_timestamp_common.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/log/src/os/log_lock.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/log/src/os/log_write.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/log/src/buffer/log_buffers.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/log/src/util.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/log/src/log_level/log_level.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/log/src/log_level/tag_log_level/tag_log_level.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/log/src/log_level/tag_log_level/linked_list/log_linked_list.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/log/src/log_level/tag_log_level/cache/log_binary_heap.c"], "include_dirs": ["include"]}, "lvgl__lvgl": {"alias": "idf::lvgl__lvgl", "target": "___idf_lvgl__lvgl", "prefix": "idf", "dir": "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl", "type": "LIBRARY", "lib": "__idf_lvgl__lvgl", "reqs": ["esp_timer"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "D:/ESP-Project/ameter00/build/esp-idf/lvgl__lvgl/liblvgl__lvgl.a", "sources": ["D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/core/lv_disp.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/core/lv_event.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/core/lv_group.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/core/lv_indev.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/core/lv_indev_scroll.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/core/lv_obj.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/core/lv_obj_class.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/core/lv_obj_draw.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/core/lv_obj_pos.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/core/lv_obj_scroll.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/core/lv_obj_style.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/core/lv_obj_style_gen.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/core/lv_obj_tree.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/core/lv_refr.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/core/lv_theme.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/draw/arm2d/lv_gpu_arm2d.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/draw/lv_draw.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/draw/lv_draw_arc.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/draw/lv_draw_img.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/draw/lv_draw_label.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/draw/lv_draw_layer.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/draw/lv_draw_line.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/draw/lv_draw_mask.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/draw/lv_draw_rect.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/draw/lv_draw_transform.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/draw/lv_draw_triangle.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/draw/lv_img_buf.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/draw/lv_img_cache.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/draw/lv_img_decoder.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/draw/nxp/pxp/lv_draw_pxp.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/draw/nxp/pxp/lv_draw_pxp_blend.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/draw/nxp/pxp/lv_gpu_nxp_pxp.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/draw/nxp/pxp/lv_gpu_nxp_pxp_osa.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/draw/nxp/vglite/lv_draw_vglite.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/draw/nxp/vglite/lv_draw_vglite_arc.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/draw/nxp/vglite/lv_draw_vglite_blend.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/draw/nxp/vglite/lv_draw_vglite_line.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/draw/nxp/vglite/lv_draw_vglite_rect.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/draw/nxp/vglite/lv_vglite_buf.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/draw/nxp/vglite/lv_vglite_utils.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/draw/renesas/lv_gpu_d2_draw_label.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/draw/renesas/lv_gpu_d2_ra6m3.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/draw/sdl/lv_draw_sdl.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/draw/sdl/lv_draw_sdl_arc.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/draw/sdl/lv_draw_sdl_bg.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/draw/sdl/lv_draw_sdl_composite.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/draw/sdl/lv_draw_sdl_img.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/draw/sdl/lv_draw_sdl_label.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/draw/sdl/lv_draw_sdl_layer.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/draw/sdl/lv_draw_sdl_line.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/draw/sdl/lv_draw_sdl_mask.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/draw/sdl/lv_draw_sdl_polygon.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/draw/sdl/lv_draw_sdl_rect.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/draw/sdl/lv_draw_sdl_stack_blur.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/draw/sdl/lv_draw_sdl_texture_cache.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/draw/sdl/lv_draw_sdl_utils.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/draw/stm32_dma2d/lv_gpu_stm32_dma2d.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/draw/sw/lv_draw_sw.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/draw/sw/lv_draw_sw_arc.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/draw/sw/lv_draw_sw_blend.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/draw/sw/lv_draw_sw_dither.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/draw/sw/lv_draw_sw_gradient.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/draw/sw/lv_draw_sw_img.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/draw/sw/lv_draw_sw_layer.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/draw/sw/lv_draw_sw_letter.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/draw/sw/lv_draw_sw_line.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/draw/sw/lv_draw_sw_polygon.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/draw/sw/lv_draw_sw_rect.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/draw/sw/lv_draw_sw_transform.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/draw/swm341_dma2d/lv_gpu_swm341_dma2d.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/extra/layouts/flex/lv_flex.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/extra/layouts/grid/lv_grid.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/extra/libs/bmp/lv_bmp.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/extra/libs/ffmpeg/lv_ffmpeg.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/extra/libs/freetype/lv_freetype.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/extra/libs/fsdrv/lv_fs_fatfs.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/extra/libs/fsdrv/lv_fs_littlefs.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/extra/libs/fsdrv/lv_fs_posix.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/extra/libs/fsdrv/lv_fs_stdio.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/extra/libs/fsdrv/lv_fs_win32.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/extra/libs/gif/gifdec.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/extra/libs/gif/lv_gif.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/extra/libs/png/lodepng.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/extra/libs/png/lv_png.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/extra/libs/qrcode/lv_qrcode.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/extra/libs/qrcode/qrcodegen.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/extra/libs/rlottie/lv_rlottie.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/extra/libs/sjpg/lv_sjpg.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/extra/libs/sjpg/tjpgd.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/extra/libs/tiny_ttf/lv_tiny_ttf.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/extra/lv_extra.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/extra/others/fragment/lv_fragment.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/extra/others/fragment/lv_fragment_manager.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/extra/others/gridnav/lv_gridnav.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/extra/others/ime/lv_ime_pinyin.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/extra/others/imgfont/lv_imgfont.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/extra/others/monkey/lv_monkey.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/extra/others/msg/lv_msg.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/extra/others/snapshot/lv_snapshot.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/extra/themes/basic/lv_theme_basic.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/extra/themes/default/lv_theme_default.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/extra/themes/mono/lv_theme_mono.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/extra/widgets/animimg/lv_animimg.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/extra/widgets/calendar/lv_calendar.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/extra/widgets/calendar/lv_calendar_header_arrow.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/extra/widgets/calendar/lv_calendar_header_dropdown.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/extra/widgets/chart/lv_chart.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/extra/widgets/colorwheel/lv_colorwheel.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/extra/widgets/imgbtn/lv_imgbtn.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/extra/widgets/keyboard/lv_keyboard.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/extra/widgets/led/lv_led.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/extra/widgets/list/lv_list.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/extra/widgets/menu/lv_menu.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/extra/widgets/meter/lv_meter.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/extra/widgets/msgbox/lv_msgbox.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/extra/widgets/span/lv_span.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/extra/widgets/spinbox/lv_spinbox.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/extra/widgets/spinner/lv_spinner.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/extra/widgets/tabview/lv_tabview.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/extra/widgets/tileview/lv_tileview.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/extra/widgets/win/lv_win.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/font/lv_font.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/font/lv_font_dejavu_16_persian_hebrew.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/font/lv_font_fmt_txt.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/font/lv_font_loader.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/font/lv_font_montserrat_10.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/font/lv_font_montserrat_12.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/font/lv_font_montserrat_12_subpx.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/font/lv_font_montserrat_14.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/font/lv_font_montserrat_16.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/font/lv_font_montserrat_18.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/font/lv_font_montserrat_20.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/font/lv_font_montserrat_22.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/font/lv_font_montserrat_24.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/font/lv_font_montserrat_26.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/font/lv_font_montserrat_28.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/font/lv_font_montserrat_28_compressed.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/font/lv_font_montserrat_30.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/font/lv_font_montserrat_32.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/font/lv_font_montserrat_34.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/font/lv_font_montserrat_36.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/font/lv_font_montserrat_38.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/font/lv_font_montserrat_40.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/font/lv_font_montserrat_42.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/font/lv_font_montserrat_44.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/font/lv_font_montserrat_46.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/font/lv_font_montserrat_48.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/font/lv_font_montserrat_8.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/font/lv_font_simsun_16_cjk.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/font/lv_font_unscii_16.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/font/lv_font_unscii_8.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/hal/lv_hal_disp.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/hal/lv_hal_indev.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/hal/lv_hal_tick.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/misc/lv_anim.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/misc/lv_anim_timeline.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/misc/lv_area.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/misc/lv_async.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/misc/lv_bidi.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/misc/lv_color.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/misc/lv_fs.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/misc/lv_gc.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/misc/lv_ll.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/misc/lv_log.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/misc/lv_lru.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/misc/lv_math.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/misc/lv_mem.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/misc/lv_printf.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/misc/lv_style.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/misc/lv_style_gen.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/misc/lv_templ.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/misc/lv_timer.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/misc/lv_tlsf.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/misc/lv_txt.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/misc/lv_txt_ap.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/misc/lv_utils.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/widgets/lv_arc.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/widgets/lv_bar.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/widgets/lv_btn.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/widgets/lv_btnmatrix.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/widgets/lv_canvas.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/widgets/lv_checkbox.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/widgets/lv_dropdown.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/widgets/lv_img.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/widgets/lv_label.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/widgets/lv_line.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/widgets/lv_objx_templ.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/widgets/lv_roller.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/widgets/lv_slider.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/widgets/lv_switch.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/widgets/lv_table.c", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src/widgets/lv_textarea.c"], "include_dirs": ["D:/ESP-Project/ameter00/managed_components/lvgl__lvgl", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/../", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/examples", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/demos"]}, "lwip": {"alias": "idf::lwip", "target": "___idf_lwip", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/lwip", "type": "LIBRARY", "lib": "__idf_lwip", "reqs": [], "priv_reqs": ["vfs"], "managed_reqs": [], "managed_priv_reqs": [], "file": "D:/ESP-Project/ameter00/build/esp-idf/lwip/liblwip.a", "sources": ["D:/ESPRESSIF/v5.4.1/esp-idf/components/lwip/apps/sntp/sntp.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/lwip/lwip/src/api/api_lib.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/lwip/lwip/src/api/api_msg.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/lwip/lwip/src/api/err.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/lwip/lwip/src/api/if_api.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/lwip/lwip/src/api/netbuf.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/lwip/lwip/src/api/netdb.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/lwip/lwip/src/api/netifapi.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/lwip/lwip/src/api/sockets.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/lwip/lwip/src/api/tcpip.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/lwip/lwip/src/apps/sntp/sntp.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/lwip/lwip/src/apps/netbiosns/netbiosns.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/lwip/lwip/src/core/def.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/lwip/lwip/src/core/dns.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/lwip/lwip/src/core/inet_chksum.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/lwip/lwip/src/core/init.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/lwip/lwip/src/core/ip.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/lwip/lwip/src/core/mem.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/lwip/lwip/src/core/memp.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/lwip/lwip/src/core/netif.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/lwip/lwip/src/core/pbuf.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/lwip/lwip/src/core/raw.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/lwip/lwip/src/core/stats.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/lwip/lwip/src/core/sys.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/lwip/lwip/src/core/tcp.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/lwip/lwip/src/core/tcp_in.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/lwip/lwip/src/core/tcp_out.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/lwip/lwip/src/core/timeouts.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/lwip/lwip/src/core/udp.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/lwip/lwip/src/core/ipv4/autoip.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/lwip/lwip/src/core/ipv4/dhcp.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/lwip/lwip/src/core/ipv4/etharp.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/lwip/lwip/src/core/ipv4/icmp.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/lwip/lwip/src/core/ipv4/igmp.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/lwip/lwip/src/core/ipv4/ip4.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/lwip/lwip/src/core/ipv4/ip4_napt.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/lwip/lwip/src/core/ipv4/ip4_addr.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/lwip/lwip/src/core/ipv4/ip4_frag.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/lwip/lwip/src/core/ipv6/dhcp6.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/lwip/lwip/src/core/ipv6/ethip6.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/lwip/lwip/src/core/ipv6/icmp6.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/lwip/lwip/src/core/ipv6/inet6.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/lwip/lwip/src/core/ipv6/ip6.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/lwip/lwip/src/core/ipv6/ip6_addr.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/lwip/lwip/src/core/ipv6/ip6_frag.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/lwip/lwip/src/core/ipv6/mld6.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/lwip/lwip/src/core/ipv6/nd6.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/lwip/lwip/src/netif/ethernet.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/lwip/lwip/src/netif/bridgeif.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/lwip/lwip/src/netif/bridgeif_fdb.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/lwip/lwip/src/netif/slipif.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/lwip/lwip/src/netif/ppp/auth.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/lwip/lwip/src/netif/ppp/ccp.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/lwip/lwip/src/netif/ppp/chap-md5.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/lwip/lwip/src/netif/ppp/chap-new.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/lwip/lwip/src/netif/ppp/chap_ms.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/lwip/lwip/src/netif/ppp/demand.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/lwip/lwip/src/netif/ppp/eap.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/lwip/lwip/src/netif/ppp/ecp.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/lwip/lwip/src/netif/ppp/eui64.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/lwip/lwip/src/netif/ppp/fsm.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/lwip/lwip/src/netif/ppp/ipcp.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/lwip/lwip/src/netif/ppp/ipv6cp.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/lwip/lwip/src/netif/ppp/lcp.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/lwip/lwip/src/netif/ppp/magic.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/lwip/lwip/src/netif/ppp/mppe.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/lwip/lwip/src/netif/ppp/multilink.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/lwip/lwip/src/netif/ppp/ppp.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/lwip/lwip/src/netif/ppp/pppapi.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/lwip/lwip/src/netif/ppp/pppcrypt.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/lwip/lwip/src/netif/ppp/pppoe.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/lwip/lwip/src/netif/ppp/pppol2tp.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/lwip/lwip/src/netif/ppp/pppos.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/lwip/lwip/src/netif/ppp/upap.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/lwip/lwip/src/netif/ppp/utils.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/lwip/lwip/src/netif/ppp/vj.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/lwip/port/hooks/tcp_isn_default.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/lwip/port/hooks/lwip_default_hooks.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/lwip/port/debug/lwip_debug.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/lwip/port/sockets_ext.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/lwip/port/freertos/sys_arch.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/lwip/port/acd_dhcp_check.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/lwip/port/esp32xx/vfs_lwip.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/lwip/apps/ping/esp_ping.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/lwip/apps/ping/ping.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/lwip/apps/ping/ping_sock.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/lwip/lwip/src/netif/ppp/polarssl/arc4.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/lwip/lwip/src/netif/ppp/polarssl/des.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/lwip/lwip/src/netif/ppp/polarssl/md4.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/lwip/lwip/src/netif/ppp/polarssl/md5.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/lwip/lwip/src/netif/ppp/polarssl/sha1.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/lwip/apps/dhcpserver/dhcpserver.c"], "include_dirs": ["include", "include/apps", "include/apps/sntp", "lwip/src/include", "port/include", "port/freertos/include/", "port/esp32xx/include", "port/esp32xx/include/arch", "port/esp32xx/include/sys"]}, "main": {"alias": "idf::main", "target": "___idf_main", "prefix": "idf", "dir": "D:/ESP-Project/ameter00/main", "type": "LIBRARY", "lib": "__idf_main", "reqs": [], "priv_reqs": ["espressif__esp_lcd_touch_cst816s", "fusion_ameter_s3", "fb_gfx", "pcf8563", "modules", "bus", "esp-dl", "espressif__esp-modbus", "espressif__esp-sr", "espressif__esp32_s3_eye", "espressif__esp_io_expander", "espressif__esp_io_expander_tca95xx_16bit", "espressif__esp_lvgl_port", "lvgl__lvgl"], "managed_reqs": [], "managed_priv_reqs": ["esp-dl", "espressif__esp-modbus", "espressif__esp-sr", "espressif__esp32_s3_eye", "espressif__esp_io_expander", "espressif__esp_io_expander_tca95xx_16bit", "espressif__esp_lcd_touch_cst816s", "espressif__esp_lvgl_port", "lvgl__lvgl"], "file": "D:/ESP-Project/ameter00/build/esp-idf/main/libmain.a", "sources": ["D:/ESP-Project/ameter00/main/app_main.cpp", "D:/ESP-Project/ameter00/main/mb_slave.c", "D:/ESP-Project/ameter00/main/mb_master.c", "D:/ESP-Project/ameter00/main/bit_banging.c", "D:/ESP-Project/ameter00/main/src/memory_manager.c", "D:/ESP-Project/ameter00/main/src/system_monitor.c", "D:/ESP-Project/ameter00/main/src/error_handler.c", "D:/ESP-Project/ameter00/main/src/camera_power_manager.c", "D:/ESP-Project/ameter00/main/src/cpu_monitor.c", "D:/ESP-Project/ameter00/main/src/lvgl_monitor.c", "D:/ESP-Project/ameter00/main/src/screen_monitor.c", "D:/ESP-Project/ameter00/main/src/app_camera.cpp", "D:/ESP-Project/ameter00/main/src/app_face.cpp", "D:/ESP-Project/ameter00/main/ui/components/ui_comp.c", "D:/ESP-Project/ameter00/main/ui/components/ui_comp_face_id_pl.c", "D:/ESP-Project/ameter00/main/ui/components/ui_comp_hook.c", "D:/ESP-Project/ameter00/main/ui/fonts/ui_font_WD18.c", "D:/ESP-Project/ameter00/main/ui/fonts/ui_font_WD_28.c", "D:/ESP-Project/ameter00/main/ui/fonts/ui_font_WD_48.c", "D:/ESP-Project/ameter00/main/ui/images/ui_img_1367759103.c", "D:/ESP-Project/ameter00/main/ui/images/ui_img_137911575.c", "D:/ESP-Project/ameter00/main/ui/images/ui_img_2141690491.c", "D:/ESP-Project/ameter00/main/ui/images/ui_img_222780303.c", "D:/ESP-Project/ameter00/main/ui/images/ui_img_b01_20_png.c", "D:/ESP-Project/ameter00/main/ui/images/ui_img_b01_png.c", "D:/ESP-Project/ameter00/main/ui/images/ui_img_b02_10_png.c", "D:/ESP-Project/ameter00/main/ui/images/ui_img_b03_30_png.c", "D:/ESP-Project/ameter00/main/ui/images/ui_img_b04_30_png.c", "D:/ESP-Project/ameter00/main/ui/images/ui_img_b05_30_png.c", "D:/ESP-Project/ameter00/main/ui/images/ui_img_b06_30_png.c", "D:/ESP-Project/ameter00/main/ui/images/ui_img_b07_30_png.c", "D:/ESP-Project/ameter00/main/ui/images/ui_img_b10_14050_png.c", "D:/ESP-Project/ameter00/main/ui/images/ui_img_bu25_png.c", "D:/ESP-Project/ameter00/main/ui/images/ui_img_fusiontech_png.c", "D:/ESP-Project/ameter00/main/ui/images/ui_img_i17_90_png.c", "D:/ESP-Project/ameter00/main/ui/images/ui_img_i19_250120_png.c", "D:/ESP-Project/ameter00/main/ui/images/ui_img_i19_png.c", "D:/ESP-Project/ameter00/main/ui/images/ui_img_i21_90_png.c", "D:/ESP-Project/ameter00/main/ui/images/ui_img_lift_arrow_1_png.c", "D:/ESP-Project/ameter00/main/ui/images/ui_img_lift_arrow_png.c", "D:/ESP-Project/ameter00/main/ui/images/ui_img_load_110_nopin_png.c", "D:/ESP-Project/ameter00/main/ui/images/ui_img_load_110_png.c", "D:/ESP-Project/ameter00/main/ui/images/ui_img_load_44_png.c", "D:/ESP-Project/ameter00/main/ui/images/ui_img_load_80_nopin_png.c", "D:/ESP-Project/ameter00/main/ui/images/ui_img_load_80_pin_png.c", "D:/ESP-Project/ameter00/main/ui/images/ui_img_load_80_png.c", "D:/ESP-Project/ameter00/main/ui/images/ui_img_logo_png.c", "D:/ESP-Project/ameter00/main/ui/images/ui_img_p1_png.c", "D:/ESP-Project/ameter00/main/ui/images/ui_img_p2_png.c", "D:/ESP-Project/ameter00/main/ui/images/ui_img_p3_png.c", "D:/ESP-Project/ameter00/main/ui/images/ui_img_p4_png.c", "D:/ESP-Project/ameter00/main/ui/images/ui_img_right_arrow_1_png.c", "D:/ESP-Project/ameter00/main/ui/images/ui_img_right_arrow_png.c", "D:/ESP-Project/ameter00/main/ui/images/ui_img_rpm_110_pin_png.c", "D:/ESP-Project/ameter00/main/ui/images/ui_img_rpm_110_png.c", "D:/ESP-Project/ameter00/main/ui/images/ui_img_rpm_44_png.c", "D:/ESP-Project/ameter00/main/ui/images/ui_img_rpm_80_pin_png.c", "D:/ESP-Project/ameter00/main/ui/images/ui_img_rpm_80_png.c", "D:/ESP-Project/ameter00/main/ui/images/ui_img_tool1_110_png.c", "D:/ESP-Project/ameter00/main/ui/images/ui_img_tool1_44_png.c", "D:/ESP-Project/ameter00/main/ui/images/ui_img_tool1_80_png.c", "D:/ESP-Project/ameter00/main/ui/images/ui_img_tool2_80_png.c", "D:/ESP-Project/ameter00/main/ui/images/ui_img_user_140_png.c", "D:/ESP-Project/ameter00/main/ui/images/ui_temporary_image.c", "D:/ESP-Project/ameter00/main/ui/screens/ui_SC_1_1.c", "D:/ESP-Project/ameter00/main/ui/screens/ui_SC_1_2.c", "D:/ESP-Project/ameter00/main/ui/screens/ui_SC_2_1.c", "D:/ESP-Project/ameter00/main/ui/screens/ui_SC_2_2.c", "D:/ESP-Project/ameter00/main/ui/screens/ui_SC_2_3.c", "D:/ESP-Project/ameter00/main/ui/screens/ui_SC_2_4.c", "D:/ESP-Project/ameter00/main/ui/screens/ui_SC_2_5.c", "D:/ESP-Project/ameter00/main/ui/screens/ui_SC_2_6.c", "D:/ESP-Project/ameter00/main/ui/screens/ui_SC_3_1.c", "D:/ESP-Project/ameter00/main/ui/screens/ui_SC_3_2.c", "D:/ESP-Project/ameter00/main/ui/screens/ui_SC_3_3.c", "D:/ESP-Project/ameter00/main/ui/screens/ui_SC_3_4.c", "D:/ESP-Project/ameter00/main/ui/screens/ui_SC_4_1.c", "D:/ESP-Project/ameter00/main/ui/screens/ui_SC_4_2.c", "D:/ESP-Project/ameter00/main/ui/screens/ui_SC_5_1.c", "D:/ESP-Project/ameter00/main/ui/screens/ui_SC_6_1.c", "D:/ESP-Project/ameter00/main/ui/screens/ui_SC_Face_Login.c", "D:/ESP-Project/ameter00/main/ui/screens/ui_SC_Face_Rec.c", "D:/ESP-Project/ameter00/main/ui/screens/ui_SC_ID_EDIT.c", "D:/ESP-Project/ameter00/main/ui/screens/ui_SC_Key_input.c", "D:/ESP-Project/ameter00/main/ui/screens/ui_SC_LOGO.c", "D:/ESP-Project/ameter00/main/ui/screens/ui_SC_ModBus_set.c", "D:/ESP-Project/ameter00/main/ui/screens/ui_SC_Time_set.c", "D:/ESP-Project/ameter00/main/ui/screens/ui_SC_first_login.c", "D:/ESP-Project/ameter00/main/ui/screens/ui_SC_set_ModBus_ID.c", "D:/ESP-Project/ameter00/main/ui/screens/ui_SC_set_PWD.c", "D:/ESP-Project/ameter00/main/ui/ui.c", "D:/ESP-Project/ameter00/main/ui/ui_helpers.c"], "include_dirs": [".", "ui", "include", "."]}, "mbedtls": {"alias": "idf::mbedtls", "target": "___idf_mbedtls", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/mbedtls", "type": "LIBRARY", "lib": "__idf_mbedtls", "reqs": [], "priv_reqs": ["soc", "esp_hw_support", "esp_pm"], "managed_reqs": [], "managed_priv_reqs": [], "file": "D:/ESP-Project/ameter00/build/esp-idf/mbedtls/libmbedtls.a", "sources": ["D:/ESPRESSIF/v5.4.1/esp-idf/components/mbedtls/esp_crt_bundle/esp_crt_bundle.c", "D:/ESP-Project/ameter00/build/x509_crt_bundle.S"], "include_dirs": ["port/include", "mbedtls/include", "mbedtls/library", "esp_crt_bundle/include"]}, "modules": {"alias": "idf::modules", "target": "___idf_modules", "prefix": "idf", "dir": "D:/ESP-Project/ameter00/components/modules", "type": "LIBRARY", "lib": "__idf_modules", "reqs": ["bus", "esp-dl", "esp_http_server", "nvs_flash", "esp_adc", "esp_lcd", "esp_timer", "esp_wifi", "fb_gfx", "espressif__esp32-camera", "espressif__mdns"], "priv_reqs": [], "managed_reqs": ["espressif__esp32-camera", "espressif__mdns"], "managed_priv_reqs": [], "file": "D:/ESP-Project/ameter00/build/esp-idf/modules/libmodules.a", "sources": ["D:/ESP-Project/ameter00/components/modules/ai/who_ai_utils.cpp", "D:/ESP-Project/ameter00/components/modules/ai/who_cat_face_detection.cpp", "D:/ESP-Project/ameter00/components/modules/ai/who_color_detection.cpp", "D:/ESP-Project/ameter00/components/modules/ai/who_human_face_detection.cpp", "D:/ESP-Project/ameter00/components/modules/ai/who_human_face_recognition.cpp", "D:/ESP-Project/ameter00/components/modules/ai/who_motion_detection.cpp", "D:/ESP-Project/ameter00/components/modules/camera/who_camera.c", "D:/ESP-Project/ameter00/components/modules/lcd/who_lcd.c", "D:/ESP-Project/ameter00/components/modules/led/who_led.c", "D:/ESP-Project/ameter00/components/modules/button/who_adc_button.c", "D:/ESP-Project/ameter00/components/modules/button/who_button.c", "D:/ESP-Project/ameter00/components/modules/web/app_httpd.cpp", "D:/ESP-Project/ameter00/components/modules/web/app_mdns.c", "D:/ESP-Project/ameter00/components/modules/web/app_wifi.c", "D:/ESP-Project/ameter00/components/modules/trace/who_trace.c", "D:/ESP-Project/ameter00/components/modules/imu/qma7981.c", "D:/ESP-Project/ameter00/build/index_ov2640.html.gz.S", "D:/ESP-Project/ameter00/build/index_ov3660.html.gz.S", "D:/ESP-Project/ameter00/build/index_ov5640.html.gz.S", "D:/ESP-Project/ameter00/build/monitor.html.gz.S"], "include_dirs": ["ai", "camera", "lcd", "led", "button", "web", "trace", "imu"]}, "mqtt": {"alias": "idf::mqtt", "target": "___idf_mqtt", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/mqtt", "type": "LIBRARY", "lib": "__idf_mqtt", "reqs": ["esp_event", "tcp_transport"], "priv_reqs": ["esp_timer", "http_parser", "esp_hw_support", "heap"], "managed_reqs": [], "managed_priv_reqs": [], "file": "D:/ESP-Project/ameter00/build/esp-idf/mqtt/libmqtt.a", "sources": ["D:/ESPRESSIF/v5.4.1/esp-idf/components/mqtt/esp-mqtt/mqtt_client.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/mqtt/esp-mqtt/lib/mqtt_msg.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/mqtt/esp-mqtt/lib/mqtt_outbox.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/mqtt/esp-mqtt/lib/platform_esp32_idf.c"], "include_dirs": ["D:/ESPRESSIF/v5.4.1/esp-idf/components/mqtt/esp-mqtt/include"]}, "newlib": {"alias": "idf::newlib", "target": "___idf_newlib", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/newlib", "type": "LIBRARY", "lib": "__idf_newlib", "reqs": [], "priv_reqs": ["soc", "spi_flash"], "managed_reqs": [], "managed_priv_reqs": [], "file": "D:/ESP-Project/ameter00/build/esp-idf/newlib/libnewlib.a", "sources": ["D:/ESPRESSIF/v5.4.1/esp-idf/components/newlib/abort.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/newlib/assert.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/newlib/heap.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/newlib/flockfile.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/newlib/locks.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/newlib/poll.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/newlib/pthread.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/newlib/random.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/newlib/getentropy.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/newlib/reent_init.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/newlib/newlib_init.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/newlib/syscalls.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/newlib/termios.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/newlib/stdatomic.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/newlib/time.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/newlib/sysconf.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/newlib/realpath.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/newlib/scandir.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/newlib/port/xtensa/stdatomic_s32c1i.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/newlib/port/esp_time_impl.c"], "include_dirs": ["platform_include"]}, "nvs_flash": {"alias": "idf::nvs_flash", "target": "___idf_nvs_flash", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/nvs_flash", "type": "LIBRARY", "lib": "__idf_nvs_flash", "reqs": ["esp_partition"], "priv_reqs": ["spi_flash", "newlib"], "managed_reqs": [], "managed_priv_reqs": [], "file": "D:/ESP-Project/ameter00/build/esp-idf/nvs_flash/libnvs_flash.a", "sources": ["D:/ESPRESSIF/v5.4.1/esp-idf/components/nvs_flash/src/nvs_api.cpp", "D:/ESPRESSIF/v5.4.1/esp-idf/components/nvs_flash/src/nvs_cxx_api.cpp", "D:/ESPRESSIF/v5.4.1/esp-idf/components/nvs_flash/src/nvs_item_hash_list.cpp", "D:/ESPRESSIF/v5.4.1/esp-idf/components/nvs_flash/src/nvs_page.cpp", "D:/ESPRESSIF/v5.4.1/esp-idf/components/nvs_flash/src/nvs_pagemanager.cpp", "D:/ESPRESSIF/v5.4.1/esp-idf/components/nvs_flash/src/nvs_storage.cpp", "D:/ESPRESSIF/v5.4.1/esp-idf/components/nvs_flash/src/nvs_handle_simple.cpp", "D:/ESPRESSIF/v5.4.1/esp-idf/components/nvs_flash/src/nvs_handle_locked.cpp", "D:/ESPRESSIF/v5.4.1/esp-idf/components/nvs_flash/src/nvs_partition.cpp", "D:/ESPRESSIF/v5.4.1/esp-idf/components/nvs_flash/src/nvs_partition_lookup.cpp", "D:/ESPRESSIF/v5.4.1/esp-idf/components/nvs_flash/src/nvs_partition_manager.cpp", "D:/ESPRESSIF/v5.4.1/esp-idf/components/nvs_flash/src/nvs_types.cpp", "D:/ESPRESSIF/v5.4.1/esp-idf/components/nvs_flash/src/nvs_platform.cpp", "D:/ESPRESSIF/v5.4.1/esp-idf/components/nvs_flash/src/nvs_bootloader.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/nvs_flash/src/nvs_encrypted_partition.cpp"], "include_dirs": ["include"]}, "nvs_sec_provider": {"alias": "idf::nvs_sec_provider", "target": "___idf_nvs_sec_provider", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/nvs_sec_provider", "type": "LIBRARY", "lib": "__idf_nvs_sec_provider", "reqs": [], "priv_reqs": ["bootloader_support", "efuse", "esp_partition", "nvs_flash"], "managed_reqs": [], "managed_priv_reqs": [], "file": "D:/ESP-Project/ameter00/build/esp-idf/nvs_sec_provider/libnvs_sec_provider.a", "sources": ["D:/ESPRESSIF/v5.4.1/esp-idf/components/nvs_sec_provider/nvs_sec_provider.c"], "include_dirs": ["include"]}, "openthread": {"alias": "idf::openthread", "target": "___idf_openthread", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/openthread", "type": "CONFIG_ONLY", "lib": "__idf_openthread", "reqs": ["esp_netif", "lwip", "esp_driver_uart", "driver"], "priv_reqs": ["console", "esp_coex", "esp_event", "esp_partition", "esp_timer", "ieee802154", "mbedtls", "nvs_flash"], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": []}, "partition_table": {"alias": "idf::partition_table", "target": "___idf_partition_table", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/partition_table", "type": "CONFIG_ONLY", "lib": "__idf_partition_table", "reqs": [], "priv_reqs": ["esptool_py"], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": []}, "pcf8563": {"alias": "idf::pcf8563", "target": "___idf_pcf8563", "prefix": "idf", "dir": "D:/ESP-Project/ameter00/components/pcf8563", "type": "LIBRARY", "lib": "__idf_pcf8563", "reqs": ["i2cdev", "log"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "D:/ESP-Project/ameter00/build/esp-idf/pcf8563/libpcf8563.a", "sources": ["D:/ESP-Project/ameter00/components/pcf8563/pcf8563.c"], "include_dirs": ["."]}, "perfmon": {"alias": "idf::perfmon", "target": "___idf_perfmon", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/perfmon", "type": "LIBRARY", "lib": "__idf_perfmon", "reqs": ["xtensa"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "D:/ESP-Project/ameter00/build/esp-idf/perfmon/libperfmon.a", "sources": ["D:/ESPRESSIF/v5.4.1/esp-idf/components/perfmon/xtensa_perfmon_access.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/perfmon/xtensa_perfmon_apis.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/perfmon/xtensa_perfmon_masks.c"], "include_dirs": ["include"]}, "protobuf-c": {"alias": "idf::protobuf-c", "target": "___idf_protobuf-c", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/protobuf-c", "type": "LIBRARY", "lib": "__idf_protobuf-c", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "D:/ESP-Project/ameter00/build/esp-idf/protobuf-c/libprotobuf-c.a", "sources": ["D:/ESPRESSIF/v5.4.1/esp-idf/components/protobuf-c/protobuf-c/protobuf-c/protobuf-c.c"], "include_dirs": ["protobuf-c"]}, "protocomm": {"alias": "idf::protocomm", "target": "___idf_protocomm", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/protocomm", "type": "LIBRARY", "lib": "__idf_protocomm", "reqs": ["bt"], "priv_reqs": ["protobuf-c", "mbedtls", "console", "esp_http_server", "driver"], "managed_reqs": [], "managed_priv_reqs": [], "file": "D:/ESP-Project/ameter00/build/esp-idf/protocomm/libprotocomm.a", "sources": ["D:/ESPRESSIF/v5.4.1/esp-idf/components/protocomm/src/common/protocomm.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/protocomm/proto-c/constants.pb-c.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/protocomm/proto-c/sec0.pb-c.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/protocomm/proto-c/sec1.pb-c.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/protocomm/proto-c/sec2.pb-c.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/protocomm/proto-c/session.pb-c.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/protocomm/src/transports/protocomm_console.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/protocomm/src/transports/protocomm_httpd.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/protocomm/src/security/security0.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/protocomm/src/security/security1.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/protocomm/src/security/security2.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/protocomm/src/crypto/srp6a/esp_srp.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/protocomm/src/crypto/srp6a/esp_srp_mpi.c"], "include_dirs": ["include/common", "include/security", "include/transports", "include/crypto/srp6a", "proto-c"]}, "pthread": {"alias": "idf::pthread", "target": "___idf_pthread", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/pthread", "type": "LIBRARY", "lib": "__idf_pthread", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "D:/ESP-Project/ameter00/build/esp-idf/pthread/libpthread.a", "sources": ["D:/ESPRESSIF/v5.4.1/esp-idf/components/pthread/pthread.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/pthread/pthread_cond_var.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/pthread/pthread_local_storage.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/pthread/pthread_rwlock.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/pthread/pthread_semaphore.c"], "include_dirs": ["include"]}, "rt": {"alias": "idf::rt", "target": "___idf_rt", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/rt", "type": "LIBRARY", "lib": "__idf_rt", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "D:/ESP-Project/ameter00/build/esp-idf/rt/librt.a", "sources": ["D:/ESPRESSIF/v5.4.1/esp-idf/components/rt/FreeRTOS_POSIX_mqueue.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/rt/FreeRTOS_POSIX_utils.c"], "include_dirs": ["include"]}, "sdmmc": {"alias": "idf::sdmmc", "target": "___idf_sdmmc", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/sdmmc", "type": "LIBRARY", "lib": "__idf_sdmmc", "reqs": [], "priv_reqs": ["soc", "esp_timer", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "file": "D:/ESP-Project/ameter00/build/esp-idf/sdmmc/libsdmmc.a", "sources": ["D:/ESPRESSIF/v5.4.1/esp-idf/components/sdmmc/sdmmc_cmd.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/sdmmc/sdmmc_common.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/sdmmc/sdmmc_init.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/sdmmc/sdmmc_io.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/sdmmc/sdmmc_mmc.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/sdmmc/sdmmc_sd.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/sdmmc/sd_pwr_ctrl/sd_pwr_ctrl.c"], "include_dirs": ["include"]}, "soc": {"alias": "idf::soc", "target": "___idf_soc", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/soc", "type": "LIBRARY", "lib": "__idf_soc", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "D:/ESP-Project/ameter00/build/esp-idf/soc/libsoc.a", "sources": ["D:/ESPRESSIF/v5.4.1/esp-idf/components/soc/lldesc.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/soc/dport_access_common.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/soc/esp32s3/interrupts.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/soc/esp32s3/gpio_periph.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/soc/esp32s3/uart_periph.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/soc/esp32s3/adc_periph.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/soc/esp32s3/dedic_gpio_periph.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/soc/esp32s3/gdma_periph.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/soc/esp32s3/spi_periph.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/soc/esp32s3/ledc_periph.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/soc/esp32s3/pcnt_periph.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/soc/esp32s3/rmt_periph.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/soc/esp32s3/sdm_periph.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/soc/esp32s3/i2s_periph.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/soc/esp32s3/i2c_periph.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/soc/esp32s3/temperature_sensor_periph.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/soc/esp32s3/timer_periph.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/soc/esp32s3/lcd_periph.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/soc/esp32s3/mcpwm_periph.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/soc/esp32s3/mpi_periph.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/soc/esp32s3/sdmmc_periph.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/soc/esp32s3/touch_sensor_periph.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/soc/esp32s3/twai_periph.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/soc/esp32s3/wdt_periph.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/soc/esp32s3/usb_dwc_periph.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/soc/esp32s3/rtc_io_periph.c"], "include_dirs": ["include", "esp32s3", "esp32s3/include", "esp32s3/register"]}, "spi_flash": {"alias": "idf::spi_flash", "target": "___idf_spi_flash", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/spi_flash", "type": "LIBRARY", "lib": "__idf_spi_flash", "reqs": ["hal"], "priv_reqs": ["bootloader_support", "app_update", "soc", "esp_mm", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "file": "D:/ESP-Project/ameter00/build/esp-idf/spi_flash/libspi_flash.a", "sources": ["D:/ESPRESSIF/v5.4.1/esp-idf/components/spi_flash/flash_brownout_hook.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/spi_flash/esp32s3/spi_flash_oct_flash_init.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/spi_flash/spi_flash_hpm_enable.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/spi_flash/spi_flash_chip_drivers.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/spi_flash/spi_flash_chip_generic.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/spi_flash/spi_flash_chip_issi.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/spi_flash/spi_flash_chip_mxic.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/spi_flash/spi_flash_chip_gd.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/spi_flash/spi_flash_chip_winbond.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/spi_flash/spi_flash_chip_boya.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/spi_flash/spi_flash_chip_mxic_opi.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/spi_flash/spi_flash_chip_th.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/spi_flash/memspi_host_driver.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/spi_flash/cache_utils.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/spi_flash/flash_mmap.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/spi_flash/flash_ops.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/spi_flash/spi_flash_wrap.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/spi_flash/esp_flash_api.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/spi_flash/esp_flash_spi_init.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/spi_flash/spi_flash_os_func_app.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/spi_flash/spi_flash_os_func_noos.c"], "include_dirs": ["include"]}, "spiffs": {"alias": "idf::spiffs", "target": "___idf_spiffs", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/spiffs", "type": "LIBRARY", "lib": "__idf_spiffs", "reqs": ["esp_partition"], "priv_reqs": ["bootloader_support", "esptool_py", "vfs"], "managed_reqs": [], "managed_priv_reqs": [], "file": "D:/ESP-Project/ameter00/build/esp-idf/spiffs/libspiffs.a", "sources": ["D:/ESPRESSIF/v5.4.1/esp-idf/components/spiffs/spiffs_api.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/spiffs/spiffs/src/spiffs_cache.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/spiffs/spiffs/src/spiffs_check.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/spiffs/spiffs/src/spiffs_gc.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/spiffs/spiffs/src/spiffs_hydrogen.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/spiffs/spiffs/src/spiffs_nucleus.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/spiffs/esp_spiffs.c"], "include_dirs": ["include"]}, "tcp_transport": {"alias": "idf::tcp_transport", "target": "___idf_tcp_transport", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/tcp_transport", "type": "LIBRARY", "lib": "__idf_tcp_transport", "reqs": ["esp-tls", "lwip", "esp_timer"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "D:/ESP-Project/ameter00/build/esp-idf/tcp_transport/libtcp_transport.a", "sources": ["D:/ESPRESSIF/v5.4.1/esp-idf/components/tcp_transport/transport.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/tcp_transport/transport_ssl.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/tcp_transport/transport_internal.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/tcp_transport/transport_socks_proxy.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/tcp_transport/transport_ws.c"], "include_dirs": ["include"]}, "touch_element": {"alias": "idf::touch_element", "target": "___idf_touch_element", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/touch_element", "type": "LIBRARY", "lib": "__idf_touch_element", "reqs": ["driver"], "priv_reqs": ["esp_timer"], "managed_reqs": [], "managed_priv_reqs": [], "file": "D:/ESP-Project/ameter00/build/esp-idf/touch_element/libtouch_element.a", "sources": ["D:/ESPRESSIF/v5.4.1/esp-idf/components/touch_element/touch_element.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/touch_element/touch_button.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/touch_element/touch_slider.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/touch_element/touch_matrix.c"], "include_dirs": ["include"]}, "ulp": {"alias": "idf::ulp", "target": "___idf_ulp", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/ulp", "type": "CONFIG_ONLY", "lib": "__idf_ulp", "reqs": ["driver", "esp_adc"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": []}, "unity": {"alias": "idf::unity", "target": "___idf_unity", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/unity", "type": "LIBRARY", "lib": "__idf_unity", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "D:/ESP-Project/ameter00/build/esp-idf/unity/libunity.a", "sources": ["D:/ESPRESSIF/v5.4.1/esp-idf/components/unity/unity/src/unity.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/unity/unity_compat.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/unity/unity_runner.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/unity/unity_utils_freertos.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/unity/unity_utils_cache.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/unity/unity_utils_memory.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/unity/unity_port_esp32.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/unity/port/esp/unity_utils_memory_esp.c"], "include_dirs": ["include", "unity/src"]}, "usb": {"alias": "idf::usb", "target": "___idf_usb", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/usb", "type": "LIBRARY", "lib": "__idf_usb", "reqs": [], "priv_reqs": ["esp_driver_gpio", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "file": "D:/ESP-Project/ameter00/build/esp-idf/usb/libusb.a", "sources": ["D:/ESPRESSIF/v5.4.1/esp-idf/components/usb/hcd_dwc.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/usb/enum.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/usb/hub.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/usb/usb_helpers.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/usb/usb_host.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/usb/usb_private.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/usb/usbh.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/usb/usb_phy.c"], "include_dirs": ["include"]}, "vfs": {"alias": "idf::vfs", "target": "___idf_vfs", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/vfs", "type": "LIBRARY", "lib": "__idf_vfs", "reqs": [], "priv_reqs": ["esp_timer", "esp_driver_uart", "esp_driver_usb_serial_jtag", "esp_vfs_console"], "managed_reqs": [], "managed_priv_reqs": [], "file": "D:/ESP-Project/ameter00/build/esp-idf/vfs/libvfs.a", "sources": ["D:/ESPRESSIF/v5.4.1/esp-idf/components/vfs/vfs.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/vfs/vfs_eventfd.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/vfs/vfs_semihost.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/vfs/nullfs.c"], "include_dirs": ["include"]}, "wear_levelling": {"alias": "idf::wear_levelling", "target": "___idf_wear_levelling", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/wear_levelling", "type": "LIBRARY", "lib": "__idf_wear_levelling", "reqs": ["esp_partition"], "priv_reqs": ["spi_flash"], "managed_reqs": [], "managed_priv_reqs": [], "file": "D:/ESP-Project/ameter00/build/esp-idf/wear_levelling/libwear_levelling.a", "sources": ["D:/ESPRESSIF/v5.4.1/esp-idf/components/wear_levelling/Partition.cpp", "D:/ESPRESSIF/v5.4.1/esp-idf/components/wear_levelling/SPI_Flash.cpp", "D:/ESPRESSIF/v5.4.1/esp-idf/components/wear_levelling/WL_Ext_Perf.cpp", "D:/ESPRESSIF/v5.4.1/esp-idf/components/wear_levelling/WL_Ext_Safe.cpp", "D:/ESPRESSIF/v5.4.1/esp-idf/components/wear_levelling/WL_Flash.cpp", "D:/ESPRESSIF/v5.4.1/esp-idf/components/wear_levelling/crc32.cpp", "D:/ESPRESSIF/v5.4.1/esp-idf/components/wear_levelling/wear_levelling.cpp"], "include_dirs": ["include"]}, "wifi_provisioning": {"alias": "idf::wifi_provisioning", "target": "___idf_wifi_provisioning", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/wifi_provisioning", "type": "LIBRARY", "lib": "__idf_wifi_provisioning", "reqs": ["lwip", "protocomm"], "priv_reqs": ["protobuf-c", "bt", "json", "esp_timer", "esp_wifi"], "managed_reqs": [], "managed_priv_reqs": [], "file": "D:/ESP-Project/ameter00/build/esp-idf/wifi_provisioning/libwifi_provisioning.a", "sources": ["D:/ESPRESSIF/v5.4.1/esp-idf/components/wifi_provisioning/src/wifi_config.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/wifi_provisioning/src/wifi_scan.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/wifi_provisioning/src/wifi_ctrl.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/wifi_provisioning/src/manager.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/wifi_provisioning/src/handlers.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/wifi_provisioning/src/scheme_console.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/wifi_provisioning/proto-c/wifi_config.pb-c.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/wifi_provisioning/proto-c/wifi_scan.pb-c.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/wifi_provisioning/proto-c/wifi_ctrl.pb-c.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/wifi_provisioning/proto-c/wifi_constants.pb-c.c"], "include_dirs": ["include"]}, "wpa_supplicant": {"alias": "idf::wpa_supplicant", "target": "___idf_wpa_supplicant", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/wpa_supplicant", "type": "LIBRARY", "lib": "__idf_wpa_supplicant", "reqs": [], "priv_reqs": ["mbedtls", "esp_timer", "esp_wifi"], "managed_reqs": [], "managed_priv_reqs": [], "file": "D:/ESP-Project/ameter00/build/esp-idf/wpa_supplicant/libwpa_supplicant.a", "sources": ["D:/ESPRESSIF/v5.4.1/esp-idf/components/wpa_supplicant/port/os_xtensa.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/wpa_supplicant/port/eloop.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/wpa_supplicant/src/ap/ap_config.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/wpa_supplicant/src/ap/ieee802_1x.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/wpa_supplicant/src/ap/wpa_auth.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/wpa_supplicant/src/ap/wpa_auth_ie.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/wpa_supplicant/src/ap/pmksa_cache_auth.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/wpa_supplicant/src/ap/sta_info.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/wpa_supplicant/src/ap/ieee802_11.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/wpa_supplicant/src/ap/comeback_token.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/wpa_supplicant/src/common/sae.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/wpa_supplicant/src/common/dragonfly.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/wpa_supplicant/src/common/wpa_common.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/wpa_supplicant/src/utils/bitfield.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/wpa_supplicant/src/crypto/aes-siv.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/wpa_supplicant/src/crypto/sha256-kdf.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/wpa_supplicant/src/crypto/ccmp.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/wpa_supplicant/src/crypto/aes-gcm.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/wpa_supplicant/src/crypto/crypto_ops.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/wpa_supplicant/src/crypto/dh_group5.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/wpa_supplicant/src/crypto/dh_groups.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/wpa_supplicant/src/crypto/ms_funcs.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/wpa_supplicant/src/crypto/sha1-tlsprf.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/wpa_supplicant/src/crypto/sha256-tlsprf.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/wpa_supplicant/src/crypto/sha384-tlsprf.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/wpa_supplicant/src/crypto/sha256-prf.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/wpa_supplicant/src/crypto/sha1-prf.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/wpa_supplicant/src/crypto/sha384-prf.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/wpa_supplicant/src/crypto/md4-internal.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/wpa_supplicant/src/crypto/sha1-tprf.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/wpa_supplicant/src/eap_common/eap_wsc_common.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/wpa_supplicant/src/common/ieee802_11_common.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/wpa_supplicant/src/eap_peer/chap.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/wpa_supplicant/src/eap_peer/eap.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/wpa_supplicant/src/eap_peer/eap_common.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/wpa_supplicant/src/eap_peer/eap_mschapv2.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/wpa_supplicant/src/eap_peer/eap_peap.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/wpa_supplicant/src/eap_peer/eap_peap_common.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/wpa_supplicant/src/eap_peer/eap_tls.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/wpa_supplicant/src/eap_peer/eap_tls_common.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/wpa_supplicant/src/eap_peer/eap_ttls.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/wpa_supplicant/src/eap_peer/mschapv2.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/wpa_supplicant/src/eap_peer/eap_fast.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/wpa_supplicant/src/eap_peer/eap_fast_common.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/wpa_supplicant/src/eap_peer/eap_fast_pac.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/wpa_supplicant/src/rsn_supp/pmksa_cache.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/wpa_supplicant/src/rsn_supp/wpa.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/wpa_supplicant/src/rsn_supp/wpa_ie.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/wpa_supplicant/src/utils/base64.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/wpa_supplicant/src/utils/common.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/wpa_supplicant/src/utils/ext_password.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/wpa_supplicant/src/utils/uuid.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/wpa_supplicant/src/utils/wpabuf.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/wpa_supplicant/src/utils/wpa_debug.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/wpa_supplicant/src/utils/json.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/wpa_supplicant/src/wps/wps.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/wpa_supplicant/src/wps/wps_attr_build.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/wpa_supplicant/src/wps/wps_attr_parse.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/wpa_supplicant/src/wps/wps_attr_process.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/wpa_supplicant/src/wps/wps_common.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/wpa_supplicant/src/wps/wps_dev_attr.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/wpa_supplicant/src/wps/wps_enrollee.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/wpa_supplicant/src/common/sae_pk.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/wpa_supplicant/esp_supplicant/src/esp_eap_client.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/wpa_supplicant/esp_supplicant/src/esp_wpa2_api_port.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/wpa_supplicant/esp_supplicant/src/esp_wpa_main.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/wpa_supplicant/esp_supplicant/src/esp_wpas_glue.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/wpa_supplicant/esp_supplicant/src/esp_common.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/wpa_supplicant/esp_supplicant/src/esp_wps.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/wpa_supplicant/esp_supplicant/src/esp_wpa3.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/wpa_supplicant/esp_supplicant/src/esp_owe.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/wpa_supplicant/esp_supplicant/src/crypto/tls_mbedtls.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/wpa_supplicant/esp_supplicant/src/crypto/fastpbkdf2.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/wpa_supplicant/esp_supplicant/src/crypto/crypto_mbedtls.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/wpa_supplicant/esp_supplicant/src/crypto/crypto_mbedtls-bignum.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/wpa_supplicant/esp_supplicant/src/crypto/crypto_mbedtls-rsa.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/wpa_supplicant/esp_supplicant/src/crypto/crypto_mbedtls-ec.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/wpa_supplicant/src/crypto/rc4.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/wpa_supplicant/src/crypto/des-internal.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/wpa_supplicant/src/crypto/aes-wrap.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/wpa_supplicant/src/crypto/aes-unwrap.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/wpa_supplicant/src/crypto/aes-ccm.c"], "include_dirs": ["include", "port/include", "esp_supplicant/include"]}, "xtensa": {"alias": "idf::xtensa", "target": "___idf_xtensa", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/xtensa", "type": "LIBRARY", "lib": "__idf_xtensa", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "D:/ESP-Project/ameter00/build/esp-idf/xtensa/libxtensa.a", "sources": ["D:/ESPRESSIF/v5.4.1/esp-idf/components/xtensa/eri.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/xtensa/xt_trax.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/xtensa/xtensa_context.S", "D:/ESPRESSIF/v5.4.1/esp-idf/components/xtensa/xtensa_intr_asm.S", "D:/ESPRESSIF/v5.4.1/esp-idf/components/xtensa/xtensa_intr.c", "D:/ESPRESSIF/v5.4.1/esp-idf/components/xtensa/xtensa_vectors.S"], "include_dirs": ["esp32s3/include", "include", "deprecated_include"]}}, "all_component_info": {"app_trace": {"alias": "idf::app_trace", "target": "___idf_app_trace", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/app_trace", "lib": "__idf_app_trace", "reqs": ["esp_timer"], "priv_reqs": ["esp_driver_gptimer", "esp_driver_gpio", "esp_driver_uart"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "app_update": {"alias": "idf::app_update", "target": "___idf_app_update", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/app_update", "lib": "__idf_app_update", "reqs": ["partition_table", "bootloader_support", "esp_app_format", "esp_bootloader_format", "esp_partition"], "priv_reqs": ["esptool_py", "efuse", "spi_flash"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "bootloader": {"alias": "idf::bootloader", "target": "___idf_bootloader", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/bootloader", "lib": "__idf_bootloader", "reqs": [], "priv_reqs": ["partition_table", "esptool_py"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": []}, "bootloader_support": {"alias": "idf::bootloader_support", "target": "___idf_bootloader_support", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/bootloader_support", "lib": "__idf_bootloader_support", "reqs": ["soc"], "priv_reqs": ["spi_flash", "mbedtls", "efuse", "heap", "esp_bootloader_format", "esp_app_format"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "bootloader_flash/include"]}, "bt": {"alias": "idf::bt", "target": "___idf_bt", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/bt", "lib": "__idf_bt", "reqs": ["esp_timer", "esp_wifi"], "priv_reqs": ["nvs_flash", "soc", "esp_pm", "esp_phy", "esp_coex", "mbedtls", "esp_driver_uart", "vfs", "esp_ringbuf", "esp_driver_spi", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": []}, "cmock": {"alias": "idf::cmock", "target": "___idf_cmock", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/cmock", "lib": "__idf_cmock", "reqs": ["unity"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["CMock/src"]}, "console": {"alias": "idf::console", "target": "___idf_console", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/console", "lib": "__idf_console", "reqs": ["vfs", "esp_vfs_console"], "priv_reqs": ["esp_driver_uart", "esp_driver_usb_serial_jtag"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["D:/ESPRESSIF/v5.4.1/esp-idf/components/console"]}, "cxx": {"alias": "idf::cxx", "target": "___idf_cxx", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/cxx", "lib": "__idf_cxx", "reqs": [], "priv_reqs": ["pthread", "esp_system"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": []}, "driver": {"alias": "idf::driver", "target": "___idf_driver", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/driver", "lib": "__idf_driver", "reqs": ["esp_pm", "esp_ringbuf", "freertos", "soc", "hal", "esp_hw_support", "esp_driver_gpio", "esp_driver_pcnt", "esp_driver_gptimer", "esp_driver_spi", "esp_driver_mcpwm", "esp_driver_ana_cmpr", "esp_driver_i2s", "esp_driver_sdmmc", "esp_driver_sdspi", "esp_driver_sdio", "esp_driver_dac", "esp_driver_rmt", "esp_driver_tsens", "esp_driver_sdm", "esp_driver_i2c", "esp_driver_uart", "esp_driver_ledc", "esp_driver_parlio", "esp_driver_usb_serial_jtag"], "priv_reqs": ["efuse", "esp_timer", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["deprecated", "i2c/include", "touch_sensor/include", "twai/include", "touch_sensor/esp32s3/include"]}, "efuse": {"alias": "idf::efuse", "target": "___idf_efuse", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/efuse", "lib": "__idf_efuse", "reqs": [], "priv_reqs": ["bootloader_support", "soc", "spi_flash", "esp_system", "esp_partition", "esp_app_format"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "esp32s3/include"]}, "esp-tls": {"alias": "idf::esp-tls", "target": "___idf_esp-tls", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp-tls", "lib": "__idf_esp-tls", "reqs": ["mbedtls"], "priv_reqs": ["http_parser", "esp_timer", "lwip"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["D:/ESPRESSIF/v5.4.1/esp-idf/components/esp-tls", "esp-tls-crypto"]}, "esp_adc": {"alias": "idf::esp_adc", "target": "___idf_esp_adc", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_adc", "lib": "__idf_esp_adc", "reqs": [], "priv_reqs": ["driver", "esp_driver_gpio", "efuse", "esp_pm", "esp_ringbuf", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "interface", "esp32s3/include", "deprecated/include"]}, "esp_app_format": {"alias": "idf::esp_app_format", "target": "___idf_esp_app_format", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_app_format", "lib": "__idf_esp_app_format", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_bootloader_format": {"alias": "idf::esp_bootloader_format", "target": "___idf_esp_bootloader_format", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_bootloader_format", "lib": "__idf_esp_bootloader_format", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_coex": {"alias": "idf::esp_coex", "target": "___idf_esp_coex", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_coex", "lib": "__idf_esp_coex", "reqs": [], "priv_reqs": ["esp_timer", "driver", "esp_event"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_common": {"alias": "idf::esp_common", "target": "___idf_esp_common", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_common", "lib": "__idf_esp_common", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_ana_cmpr": {"alias": "idf::esp_driver_ana_cmpr", "target": "___idf_esp_driver_ana_cmpr", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_driver_ana_cmpr", "lib": "__idf_esp_driver_ana_cmpr", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_cam": {"alias": "idf::esp_driver_cam", "target": "___idf_esp_driver_cam", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_driver_cam", "lib": "__idf_esp_driver_cam", "reqs": ["esp_driver_isp", "esp_mm"], "priv_reqs": ["esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "interface"]}, "esp_driver_dac": {"alias": "idf::esp_driver_dac", "target": "___idf_esp_driver_dac", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_driver_dac", "lib": "__idf_esp_driver_dac", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["./include"]}, "esp_driver_gpio": {"alias": "idf::esp_driver_gpio", "target": "___idf_esp_driver_gpio", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_driver_gpio", "lib": "__idf_esp_driver_gpio", "reqs": [], "priv_reqs": ["esp_pm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_gptimer": {"alias": "idf::esp_driver_gptimer", "target": "___idf_esp_driver_gptimer", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_driver_gptimer", "lib": "__idf_esp_driver_gptimer", "reqs": ["esp_pm"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_i2c": {"alias": "idf::esp_driver_i2c", "target": "___idf_esp_driver_i2c", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_driver_i2c", "lib": "__idf_esp_driver_i2c", "reqs": [], "priv_reqs": ["esp_driver_gpio", "esp_pm", "esp_ringbuf"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_i2s": {"alias": "idf::esp_driver_i2s", "target": "___idf_esp_driver_i2s", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_driver_i2s", "lib": "__idf_esp_driver_i2s", "reqs": [], "priv_reqs": ["esp_driver_gpio", "esp_pm", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_isp": {"alias": "idf::esp_driver_isp", "target": "___idf_esp_driver_isp", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_driver_isp", "lib": "__idf_esp_driver_isp", "reqs": ["esp_mm"], "priv_reqs": ["esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_jpeg": {"alias": "idf::esp_driver_jpeg", "target": "___idf_esp_driver_jpeg", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_driver_jpeg", "lib": "__idf_esp_driver_jpeg", "reqs": [], "priv_reqs": ["esp_mm", "esp_pm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_ledc": {"alias": "idf::esp_driver_ledc", "target": "___idf_esp_driver_ledc", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_driver_ledc", "lib": "__idf_esp_driver_ledc", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_mcpwm": {"alias": "idf::esp_driver_mcpwm", "target": "___idf_esp_driver_mcpwm", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_driver_mcpwm", "lib": "__idf_esp_driver_mcpwm", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_parlio": {"alias": "idf::esp_driver_parlio", "target": "___idf_esp_driver_parlio", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_driver_parlio", "lib": "__idf_esp_driver_parlio", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_pcnt": {"alias": "idf::esp_driver_pcnt", "target": "___idf_esp_driver_pcnt", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_driver_pcnt", "lib": "__idf_esp_driver_pcnt", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_ppa": {"alias": "idf::esp_driver_ppa", "target": "___idf_esp_driver_ppa", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_driver_ppa", "lib": "__idf_esp_driver_ppa", "reqs": [], "priv_reqs": ["esp_mm", "esp_pm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_rmt": {"alias": "idf::esp_driver_rmt", "target": "___idf_esp_driver_rmt", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_driver_rmt", "lib": "__idf_esp_driver_rmt", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_sdio": {"alias": "idf::esp_driver_sdio", "target": "___idf_esp_driver_sdio", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_driver_sdio", "lib": "__idf_esp_driver_sdio", "reqs": [], "priv_reqs": ["esp_driver_gpio", "esp_ringbuf"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_sdm": {"alias": "idf::esp_driver_sdm", "target": "___idf_esp_driver_sdm", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_driver_sdm", "lib": "__idf_esp_driver_sdm", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_sdmmc": {"alias": "idf::esp_driver_sdmmc", "target": "___idf_esp_driver_sdmmc", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_driver_sdmmc", "lib": "__idf_esp_driver_sdmmc", "reqs": ["sdmmc", "esp_driver_gpio"], "priv_reqs": ["esp_timer", "esp_pm", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_sdspi": {"alias": "idf::esp_driver_sdspi", "target": "___idf_esp_driver_sdspi", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_driver_sdspi", "lib": "__idf_esp_driver_sdspi", "reqs": ["sdmmc", "esp_driver_spi", "esp_driver_gpio"], "priv_reqs": ["esp_timer"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_spi": {"alias": "idf::esp_driver_spi", "target": "___idf_esp_driver_spi", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_driver_spi", "lib": "__idf_esp_driver_spi", "reqs": ["esp_pm"], "priv_reqs": ["esp_timer", "esp_mm", "esp_driver_gpio", "esp_ringbuf"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_touch_sens": {"alias": "idf::esp_driver_touch_sens", "target": "___idf_esp_driver_touch_sens", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_driver_touch_sens", "lib": "__idf_esp_driver_touch_sens", "reqs": [], "priv_reqs": ["esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": []}, "esp_driver_tsens": {"alias": "idf::esp_driver_tsens", "target": "___idf_esp_driver_tsens", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_driver_tsens", "lib": "__idf_esp_driver_tsens", "reqs": [], "priv_reqs": ["efuse"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_uart": {"alias": "idf::esp_driver_uart", "target": "___idf_esp_driver_uart", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_driver_uart", "lib": "__idf_esp_driver_uart", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio", "esp_ringbuf"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_usb_serial_jtag": {"alias": "idf::esp_driver_usb_serial_jtag", "target": "___idf_esp_driver_usb_serial_jtag", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_driver_usb_serial_jtag", "lib": "__idf_esp_driver_usb_serial_jtag", "reqs": [], "priv_reqs": ["esp_driver_gpio", "esp_ringbuf", "esp_pm", "esp_timer"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_eth": {"alias": "idf::esp_eth", "target": "___idf_esp_eth", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_eth", "lib": "__idf_esp_eth", "reqs": ["esp_event"], "priv_reqs": ["log", "esp_timer", "esp_driver_spi", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": []}, "esp_event": {"alias": "idf::esp_event", "target": "___idf_esp_event", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_event", "lib": "__idf_esp_event", "reqs": ["log", "esp_common", "freertos"], "priv_reqs": ["esp_timer"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_gdbstub": {"alias": "idf::esp_gdbstub", "target": "___idf_esp_gdbstub", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_gdbstub", "lib": "__idf_esp_gdbstub", "reqs": ["freertos"], "priv_reqs": ["soc", "esp_rom", "esp_system"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_hid": {"alias": "idf::esp_hid", "target": "___idf_esp_hid", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_hid", "lib": "__idf_esp_hid", "reqs": ["esp_event", "bt"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_http_client": {"alias": "idf::esp_http_client", "target": "___idf_esp_http_client", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_http_client", "lib": "__idf_esp_http_client", "reqs": ["lwip", "esp_event"], "priv_reqs": ["tcp_transport", "http_parser"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_http_server": {"alias": "idf::esp_http_server", "target": "___idf_esp_http_server", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_http_server", "lib": "__idf_esp_http_server", "reqs": ["http_parser", "esp_event"], "priv_reqs": ["mbedtls", "lwip", "esp_timer"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_https_ota": {"alias": "idf::esp_https_ota", "target": "___idf_esp_https_ota", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_https_ota", "lib": "__idf_esp_https_ota", "reqs": ["esp_http_client", "bootloader_support", "esp_app_format", "esp_event"], "priv_reqs": ["log", "app_update"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_https_server": {"alias": "idf::esp_https_server", "target": "___idf_esp_https_server", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_https_server", "lib": "__idf_esp_https_server", "reqs": ["esp_http_server", "esp-tls", "esp_event"], "priv_reqs": ["lwip"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_hw_support": {"alias": "idf::esp_hw_support", "target": "___idf_esp_hw_support", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_hw_support", "lib": "__idf_esp_hw_support", "reqs": ["soc"], "priv_reqs": ["efuse", "spi_flash", "bootloader_support", "esp_security", "esp_driver_gpio", "esp_timer", "esp_pm", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "include/soc", "include/soc/esp32s3", "dma/include", "ldo/include", "debug_probe/include"]}, "esp_lcd": {"alias": "idf::esp_lcd", "target": "___idf_esp_lcd", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_lcd", "lib": "__idf_esp_lcd", "reqs": ["driver", "esp_driver_gpio", "esp_driver_i2c", "esp_driver_spi"], "priv_reqs": ["esp_mm", "esp_psram", "esp_pm", "esp_driver_i2s"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "interface", "rgb/include"]}, "esp_local_ctrl": {"alias": "idf::esp_local_ctrl", "target": "___idf_esp_local_ctrl", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_local_ctrl", "lib": "__idf_esp_local_ctrl", "reqs": ["protocomm", "esp_https_server"], "priv_reqs": ["protobuf-c"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_mm": {"alias": "idf::esp_mm", "target": "___idf_esp_mm", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_mm", "lib": "__idf_esp_mm", "reqs": [], "priv_reqs": ["heap", "spi_flash"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_netif": {"alias": "idf::esp_netif", "target": "___idf_esp_netif", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_netif", "lib": "__idf_esp_netif", "reqs": ["esp_event"], "priv_reqs": ["esp_netif_stack"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_netif_stack": {"alias": "idf::esp_netif_stack", "target": "___idf_esp_netif_stack", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_netif_stack", "lib": "__idf_esp_netif_stack", "reqs": ["lwip"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": []}, "esp_partition": {"alias": "idf::esp_partition", "target": "___idf_esp_partition", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_partition", "lib": "__idf_esp_partition", "reqs": [], "priv_reqs": ["esp_system", "spi_flash", "partition_table", "bootloader_support", "app_update"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_phy": {"alias": "idf::esp_phy", "target": "___idf_esp_phy", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_phy", "lib": "__idf_esp_phy", "reqs": [], "priv_reqs": ["nvs_flash", "driver", "efuse", "esp_timer", "esp_wifi"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "esp32s3/include"]}, "esp_pm": {"alias": "idf::esp_pm", "target": "___idf_esp_pm", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_pm", "lib": "__idf_esp_pm", "reqs": [], "priv_reqs": ["esp_system", "esp_driver_gpio", "esp_timer"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_psram": {"alias": "idf::esp_psram", "target": "___idf_esp_psram", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_psram", "lib": "__idf_esp_psram", "reqs": [], "priv_reqs": ["heap", "spi_flash", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_ringbuf": {"alias": "idf::esp_ringbuf", "target": "___idf_esp_ringbuf", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_ringbuf", "lib": "__idf_esp_ringbuf", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_rom": {"alias": "idf::esp_rom", "target": "___idf_esp_rom", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_rom", "lib": "__idf_esp_rom", "reqs": [], "priv_reqs": ["soc", "hal"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "esp32s3/include", "esp32s3/include/esp32s3", "esp32s3"]}, "esp_security": {"alias": "idf::esp_security", "target": "___idf_esp_security", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_security", "lib": "__idf_esp_security", "reqs": [], "priv_reqs": ["efuse", "esp_hw_support", "esp_system", "esp_timer"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_system": {"alias": "idf::esp_system", "target": "___idf_esp_system", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_system", "lib": "__idf_esp_system", "reqs": [], "priv_reqs": ["spi_flash", "esp_timer", "esp_mm", "bootloader_support", "esp_pm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_timer": {"alias": "idf::esp_timer", "target": "___idf_esp_timer", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_timer", "lib": "__idf_esp_timer", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_vfs_console": {"alias": "idf::esp_vfs_console", "target": "___idf_esp_vfs_console", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_vfs_console", "lib": "__idf_esp_vfs_console", "reqs": [], "priv_reqs": ["vfs", "esp_driver_uart", "esp_driver_usb_serial_jtag"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_wifi": {"alias": "idf::esp_wifi", "target": "___idf_esp_wifi", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/esp_wifi", "lib": "__idf_esp_wifi", "reqs": ["esp_event", "esp_phy", "esp_netif"], "priv_reqs": ["driver", "esptool_py", "esp_pm", "esp_timer", "nvs_flash", "wpa_supplicant", "hal", "lwip", "esp_coex"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "include/local", "wifi_apps/include", "wifi_apps/nan_app/include"]}, "espcoredump": {"alias": "idf::espcoredump", "target": "___idf_espcoredump", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/espcoredump", "lib": "__idf_espcoredump", "reqs": [], "priv_reqs": ["esp_partition", "spi_flash", "bootloader_support", "mbedtls", "esp_rom", "soc", "esp_system", "esp_driver_gpio", "driver"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "include/port/xtensa"]}, "esptool_py": {"alias": "idf::esptool_py", "target": "___idf_esptool_py", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/esptool_py", "lib": "__idf_esptool_py", "reqs": ["bootloader"], "priv_reqs": ["partition_table"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": []}, "fatfs": {"alias": "idf::fatfs", "target": "___idf_fatfs", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/fatfs", "lib": "__idf_fatfs", "reqs": ["wear_levelling", "sdmmc", "esp_driver_sdmmc", "esp_driver_sdspi"], "priv_reqs": ["vfs", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["diskio", "src", "vfs"]}, "freertos": {"alias": "idf::freertos", "target": "___idf_freertos", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/freertos", "lib": "__idf_freertos", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["config/include", "config/include/freertos", "config/xtensa/include", "FreeRTOS-Kernel/include", "FreeRTOS-Kernel/portable/xtensa/include", "FreeRTOS-Kernel/portable/xtensa/include/freertos", "esp_additions/include"]}, "hal": {"alias": "idf::hal", "target": "___idf_hal", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/hal", "lib": "__idf_hal", "reqs": ["soc", "esp_rom"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["platform_port/include", "esp32s3/include", "include"]}, "heap": {"alias": "idf::heap", "target": "___idf_heap", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/heap", "lib": "__idf_heap", "reqs": [], "priv_reqs": ["soc"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "tlsf"]}, "http_parser": {"alias": "idf::http_parser", "target": "___idf_http_parser", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/http_parser", "lib": "__idf_http_parser", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["."]}, "idf_test": {"alias": "idf::idf_test", "target": "___idf_idf_test", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/idf_test", "lib": "__idf_idf_test", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "include/esp32s3"]}, "ieee802154": {"alias": "idf::ieee802154", "target": "___idf_ieee802154", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/ieee802154", "lib": "__idf_ieee802154", "reqs": ["esp_coex"], "priv_reqs": ["esp_phy", "driver", "esp_timer", "soc", "hal"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "json": {"alias": "idf::json", "target": "___idf_json", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/json", "lib": "__idf_json", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["cJSON"]}, "linux": {"alias": "idf::linux", "target": "___idf_linux", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/linux", "lib": "__idf_linux", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["cJSON"]}, "log": {"alias": "idf::log", "target": "___idf_log", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/log", "lib": "__idf_log", "reqs": [], "priv_reqs": ["soc", "hal", "esp_hw_support"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "lwip": {"alias": "idf::lwip", "target": "___idf_lwip", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/lwip", "lib": "__idf_lwip", "reqs": [], "priv_reqs": ["vfs"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "include/apps", "include/apps/sntp", "lwip/src/include", "port/include", "port/freertos/include/", "port/esp32xx/include", "port/esp32xx/include/arch", "port/esp32xx/include/sys"]}, "mbedtls": {"alias": "idf::mbedtls", "target": "___idf_mbedtls", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/mbedtls", "lib": "__idf_mbedtls", "reqs": [], "priv_reqs": ["soc", "esp_hw_support", "esp_pm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["port/include", "mbedtls/include", "mbedtls/library", "esp_crt_bundle/include"]}, "mqtt": {"alias": "idf::mqtt", "target": "___idf_mqtt", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/mqtt", "lib": "__idf_mqtt", "reqs": ["esp_event", "tcp_transport"], "priv_reqs": ["esp_timer", "http_parser", "esp_hw_support", "heap"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["D:/ESPRESSIF/v5.4.1/esp-idf/components/mqtt/esp-mqtt/include"]}, "newlib": {"alias": "idf::newlib", "target": "___idf_newlib", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/newlib", "lib": "__idf_newlib", "reqs": [], "priv_reqs": ["soc", "spi_flash"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["platform_include"]}, "nvs_flash": {"alias": "idf::nvs_flash", "target": "___idf_nvs_flash", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/nvs_flash", "lib": "__idf_nvs_flash", "reqs": ["esp_partition"], "priv_reqs": ["spi_flash", "newlib"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "nvs_sec_provider": {"alias": "idf::nvs_sec_provider", "target": "___idf_nvs_sec_provider", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/nvs_sec_provider", "lib": "__idf_nvs_sec_provider", "reqs": [], "priv_reqs": ["bootloader_support", "efuse", "esp_partition", "nvs_flash"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "openthread": {"alias": "idf::openthread", "target": "___idf_openthread", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/openthread", "lib": "__idf_openthread", "reqs": ["esp_netif", "lwip", "esp_driver_uart", "driver"], "priv_reqs": ["console", "esp_coex", "esp_event", "esp_partition", "esp_timer", "ieee802154", "mbedtls", "nvs_flash"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": []}, "partition_table": {"alias": "idf::partition_table", "target": "___idf_partition_table", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/partition_table", "lib": "__idf_partition_table", "reqs": [], "priv_reqs": ["esptool_py"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": []}, "perfmon": {"alias": "idf::perfmon", "target": "___idf_perfmon", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/perfmon", "lib": "__idf_perfmon", "reqs": ["xtensa"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "protobuf-c": {"alias": "idf::protobuf-c", "target": "___idf_protobuf-c", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/protobuf-c", "lib": "__idf_protobuf-c", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["protobuf-c"]}, "protocomm": {"alias": "idf::protocomm", "target": "___idf_protocomm", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/protocomm", "lib": "__idf_protocomm", "reqs": ["bt"], "priv_reqs": ["protobuf-c", "mbedtls", "console", "esp_http_server", "driver"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include/common", "include/security", "include/transports", "include/crypto/srp6a", "proto-c"]}, "pthread": {"alias": "idf::pthread", "target": "___idf_pthread", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/pthread", "lib": "__idf_pthread", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "riscv": {"alias": "idf::riscv", "target": "___idf_riscv", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/riscv", "lib": "__idf_riscv", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "rt": {"alias": "idf::rt", "target": "___idf_rt", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/rt", "lib": "__idf_rt", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "sdmmc": {"alias": "idf::sdmmc", "target": "___idf_sdmmc", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/sdmmc", "lib": "__idf_sdmmc", "reqs": [], "priv_reqs": ["soc", "esp_timer", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "soc": {"alias": "idf::soc", "target": "___idf_soc", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/soc", "lib": "__idf_soc", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "esp32s3", "esp32s3/include", "esp32s3/register"]}, "spi_flash": {"alias": "idf::spi_flash", "target": "___idf_spi_flash", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/spi_flash", "lib": "__idf_spi_flash", "reqs": ["hal"], "priv_reqs": ["bootloader_support", "app_update", "soc", "esp_mm", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "spiffs": {"alias": "idf::spiffs", "target": "___idf_spiffs", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/spiffs", "lib": "__idf_spiffs", "reqs": ["esp_partition"], "priv_reqs": ["bootloader_support", "esptool_py", "vfs"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "tcp_transport": {"alias": "idf::tcp_transport", "target": "___idf_tcp_transport", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/tcp_transport", "lib": "__idf_tcp_transport", "reqs": ["esp-tls", "lwip", "esp_timer"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "touch_element": {"alias": "idf::touch_element", "target": "___idf_touch_element", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/touch_element", "lib": "__idf_touch_element", "reqs": ["driver"], "priv_reqs": ["esp_timer"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "ulp": {"alias": "idf::ulp", "target": "___idf_ulp", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/ulp", "lib": "__idf_ulp", "reqs": ["driver", "esp_adc"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": []}, "unity": {"alias": "idf::unity", "target": "___idf_unity", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/unity", "lib": "__idf_unity", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "unity/src"]}, "usb": {"alias": "idf::usb", "target": "___idf_usb", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/usb", "lib": "__idf_usb", "reqs": [], "priv_reqs": ["esp_driver_gpio", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "vfs": {"alias": "idf::vfs", "target": "___idf_vfs", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/vfs", "lib": "__idf_vfs", "reqs": [], "priv_reqs": ["esp_timer", "esp_driver_uart", "esp_driver_usb_serial_jtag", "esp_vfs_console"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "wear_levelling": {"alias": "idf::wear_levelling", "target": "___idf_wear_levelling", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/wear_levelling", "lib": "__idf_wear_levelling", "reqs": ["esp_partition"], "priv_reqs": ["spi_flash"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "wifi_provisioning": {"alias": "idf::wifi_provisioning", "target": "___idf_wifi_provisioning", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/wifi_provisioning", "lib": "__idf_wifi_provisioning", "reqs": ["lwip", "protocomm"], "priv_reqs": ["protobuf-c", "bt", "json", "esp_timer", "esp_wifi"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "wpa_supplicant": {"alias": "idf::wpa_supplicant", "target": "___idf_wpa_supplicant", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/wpa_supplicant", "lib": "__idf_wpa_supplicant", "reqs": [], "priv_reqs": ["mbedtls", "esp_timer", "esp_wifi"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "port/include", "esp_supplicant/include"]}, "xtensa": {"alias": "idf::xtensa", "target": "___idf_xtensa", "prefix": "idf", "dir": "D:/ESPRESSIF/v5.4.1/esp-idf/components/xtensa", "lib": "__idf_xtensa", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["esp32s3/include", "include", "deprecated_include"]}, "main": {"alias": "idf::main", "target": "___idf_main", "prefix": "idf", "dir": "D:/ESP-Project/ameter00/main", "lib": "__idf_main", "reqs": [], "priv_reqs": ["espressif__esp_lcd_touch_cst816s", "fusion_ameter_s3", "fb_gfx", "pcf8563", "modules", "bus", "esp-dl", "espressif__esp-modbus", "espressif__esp-sr", "espressif__esp32_s3_eye", "espressif__esp_io_expander", "espressif__esp_io_expander_tca95xx_16bit", "espressif__esp_lvgl_port", "lvgl__lvgl"], "managed_reqs": [], "managed_priv_reqs": ["esp-dl", "espressif__esp-modbus", "espressif__esp-sr", "espressif__esp32_s3_eye", "espressif__esp_io_expander", "espressif__esp_io_expander_tca95xx_16bit", "espressif__esp_lcd_touch_cst816s", "espressif__esp_lvgl_port", "lvgl__lvgl"], "include_dirs": [".", "ui", "include", "."]}, "bus": {"alias": "idf::bus", "target": "___idf_bus", "prefix": "idf", "dir": "D:/ESP-Project/ameter00/components/bus", "lib": "__idf_bus", "reqs": ["driver"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp-dl": {"alias": "idf::esp-dl", "target": "___idf_esp-dl", "prefix": "idf", "dir": "D:/ESP-Project/ameter00/components/esp-dl", "lib": "__idf_esp-dl", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include/", "include/tool", "include/typedef", "include/image", "include/math", "include/nn", "include/tvm", "include/layer", "include/detect", "include/model_zoo"]}, "espressif__button": {"alias": "idf::espressif__button", "target": "___idf_espressif__button", "prefix": "idf", "dir": "D:/ESP-Project/ameter00/components/espressif__button", "lib": "__idf_espressif__button", "reqs": ["driver", "esp_adc"], "priv_reqs": ["esp_timer", "espressif__cmake_utilities"], "managed_reqs": [], "managed_priv_reqs": ["espressif__cmake_utilities"], "include_dirs": ["include"]}, "espressif__cmake_utilities": {"alias": "idf::espressif__cmake_utilities", "target": "___idf_espressif__cmake_utilities", "prefix": "idf", "dir": "D:/ESP-Project/ameter00/managed_components/espressif__cmake_utilities", "lib": "__idf_espressif__cmake_utilities", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": []}, "espressif__esp32-camera": {"alias": "idf::espressif__esp32-camera", "target": "___idf_espressif__esp32-camera", "prefix": "idf", "dir": "D:/ESP-Project/ameter00/components/espressif__esp32-camera", "lib": "__idf_espressif__esp32-camera", "reqs": ["driver"], "priv_reqs": ["freertos", "nvs_flash", "esp_timer"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["driver/include", "conversions/include"]}, "espressif__esp_codec_dev": {"alias": "idf::espressif__esp_codec_dev", "target": "___idf_espressif__esp_codec_dev", "prefix": "idf", "dir": "D:/ESP-Project/ameter00/components/espressif__esp_codec_dev", "lib": "__idf_espressif__esp_codec_dev", "reqs": ["driver"], "priv_reqs": ["freertos"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "interface", "device/include"]}, "espressif__esp_io_expander": {"alias": "idf::espressif__esp_io_expander", "target": "___idf_espressif__esp_io_expander", "prefix": "idf", "dir": "D:/ESP-Project/ameter00/components/espressif__esp_io_expander", "lib": "__idf_espressif__esp_io_expander", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "espressif__esp_io_expander_tca95xx_16bit": {"alias": "idf::espressif__esp_io_expander_tca95xx_16bit", "target": "___idf_espressif__esp_io_expander_tca95xx_16bit", "prefix": "idf", "dir": "D:/ESP-Project/ameter00/components/espressif__esp_io_expander_tca95xx_16bit", "lib": "__idf_espressif__esp_io_expander_tca95xx_16bit", "reqs": ["driver"], "priv_reqs": ["espressif__esp_io_expander"], "managed_reqs": [], "managed_priv_reqs": ["espressif__esp_io_expander"], "include_dirs": ["include"]}, "espressif__esp_lcd_touch": {"alias": "idf::espressif__esp_lcd_touch", "target": "___idf_espressif__esp_lcd_touch", "prefix": "idf", "dir": "D:/ESP-Project/ameter00/components/espressif__esp_lcd_touch", "lib": "__idf_espressif__esp_lcd_touch", "reqs": ["driver", "esp_lcd"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "espressif__esp_lcd_touch_cst816s": {"alias": "idf::espressif__esp_lcd_touch_cst816s", "target": "___idf_espressif__esp_lcd_touch_cst816s", "prefix": "idf", "dir": "D:/ESP-Project/ameter00/components/espressif__esp_lcd_touch_cst816s", "lib": "__idf_espressif__esp_lcd_touch_cst816s", "reqs": ["esp_lcd", "espressif__esp_lcd_touch"], "priv_reqs": [], "managed_reqs": ["espressif__esp_lcd_touch"], "managed_priv_reqs": [], "include_dirs": ["include"]}, "fb_gfx": {"alias": "idf::fb_gfx", "target": "___idf_fb_gfx", "prefix": "idf", "dir": "D:/ESP-Project/ameter00/components/fb_gfx", "lib": "__idf_fb_gfx", "reqs": ["espressif__esp32-camera"], "priv_reqs": [], "managed_reqs": ["espressif__esp32-camera"], "managed_priv_reqs": [], "include_dirs": [".", "./include"]}, "fusion_ameter_s3": {"alias": "idf::fusion_ameter_s3", "target": "___idf_fusion_ameter_s3", "prefix": "idf", "dir": "D:/ESP-Project/ameter00/components/fusion_ameter_s3", "lib": "__idf_fusion_ameter_s3", "reqs": ["driver", "spiffs", "espressif__button", "espressif__esp32-camera", "espressif__esp_codec_dev", "espressif__esp_lvgl_port"], "priv_reqs": ["fatfs", "esp_lcd", "espressif__esp_io_expander", "espressif__esp_io_expander_tca95xx_16bit", "espressif__esp_lcd_touch_cst816s"], "managed_reqs": ["espressif__button", "espressif__esp32-camera", "espressif__esp_codec_dev", "espressif__esp_lvgl_port"], "managed_priv_reqs": ["espressif__esp_io_expander", "espressif__esp_io_expander_tca95xx_16bit", "espressif__esp_lcd_touch_cst816s"], "include_dirs": ["include"]}, "i2cdev": {"alias": "idf::i2cdev", "target": "___idf_i2cdev", "prefix": "idf", "dir": "D:/ESP-Project/ameter00/components/i2cdev", "lib": "__idf_i2cdev", "reqs": ["driver", "freertos"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["."]}, "modules": {"alias": "idf::modules", "target": "___idf_modules", "prefix": "idf", "dir": "D:/ESP-Project/ameter00/components/modules", "lib": "__idf_modules", "reqs": ["bus", "esp-dl", "esp_http_server", "nvs_flash", "esp_adc", "esp_lcd", "esp_timer", "esp_wifi", "fb_gfx", "espressif__esp32-camera", "espressif__mdns"], "priv_reqs": [], "managed_reqs": ["espressif__esp32-camera", "espressif__mdns"], "managed_priv_reqs": [], "include_dirs": ["ai", "camera", "lcd", "led", "button", "web", "trace", "imu"]}, "pcf8563": {"alias": "idf::pcf8563", "target": "___idf_pcf8563", "prefix": "idf", "dir": "D:/ESP-Project/ameter00/components/pcf8563", "lib": "__idf_pcf8563", "reqs": ["i2cdev", "log"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["."]}, "espressif__esp-dsp": {"alias": "idf::espressif__esp-dsp", "target": "___idf_espressif__esp-dsp", "prefix": "idf", "dir": "D:/ESP-Project/ameter00/managed_components/espressif__esp-dsp", "lib": "__idf_espressif__esp-dsp", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["modules/dotprod/include", "modules/support/include", "modules/support/mem/include", "modules/windows/include", "modules/windows/hann/include", "modules/windows/blackman/include", "modules/windows/blackman_harris/include", "modules/windows/blackman_nuttall/include", "modules/windows/nuttall/include", "modules/windows/flat_top/include", "modules/iir/include", "modules/fir/include", "modules/math/include", "modules/math/add/include", "modules/math/sub/include", "modules/math/mul/include", "modules/math/addc/include", "modules/math/mulc/include", "modules/math/sqrt/include", "modules/matrix/mul/include", "modules/matrix/add/include", "modules/matrix/addc/include", "modules/matrix/mulc/include", "modules/matrix/sub/include", "modules/matrix/include", "modules/fft/include", "modules/dct/include", "modules/conv/include", "modules/common/include", "modules/matrix/mul/test/include", "modules/kalman/ekf/include", "modules/kalman/ekf_imu13states/include"]}, "espressif__esp-modbus": {"alias": "idf::espressif__esp-modbus", "target": "___idf_espressif__esp-modbus", "prefix": "idf", "dir": "D:/ESP-Project/ameter00/managed_components/espressif__esp-modbus", "lib": "__idf_espressif__esp-modbus", "reqs": ["driver", "lwip", "esp_timer"], "priv_reqs": ["esp_netif"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["D:/ESP-Project/ameter00/managed_components/espressif__esp-modbus/freemodbus/common/include"]}, "espressif__esp-sr": {"alias": "idf::espressif__esp-sr", "target": "___idf_espressif__esp-sr", "prefix": "idf", "dir": "D:/ESP-Project/ameter00/managed_components/espressif__esp-sr", "lib": "__idf_espressif__esp-sr", "reqs": ["json", "spiffs", "esp_partition"], "priv_reqs": ["spi_flash", "espressif__esp-dsp"], "managed_reqs": [], "managed_priv_reqs": ["espressif__esp-dsp"], "include_dirs": ["src/include", "esp-tts/esp_tts_chinese/include", "include/esp32s3"]}, "espressif__esp32_s3_eye": {"alias": "idf::espressif__esp32_s3_eye", "target": "___idf_espressif__esp32_s3_eye", "prefix": "idf", "dir": "D:/ESP-Project/ameter00/managed_components/espressif__esp32_s3_eye", "lib": "__idf_espressif__esp32_s3_eye", "reqs": ["driver", "spiffs", "espressif__button", "espressif__esp32-camera", "espressif__esp_codec_dev", "espressif__esp_lvgl_port"], "priv_reqs": ["fatfs", "esp_lcd"], "managed_reqs": ["espressif__button", "espressif__esp32-camera", "espressif__esp_codec_dev", "espressif__esp_lvgl_port"], "managed_priv_reqs": [], "include_dirs": ["include"]}, "espressif__esp_lvgl_port": {"alias": "idf::espressif__esp_lvgl_port", "target": "___idf_espressif__esp_lvgl_port", "prefix": "idf", "dir": "D:/ESP-Project/ameter00/managed_components/espressif__esp_lvgl_port", "lib": "__idf_espressif__esp_lvgl_port", "reqs": ["esp_lcd", "lvgl__lvgl"], "priv_reqs": [], "managed_reqs": ["lvgl__lvgl"], "managed_priv_reqs": [], "include_dirs": ["include"]}, "espressif__mdns": {"alias": "idf::espressif__mdns", "target": "___idf_espressif__mdns", "prefix": "idf", "dir": "D:/ESP-Project/ameter00/managed_components/espressif__mdns", "lib": "__idf_espressif__mdns", "reqs": ["lwip", "console", "esp_netif"], "priv_reqs": ["esp_timer", "esp_wifi"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "lvgl__lvgl": {"alias": "idf::lvgl__lvgl", "target": "___idf_lvgl__lvgl", "prefix": "idf", "dir": "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl", "lib": "__idf_lvgl__lvgl", "reqs": ["esp_timer"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["D:/ESP-Project/ameter00/managed_components/lvgl__lvgl", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/src", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/../", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/examples", "D:/ESP-Project/ameter00/managed_components/lvgl__lvgl/demos"]}}, "debug_prefix_map_gdbinit": "", "gdbinit_files": {"01_symbols": "D:/ESP-Project/ameter00/build/gdbinit/symbols", "02_prefix_map": "D:/ESP-Project/ameter00/build/gdbinit/prefix_map", "03_py_extensions": "D:/ESP-Project/ameter00/build/gdbinit/py_extensions", "04_connect": "D:/ESP-Project/ameter00/build/gdbinit/connect"}, "debug_arguments_openocd": "-f board/esp32s3-builtin.cfg"}