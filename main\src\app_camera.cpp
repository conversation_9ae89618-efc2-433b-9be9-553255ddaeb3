#include "app_camera.hpp"

extern "C" {
#include "system_monitor.h"
#include "error_handler.h"
#include "camera_power_manager.h"
}

#include "esp_log.h"
#include "esp_system.h"

#include "bsp/ameter_s3.h"

#include "lvgl.h"
#include "ui/ui.h"

#include "esp_io_expander_tca95xx_16bit.h"


extern esp_io_expander_handle_t io_expander;

const static char TAG[] = "App/Camera";


static void task(AppCamera *self)
{
	ESP_LOGW(TAG, "Camera task start - Handle: %p, stopped=%d", xTaskGetCurrentTaskHandle(), self->stopped);

	// Register task for monitoring with longer timeout for camera operations
	MONITOR_REGISTER_CURRENT_TASK(30000); // 30 second timeout for camera

    //camera_img.header.always_zero = 0;
    //camera_img.header.w = 240;
    //camera_img.header.h = 240;
    //camera_img.data_size = 115200;
    //camera_img.header.cf = LV_IMG_CF_TRUE_COLOR;  //LV_IMG_CF_RAW  LV_IMG_CF_TRUE_COLOR_ALPHA

    camera_fb_t *frame;

    while (!self->stopped)
    {
    	if (self->queue_o == nullptr){
    		vTaskDelay(40);
    		// Only log occasionally to avoid log flooding
    		static uint32_t queue_null_counter = 0;
    		queue_null_counter++;
    		if (queue_null_counter % 25 == 0) { // Log every 25 iterations (~1 second)
    			ESP_LOGW(TAG, "Camera queue is null, waiting... (counter: %u)", queue_null_counter);
    		}
    		continue;
    	}

        // Only try to get frames if camera is initialized
        if (self->camera_initialized) {
            frame = esp_camera_fb_get();
            if (frame){
                // Use timeout instead of portMAX_DELAY to allow task to respond to stop signal
                if (xQueueSend(self->queue_o, &frame, 100) != pdTRUE) {
                    ESP_LOGW(TAG, "Failed to send frame to queue (timeout)");
                }
                esp_camera_fb_return(frame);

                // Send heartbeat to monitor
                MONITOR_HEARTBEAT();

                // Reset error count on successful capture
                self->stop_count = 0;
            }
            else{
                self->stop_count++;
                if (self->stop_count <= 5) {  // Only log first few errors
                    ESP_LOGW(TAG, "Camera frame capture failed (count: %u)", self->stop_count);
                }

                // Try camera reset after several failures
                if (self->stop_count == 8) {
                    ESP_LOGW(TAG, "Multiple frame capture failures, attempting camera reset");
                    esp_err_t reset_err = camera_reset();
                    if (reset_err == ESP_OK) {
                        ESP_LOGI(TAG, "Camera reset successful, waiting for stabilization");
                        vTaskDelay(pdMS_TO_TICKS(300)); // Wait longer after reset
                    } else {
                        ESP_LOGE(TAG, "Camera reset failed");
                    }
                }

                // Only report as error if it's persistent after reset attempt
                if (self->stop_count > 15) {
                    ERROR_REPORT(ERROR_SEVERITY_WARNING, ERROR_CATEGORY_HARDWARE, ESP_FAIL, "Persistent camera frame capture failure after reset");
                    self->stop_count = 0;  // Reset to avoid spam
                }
            }
        } else {
            // Camera not initialized - check if we should exit task
            camera_power_state_t power_state = camera_power_get_state();
            if (power_state != CAMERA_POWER_ON) {
                ESP_LOGI(TAG, "Camera power is off, exiting task");
                break; // Exit the task loop
            }
            ESP_LOGW(TAG, "Camera not initialized but power is on, waiting...");
            vTaskDelay(1000); // Wait longer if camera not initialized
        }

        vTaskDelay(5); // Reduced delay for faster frame rate

        // Send heartbeat even if no frame (task is still alive)
        MONITOR_HEARTBEAT();

    }
    vTaskDelay(5);
    ESP_LOGW(TAG, "Camera task Stopped - Handle: %p", xTaskGetCurrentTaskHandle());

    // Clear task handle before deleting
    if (self->camera_task_handle != NULL) {
        ESP_LOGI(TAG, "Clearing camera task handle: %p", self->camera_task_handle);
        self->camera_task_handle = NULL;
    }
    ESP_LOGI(TAG, "Camera task deleting itself");
    vTaskDelete(NULL);
}

AppCamera::AppCamera(const pixformat_t pixel_fromat,
                     const framesize_t frame_size,
                     const uint8_t fb_count,
                     QueueHandle_t queue_o) : Frame(nullptr, queue_o, nullptr),stopped(true),stop_count(0),camera_task_handle(NULL)
{
    ESP_LOGI(TAG, "Camera module is %s", CAMERA_MODULE_NAME);

    // Store configuration for later initialization
    this->pixel_format = pixel_fromat;
    this->frame_size = frame_size;
    this->fb_count = fb_count;
    this->camera_initialized = false;

    // Camera will be initialized during boot sequence
    ESP_LOGI(TAG, "Camera configuration stored, will initialize during boot sequence");
}

esp_err_t AppCamera::init_camera() {
    if (camera_initialized) {
        ESP_LOGI(TAG, "Camera already initialized");
        return ESP_OK;
    }

    ESP_LOGI(TAG, "Initializing camera hardware...");

    camera_config_t config;
    config.ledc_channel = LEDC_CHANNEL_0;
    config.ledc_timer = LEDC_TIMER_0;
    config.pin_d0 = CAMERA_PIN_D0;
    config.pin_d1 = CAMERA_PIN_D1;
    config.pin_d2 = CAMERA_PIN_D2;
    config.pin_d3 = CAMERA_PIN_D3;
    config.pin_d4 = CAMERA_PIN_D4;
    config.pin_d5 = CAMERA_PIN_D5;
    config.pin_d6 = CAMERA_PIN_D6;
    config.pin_d7 = CAMERA_PIN_D7;
    config.pin_xclk = CAMERA_PIN_XCLK;
    config.pin_pclk = CAMERA_PIN_PCLK;
    config.pin_vsync = CAMERA_PIN_VSYNC;
    config.pin_href = CAMERA_PIN_HREF;
    config.pin_sccb_sda = -1;
    config.pin_sccb_scl = -1;
    config.sccb_i2c_port = 1;
    config.pin_pwdn = -1;  // Power down controlled by camera_power_manager
    config.pin_reset = -1; // Reset controlled by camera_power_manager
    config.xclk_freq_hz = 16000000;  // Increase to 16MHz for better OV3660 support
    config.pixel_format = pixel_format;
    config.frame_size = frame_size;
    config.jpeg_quality = 12;
    config.fb_count = fb_count;
    config.fb_location = CAMERA_FB_IN_PSRAM;
    config.grab_mode = CAMERA_GRAB_WHEN_EMPTY;

    ESP_LOGW(TAG, "esp_camera_init............................");

    // Wait for camera power to stabilize - increased delay for better stability
    vTaskDelay(pdMS_TO_TICKS(100));

    // camera init
    esp_err_t err = esp_camera_init(&config);
    if (err != ESP_OK) {
        ESP_LOGE(TAG, "Camera init failed with error 0x%x", err);
        return err;
    }

    // Additional stabilization delay after init
    vTaskDelay(pdMS_TO_TICKS(50));

    sensor_t *s = esp_camera_sensor_get();
    if (s == NULL) {
        ESP_LOGE(TAG, "Failed to get camera sensor");
        return ESP_FAIL;
    }

    ESP_LOGW(TAG, "camera_sensor=%d...........................", s->id.PID);

    // Configure sensor settings
    s->set_hmirror(s, 1);

    // initial sensors are flipped vertically and colors are a bit saturated
    if (s->id.PID == OV3660_PID) {
        s->set_brightness(s, 1);  // up the brightness just a bit
        s->set_vflip(s, 1);
    }
    s->set_saturation(s, 1);
    s->set_sharpness(s, 2);
    s->set_awb_gain(s, 2);

    camera_initialized = true;
    ESP_LOGI(TAG, "Camera hardware initialized successfully");
    return ESP_OK;
}

void AppCamera::deinit_camera() {
    if (camera_initialized) {
        ESP_LOGI(TAG, "Deinitializing camera hardware");
        esp_camera_deinit();
        camera_initialized = false;
        ESP_LOGI(TAG, "Camera hardware deinitialized");
    }
}

bool AppCamera::camera_health_check() {
    if (!camera_initialized) {
        ESP_LOGW(TAG, "Camera not initialized for health check");
        return false;
    }

    // Try to get a test frame to verify camera is working
    camera_fb_t *test_frame = esp_camera_fb_get();
    if (test_frame) {
        esp_camera_fb_return(test_frame);
        ESP_LOGI(TAG, "Camera health check passed");
        return true;
    } else {
        ESP_LOGW(TAG, "Camera health check failed - no frame available");
        return false;
    }
}



void AppCamera::run()
{
	ESP_LOGW(TAG, "AppCamera task running............stop_count=%d",stop_count);

	// Check if camera power is on before starting
	camera_power_state_t power_state = camera_power_get_state();
	if (power_state != CAMERA_POWER_ON) {
		ESP_LOGW(TAG, "Camera power is not on, cannot start camera task");
		return;
	}

	// Initialize camera hardware if not already done
	if (!camera_initialized) {
		ESP_LOGW(TAG, "Camera not initialized during boot, initializing now...");
		esp_err_t err = init_camera();
		if (err != ESP_OK) {
			ESP_LOGE(TAG, "Failed to initialize camera hardware");
			return;
		}
	} else {
		ESP_LOGI(TAG, "Camera hardware already initialized, starting task");

		// Add camera reset to ensure stable state after multiple on/off cycles
		ESP_LOGW(TAG, "Performing camera reset to ensure stable state");
		esp_err_t reset_err = camera_reset();
		if (reset_err != ESP_OK) {
			ESP_LOGW(TAG, "Camera reset failed, but continuing with task start");
		} else {
			// Wait for camera to stabilize after reset
			vTaskDelay(pdMS_TO_TICKS(200));
			ESP_LOGI(TAG, "Camera reset completed, hardware should be stable");

			// Perform health check after reset
			if (!camera_health_check()) {
				ESP_LOGW(TAG, "Camera health check failed after reset, but continuing");
			}
		}
	}

	if (camera_task_handle == NULL) {
		// Create task if it doesn't exist - INCREASED STACK SIZE TO FIX TEARING
		ESP_LOGI(TAG, "Creating camera task");
		stopped = false; // Set stopped to false before creating task
		stop_count = 0; // Reset stop count
		BaseType_t result = xTaskCreatePinnedToCore((TaskFunction_t)task, TAG, 6 * 1024, this, 3, &camera_task_handle, 0);
		if (result == pdPASS) {
			ESP_LOGW(TAG, "Camera task created - Handle: %p", camera_task_handle);
		} else {
			ESP_LOGE(TAG, "Failed to create camera task");
			camera_task_handle = NULL;
		}
	} else {
		// Task already exists, just ensure it's not stopped
		stopped = false;
		stop_count = 0; // Reset stop count
		ESP_LOGW(TAG, "Camera task already running - Handle: %p", camera_task_handle);
	}

}

void AppCamera::stop()
{
	stopped = true;
	ESP_LOGW(TAG, "AppCamera task stopping............");

	// Camera hardware remains initialized, only task stops
	// Power management is handled by camera_power_manager via PD pin

	// Give more time for task to stop gracefully and ensure it's fully stopped
	if (camera_task_handle != NULL) {
		TaskHandle_t temp_handle = camera_task_handle;

		// Wait for task to process stop signal and exit cleanly
		int wait_count = 0;
		while (eTaskGetState(temp_handle) != eDeleted && wait_count < 10) {
			vTaskDelay(50); // Wait in smaller increments
			wait_count++;
			if (wait_count % 3 == 0) {
				ESP_LOGW(TAG, "Camera task still running, waiting... (%d/10)", wait_count);
			}
		}

		if (eTaskGetState(temp_handle) == eDeleted) {
			ESP_LOGI(TAG, "Camera task stopped successfully");
		} else {
			ESP_LOGW(TAG, "Camera task did not stop gracefully, forcing cleanup");
		}

		// Task handle will be cleared by the task itself before deletion
		camera_task_handle = NULL;
		ESP_LOGI(TAG, "Camera task stop completed");
	}
}


AppCamera::~AppCamera(){
	ESP_LOGW(TAG, "Camera is killed........");
}





