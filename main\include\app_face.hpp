#pragma once

#include "sdkconfig.h"

#include "human_face_detect_msr01.hpp"
#include "human_face_detect_mnp01.hpp"
#include "face_recognition_tool.hpp"
#if CONFIG_MFN_V1
#if CONFIG_S8
#include "face_recognition_112_v1_s8.hpp"
#elif CONFIG_S16
#include "face_recognition_112_v1_s16.hpp"
#endif
#endif

#include "__base__.hpp"
#include "app_camera.hpp"


typedef enum
{
    FACE_IDLE = 0,
    FACE_ENROLL = 1,
    FACE_RECOGNIZE = 2,
    FACE_DELETE = 3,
} face_action_t;

class AppFace : public Observer, public Frame
{

public:
    HumanFaceDetectMSR01 detector;
    HumanFaceDetectMNP01 detector2;

#if CONFIG_MFN_V1
#if CONFIG_S8
    FaceRecognition112V1S8 *recognizer;
#elif CONFIG_S16
    FaceRecognition112V1S16 *recognizer;
#endif
#endif

    face_info_t recognize_result;
    face_action_t state;
    face_action_t state_previous;

    bool switch_on;

    uint8_t frame_count;

    AppFace(QueueHandle_t queue_i = nullptr,
            void (*callback)(camera_fb_t *) = esp_camera_fb_return);
    ~AppFace();

    void update();
    void run();
    void stop();
    void del_user();

    bool stopped;

    // Task handle for dynamic task creation/deletion
    TaskHandle_t face_task_handle;

    StaticTask_t TaskTCB_Face;
    StackType_t xStackFace[ 4096 ];
    TaskHandle_t xHandle_Face = NULL;

};

static char s[10];

static const char* nvs_key(char * name, int x)
{
	sprintf(s, "%s%d", name, x);
	return s;
}



