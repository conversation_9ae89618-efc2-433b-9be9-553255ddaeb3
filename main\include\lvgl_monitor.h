#pragma once

#include "esp_err.h"
#include "lvgl.h"

#ifdef __cplusplus
extern "C" {
#endif

// Macros to replace bsp_display_lock/unlock with monitoring versions
// Note: These macros must be included AFTER bsp headers to avoid conflicts
#ifdef BSP_DISPLAY_LOCK_TIMEOUT_MS
#undef bsp_display_lock
#undef bsp_display_unlock
#define bsp_display_lock(timeout) lvgl_monitor_lock(timeout)
#define bsp_display_unlock() lvgl_monitor_unlock()
#endif

/**
 * @brief Initialize LVGL monitoring
 * @return ESP_OK on success
 */
esp_err_t lvgl_monitor_init(void);

/**
 * @brief Wrapper for bsp_display_lock with monitoring
 * @param timeout_ms Timeout in milliseconds
 * @return true if lock acquired, false otherwise
 */
bool lvgl_monitor_lock(uint32_t timeout_ms);

/**
 * @brief Wrapper for bsp_display_unlock with monitoring
 */
void lvgl_monitor_unlock(void);

/**
 * @brief Check if LVGL is currently blocking
 * @return true if blocking detected
 */
bool lvgl_monitor_is_blocking(void);

/**
 * @brief Get LVGL statistics
 * @param max_lock_time_ms Maximum lock time recorded
 * @param total_locks Total number of locks
 * @param blocking_count Number of blocking events
 * @return ESP_OK on success
 */
esp_err_t lvgl_monitor_get_stats(uint32_t* max_lock_time_ms, uint32_t* total_locks, uint32_t* blocking_count);

#ifdef __cplusplus
}
#endif
