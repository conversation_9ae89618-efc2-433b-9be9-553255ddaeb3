# ninja log v6
34	200	7733002705243698	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_crc.c.obj	436449df363f5244
25	206	7733002705153702	esp-idf/log/CMakeFiles/__idf_log.dir/src/noos/log_lock.c.obj	134454cabe635648
28	269	7733002705183697	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_sys.c.obj	696c9e24926989cc
49	273	7733002705393749	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_gpio.c.obj	74846b3885d84a
37	278	7733002705273714	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_uart.c.obj	3b572fdbe1775a9c
18	281	7733002705083694	esp-idf/log/CMakeFiles/__idf_log.dir/src/noos/log_timestamp.c.obj	d74c02f38916279d
52	285	7733002705433758	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_longjmp.S.obj	daa69dca3de6c4fa
45	290	7733002705353752	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_efuse.c.obj	8b9bfdb33f33615f
56	294	7733002705468856	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_systimer.c.obj	7996971523113ac3
61	300	7733002705508857	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_wdt.c.obj	e8e517c4b1012070
22	304	7733002705123709	esp-idf/log/CMakeFiles/__idf_log.dir/src/log_timestamp_common.c.obj	65af41a736d03c90
41	312	7733002705313750	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_spiflash.c.obj	28f9aa808991bdf7
68	316	7733002705593377	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_cache_writeback_esp32s3.S.obj	e8564437df3cf7fa
64	320	7733002705553382	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_cache_esp32s2_esp32s3.c.obj	1d605f9074f22f8e
71	323	7733002705623386	esp-idf/esp_common/CMakeFiles/__idf_esp_common.dir/src/esp_err_to_name.c.obj	a53c425d4317a3b9
80	328	7733002705703384	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/esp_cpu_intr.c.obj	c4a924ffb55cacb7
111	339	7733002706023422	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/chip_info.c.obj	424a77130002567b
76	343	7733002705663379	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/cpu.c.obj	d68b1363af8d1640
84	347	7733002705743382	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/esp_memory_utils.c.obj	f5066d2854852e51
88	351	7733002705783376	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/cpu_region_protect.c.obj	9d5c139ec12cdd93
31	387	7733002705213699	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_print.c.obj	c781562f311874cc
95	440	7733002705863373	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/rtc_clk_init.c.obj	9a659d9660f2a768
200	504	7733002706908791	esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/esp_err.c.obj	9502e2705b0245a1
206	512	7733002706968800	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32s3/esp_efuse_table.c.obj	4fdbce071f39fa20
104	516	7733002705943387	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/rtc_sleep.c.obj	b349062035296f0a
269	522	7733002707599051	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32s3/esp_efuse_fields.c.obj	8a398040f58e988b
108	526	7733002705983427	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/rtc_time.c.obj	b251cc4e1de13841
285	530	7733002707759044	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_fields.c.obj	79e46aa83cee7990
100	556	7733002705903373	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/rtc_init.c.obj	45c03d57454b7ed6
320	565	7733002708109098	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_mem.c.obj	beb2c651bf58697b
323	574	7733002708139099	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_random.c.obj	421d64092fdbd5f0
274	585	7733002707639046	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32s3/esp_efuse_rtc_calib.c.obj	cfebc4f49a99115c
91	588	7733002705823385	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/rtc_clk.c.obj	72b3eab4d729981a
316	592	7733002708069096	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_clock_init.c.obj	ff6184f69964e66b
328	610	7733002708184212	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_efuse.c.obj	35cb0ab5342a53c2
281	617	7733002707719044	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_api.c.obj	224fe8596314ee5e
347	642	7733002708384258	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_random_esp32s3.c.obj	680e48349618b856
339	648	7733002708294210	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/flash_encrypt.c.obj	d12598243aedacb1
278	653	7733002707689043	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32s3/esp_efuse_utility.c.obj	6a45cae111c3ed07
304	657	7733002707949042	esp-idf/log/liblog.a	2e6f9b49f642914e
343	686	7733002708344205	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/secure_boot.c.obj	71eb204e875e94fd
300	722	7733002707909047	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_common.c.obj	45199ba6f900b02d
312	726	7733002708029111	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_common_loader.c.obj	898675f44832e143
294	741	7733002707839048	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/efuse_controller/keys/with_key_purposes/esp_efuse_api_key.c.obj	5a24b3c95d9aeca
526	747	7733002710169870	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_clock_loader.c.obj	4c03211817f1c8fa
387	762	7733002708779416	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/flash_qio_mode.c.obj	a91057d0ab881f22
290	766	7733002707809045	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_utility.c.obj	f4dff40e54039296
512	780	7733002710024815	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/flash_partitions.c.obj	71a2e05077db0572
556	787	7733002710469974	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_console_loader.c.obj	fb0c726f1234f737
565	799	7733002710555100	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32s3/bootloader_sha.c.obj	bf82c1fc1bad013f
440	803	7733002709314527	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/bootloader_flash_config_esp32s3.c.obj	f4a39926a1097f4f
530	810	7733002710209933	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_console.c.obj	d68e8bc2cd4a1a34
574	843	7733002710645101	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32s3/bootloader_soc.c.obj	8430381561ad18f8
588	854	7733002710795164	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_panic.c.obj	d69f8098af35aa93
642	858	7733002711330277	esp-idf/hal/CMakeFiles/__idf_hal.dir/mpu_hal.c.obj	4ebf61e59c9d6597
592	862	7733002710835161	esp-idf/esp_bootloader_format/CMakeFiles/__idf_esp_bootloader_format.dir/esp_bootloader_desc.c.obj	4099728cc2de4b58
617	866	7733002711080286	esp-idf/hal/CMakeFiles/__idf_hal.dir/hal_utils.c.obj	3a91eef47586c27a
522	870	7733002710124814	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_init.c.obj	b359d04756d10c0
649	881	7733002711390324	esp-idf/hal/CMakeFiles/__idf_hal.dir/efuse_hal.c.obj	567ccc902484bed6
741	910	7733002712315671	esp-idf/soc/CMakeFiles/__idf_soc.dir/lldesc.c.obj	207eff9018dd310
653	918	7733002711440327	esp-idf/hal/CMakeFiles/__idf_hal.dir/esp32s3/efuse_hal.c.obj	cad9b16a5814244e
766	922	7733002712560818	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/gpio_periph.c.obj	f736bb637bbcd22f
351	952	7733002708424254	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/bootloader_flash.c.obj	75b34e23913f7591
762	956	7733002712520826	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/interrupts.c.obj	80825aa16ac878b1
610	960	7733002711000281	esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/spi_flash_wrap.c.obj	e76de02ef6a7824a
585	964	7733002710765108	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32s3/bootloader_esp32s3.c.obj	2046b4f531bb9bbc
747	967	7733002712385718	esp-idf/soc/CMakeFiles/__idf_soc.dir/dport_access_common.c.obj	3a6dbab71a6c166e
799	974	7733002712890886	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/dedic_gpio_periph.c.obj	8204b2c1a85d6a5f
803	978	7733002712935928	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/gdma_periph.c.obj	b2b0881496f67997
780	982	7733002712710834	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/uart_periph.c.obj	7d94c7200f8390f0
787	987	7733002712770832	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/adc_periph.c.obj	420765462000ec64
504	1000	7733002709944764	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_utility.c.obj	b345ea88d92fe2c7
810	1008	7733002713005992	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/spi_periph.c.obj	fe5c91a05d058d73
858	1012	7733002713486033	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/rmt_periph.c.obj	a4f11d27328ecb9f
686	1012	7733002711760527	esp-idf/hal/CMakeFiles/__idf_hal.dir/mmu_hal.c.obj	487a6866d1f71c6e
862	1013	7733002713531124	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/sdm_periph.c.obj	e13db6f4837e8fbe
870	1013	7733002713611124	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/i2c_periph.c.obj	4026e9aab31e4893
866	1013	7733002713561122	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/i2s_periph.c.obj	40284bbb01abebd4
854	1013	7733002713446035	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/pcnt_periph.c.obj	84012aff82ca112e
722	1013	7733002712130585	esp-idf/hal/CMakeFiles/__idf_hal.dir/cache_hal.c.obj	9cc360269fd8374f
881	1013	7733002713716224	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/temperature_sensor_periph.c.obj	dd532750315bc293
843	1017	7733002713335981	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/ledc_periph.c.obj	7bbeef1c2add7d9e
918	1049	7733002714086221	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/lcd_periph.c.obj	6d31bb7f7069bdf6
910	1050	7733002714006224	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/timer_periph.c.obj	4e6be6cda79bc659
922	1051	7733002714126219	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/mcpwm_periph.c.obj	1635da1633964058
657	1051	7733002711480329	esp-idf/esp_rom/libesp_rom.a	6b26f9c270ca76d6
960	1058	7733002714511441	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/touch_sensor_periph.c.obj	2740c76df2eb5920
1000	1059	7733002715411751	project_elf_src_esp32s3.c	ce3d8934f2fa79
1000	1059	7733002715411751	D:/ESP-Project/ameter00/build/bootloader/project_elf_src_esp32s3.c	ce3d8934f2fa79
956	1062	7733002714471443	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/sdmmc_periph.c.obj	b98a73673ecef8bd
952	1062	7733002714431440	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/mpi_periph.c.obj	9efbb5a8a81e9dff
974	1067	7733002714646540	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/usb_dwc_periph.c.obj	31c882736d380b66
516	1071	7733002710074810	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp_image_format.c.obj	370831e2ad67f05f
983	1072	7733002714736533	esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir/eri.c.obj	79e4cad8a498e73d
967	1073	7733002714576537	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/wdt_periph.c.obj	dea395cee01d255d
964	1073	7733002714546543	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/twai_periph.c.obj	8db04639248870f7
978	1079	7733002714686549	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/rtc_io_periph.c.obj	aa529b841bcfd12a
987	1085	7733002714776549	esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir/xt_trax.c.obj	a97079c71e808c96
1059	1102	7733002715491753	CMakeFiles/bootloader.elf.dir/project_elf_src_esp32s3.c.obj	be51c78d2e3ed124
1008	1109	7733002714981696	esp-idf/main/CMakeFiles/__idf_main.dir/bootloader_start.c.obj	51ef5d58324ba3e2
1051	1122	7733002715421752	esp-idf/esp_common/libesp_common.a	673f691c3ab2cf03
1122	1191	7733002716122001	esp-idf/esp_hw_support/libesp_hw_support.a	44361a2320d1181b
1191	1276	7733002716812228	esp-idf/esp_system/libesp_system.a	70e1c5f00836c749
1276	1349	7733002717657524	esp-idf/efuse/libefuse.a	e03851205b228822
726	1358	7733002712170583	esp-idf/micro-ecc/CMakeFiles/__idf_micro-ecc.dir/uECC_verify_antifault.c.obj	b896211a19a6b744
1349	1430	7733002718392683	esp-idf/bootloader_support/libbootloader_support.a	4f568d5a509bbba5
1430	1483	7733002719208302	esp-idf/esp_bootloader_format/libesp_bootloader_format.a	689c529dbade075e
1483	1540	7733002719735516	esp-idf/spi_flash/libspi_flash.a	5644ef8c321e7b25
1540	1602	7733002720310733	esp-idf/hal/libhal.a	206071984c76ed05
1602	1666	7733002720925951	esp-idf/micro-ecc/libmicro-ecc.a	7d43932d695fd3c8
1666	1749	7733002721562061	esp-idf/soc/libsoc.a	a59b13655f0d6f80
1749	1806	7733002722397533	esp-idf/xtensa/libxtensa.a	147a20c99f0d872b
1806	1859	7733002722962684	esp-idf/main/libmain.a	b898d7ce3eca1040
1859	1973	7733002723497834	bootloader.elf	4b685c2e3eceab7c
1974	2163	7733002726491437	.bin_timestamp	25ba514d788086cf
1974	2163	7733002726491437	D:/ESP-Project/ameter00/build/bootloader/.bin_timestamp	25ba514d788086cf
2163	2222	7733002726536539	esp-idf/esptool_py/CMakeFiles/bootloader_check_size	3f52659c9c071d14
2163	2222	7733002726536539	D:/ESP-Project/ameter00/build/bootloader/esp-idf/esptool_py/CMakeFiles/bootloader_check_size	3f52659c9c071d14
12	102	7733007550987541	esp-idf/esptool_py/CMakeFiles/bootloader_check_size	3f52659c9c071d14
12	102	7733007550987541	D:/ESP-Project/ameter00/build/bootloader/esp-idf/esptool_py/CMakeFiles/bootloader_check_size	3f52659c9c071d14
9	82	7733011492612496	esp-idf/esptool_py/CMakeFiles/bootloader_check_size	3f52659c9c071d14
9	82	7733011492612496	D:/ESP-Project/ameter00/build/bootloader/esp-idf/esptool_py/CMakeFiles/bootloader_check_size	3f52659c9c071d14
10	78	7733017052570524	esp-idf/esptool_py/CMakeFiles/bootloader_check_size	3f52659c9c071d14
10	78	7733017052570524	D:/ESP-Project/ameter00/build/bootloader/esp-idf/esptool_py/CMakeFiles/bootloader_check_size	3f52659c9c071d14
8	69	7733018919833971	esp-idf/esptool_py/CMakeFiles/bootloader_check_size	3f52659c9c071d14
8	69	7733018919833971	D:/ESP-Project/ameter00/build/bootloader/esp-idf/esptool_py/CMakeFiles/bootloader_check_size	3f52659c9c071d14
7	64	7733019370188779	esp-idf/esptool_py/CMakeFiles/bootloader_check_size	3f52659c9c071d14
7	64	7733019370188779	D:/ESP-Project/ameter00/build/bootloader/esp-idf/esptool_py/CMakeFiles/bootloader_check_size	3f52659c9c071d14
10	74	7733052027152244	esp-idf/esptool_py/CMakeFiles/bootloader_check_size	3f52659c9c071d14
10	74	7733052027152244	D:/ESP-Project/ameter00/build/bootloader/esp-idf/esptool_py/CMakeFiles/bootloader_check_size	3f52659c9c071d14
12	96	7733064244891034	esp-idf/esptool_py/CMakeFiles/bootloader_check_size	3f52659c9c071d14
12	96	7733064244891034	D:/ESP-Project/ameter00/build/bootloader/esp-idf/esptool_py/CMakeFiles/bootloader_check_size	3f52659c9c071d14
9	83	7733065377616099	esp-idf/esptool_py/CMakeFiles/bootloader_check_size	3f52659c9c071d14
9	83	7733065377616099	D:/ESP-Project/ameter00/build/bootloader/esp-idf/esptool_py/CMakeFiles/bootloader_check_size	3f52659c9c071d14
10	77	7733067568643190	esp-idf/esptool_py/CMakeFiles/bootloader_check_size	3f52659c9c071d14
10	77	7733067568643190	D:/ESP-Project/ameter00/build/bootloader/esp-idf/esptool_py/CMakeFiles/bootloader_check_size	3f52659c9c071d14
7	66	7733067773135723	esp-idf/esptool_py/CMakeFiles/bootloader_check_size	3f52659c9c071d14
7	66	7733067773135723	D:/ESP-Project/ameter00/build/bootloader/esp-idf/esptool_py/CMakeFiles/bootloader_check_size	3f52659c9c071d14
9	76	7733069182514820	esp-idf/esptool_py/CMakeFiles/bootloader_check_size	3f52659c9c071d14
9	76	7733069182514820	D:/ESP-Project/ameter00/build/bootloader/esp-idf/esptool_py/CMakeFiles/bootloader_check_size	3f52659c9c071d14
7	65	7733069942428715	esp-idf/esptool_py/CMakeFiles/bootloader_check_size	3f52659c9c071d14
7	65	7733069942428715	D:/ESP-Project/ameter00/build/bootloader/esp-idf/esptool_py/CMakeFiles/bootloader_check_size	3f52659c9c071d14
