#include "system_monitor.h"
#include "memory_manager.h"
#include "esp_log.h"
#include "esp_heap_caps.h"
#include "esp_system.h"
#include "esp_task_wdt.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/semphr.h"
#include <string.h>

static const char* TAG = "SYS_MON";

// Global system monitor state
static struct {
    monitor_config_t config;
    task_monitor_t* tasks;
    SemaphoreHandle_t mutex;
    TaskHandle_t monitor_task_handle;
    bool initialized;
    system_stats_t stats;
    uint32_t start_time;
} sys_mon = {0};

// Forward declarations
static void monitor_task(void* pvParameters);
static void check_task_health(void);
static void check_memory_health(void);
static void update_system_stats(void);

esp_err_t system_monitor_init(const monitor_config_t* config) {
    if (sys_mon.initialized) {
        ESP_LOGW(TAG, "System monitor already initialized");
        return ESP_OK;
    }

    if (!config) {
        ESP_LOGE(TAG, "Invalid configuration");
        return ESP_ERR_INVALID_ARG;
    }

    // Copy configuration
    memcpy(&sys_mon.config, config, sizeof(monitor_config_t));

    // Initialize mutex
    sys_mon.mutex = xSemaphoreCreateMutex();
    if (!sys_mon.mutex) {
        ESP_LOGE(TAG, "Failed to create mutex");
        return ESP_ERR_NO_MEM;
    }

    // Allocate task monitoring array in PSRAM to save internal RAM
    if (config->enable_task_monitoring && config->max_monitored_tasks > 0) {
        size_t tasks_size = config->max_monitored_tasks * sizeof(task_monitor_t);
        // Try PSRAM first, fallback to internal RAM if needed
        sys_mon.tasks = heap_caps_malloc(tasks_size, MALLOC_CAP_SPIRAM);
        if (!sys_mon.tasks) {
            ESP_LOGW(TAG, "PSRAM allocation failed, trying internal RAM");
            sys_mon.tasks = INTERNAL_MALLOC(tasks_size);
        }
        if (!sys_mon.tasks) {
            ESP_LOGE(TAG, "Failed to allocate task monitoring array");
            vSemaphoreDelete(sys_mon.mutex);
            return ESP_ERR_NO_MEM;
        }
        memset(sys_mon.tasks, 0, tasks_size);
        ESP_LOGI(TAG, "Task monitoring array allocated in %s",
                 heap_caps_get_allocated_size(sys_mon.tasks) ? "PSRAM" : "Internal RAM");
    }

    // Initialize statistics
    memset(&sys_mon.stats, 0, sizeof(system_stats_t));
    sys_mon.start_time = xTaskGetTickCount();

    // Create monitor task
    BaseType_t ret = xTaskCreatePinnedToCore(
        monitor_task,
        "sys_monitor",
        4096,
        NULL,
        configMAX_PRIORITIES - 2,  // High priority
        &sys_mon.monitor_task_handle,
        1  // Pin to core 1
    );

    if (ret != pdPASS) {
        ESP_LOGE(TAG, "Failed to create monitor task");
        if (sys_mon.tasks) SAFE_FREE(sys_mon.tasks);
        vSemaphoreDelete(sys_mon.mutex);
        return ESP_ERR_NO_MEM;
    }

    sys_mon.initialized = true;
    ESP_LOGI(TAG, "System monitor initialized");

    return ESP_OK;
}

esp_err_t system_monitor_register_task(TaskHandle_t handle, const char* name, uint32_t timeout_ms) {
    if (!sys_mon.initialized || !sys_mon.config.enable_task_monitoring) {
        return ESP_ERR_INVALID_STATE;
    }

    if (!handle || !name) {
        return ESP_ERR_INVALID_ARG;
    }

    if (xSemaphoreTake(sys_mon.mutex, pdMS_TO_TICKS(100)) == pdTRUE) {
        // Find empty slot
        for (uint32_t i = 0; i < sys_mon.config.max_monitored_tasks; i++) {
            if (sys_mon.tasks[i].handle == NULL) {
                sys_mon.tasks[i].handle = handle;
                sys_mon.tasks[i].name = name;
                sys_mon.tasks[i].timeout_ms = timeout_ms;
                sys_mon.tasks[i].last_heartbeat = xTaskGetTickCount();
                sys_mon.tasks[i].enabled = true;
                sys_mon.tasks[i].timeout_count = 0;
                
                ESP_LOGI(TAG, "Registered task '%s' for monitoring (timeout: %u ms)", name, timeout_ms);
                xSemaphoreGive(sys_mon.mutex);
                return ESP_OK;
            }
        }
        
        xSemaphoreGive(sys_mon.mutex);
        ESP_LOGW(TAG, "Task monitoring table full");
        return ESP_ERR_NO_MEM;
    }

    return ESP_ERR_TIMEOUT;
}

esp_err_t system_monitor_unregister_task(TaskHandle_t handle) {
    if (!sys_mon.initialized || !sys_mon.config.enable_task_monitoring) {
        return ESP_ERR_INVALID_STATE;
    }

    if (xSemaphoreTake(sys_mon.mutex, pdMS_TO_TICKS(100)) == pdTRUE) {
        for (uint32_t i = 0; i < sys_mon.config.max_monitored_tasks; i++) {
            if (sys_mon.tasks[i].handle == handle) {
                ESP_LOGI(TAG, "Unregistered task '%s' from monitoring", sys_mon.tasks[i].name);
                memset(&sys_mon.tasks[i], 0, sizeof(task_monitor_t));
                xSemaphoreGive(sys_mon.mutex);
                return ESP_OK;
            }
        }
        xSemaphoreGive(sys_mon.mutex);
    }

    return ESP_ERR_NOT_FOUND;
}

esp_err_t system_monitor_heartbeat(void) {
    if (!sys_mon.initialized || !sys_mon.config.enable_task_monitoring) {
        return ESP_ERR_INVALID_STATE;
    }

    TaskHandle_t current_task = xTaskGetCurrentTaskHandle();
    
    if (xSemaphoreTake(sys_mon.mutex, pdMS_TO_TICKS(10)) == pdTRUE) {
        for (uint32_t i = 0; i < sys_mon.config.max_monitored_tasks; i++) {
            if (sys_mon.tasks[i].handle == current_task) {
                sys_mon.tasks[i].last_heartbeat = xTaskGetTickCount();
                xSemaphoreGive(sys_mon.mutex);
                return ESP_OK;
            }
        }
        xSemaphoreGive(sys_mon.mutex);
    }

    return ESP_ERR_NOT_FOUND;
}

static void monitor_task(void* pvParameters) {
    ESP_LOGI(TAG, "System monitor task started");
    
    // Register self for monitoring
    system_monitor_register_task(xTaskGetCurrentTaskHandle(), "sys_monitor", 10000);

    TickType_t last_wake_time = xTaskGetTickCount();
    
    while (1) {
        MONITOR_HEARTBEAT();
        
        // Update system statistics
        update_system_stats();
        
        // Check various system health aspects
        if (sys_mon.config.enable_task_monitoring) {
            check_task_health();
        }
        
        if (sys_mon.config.enable_memory_monitoring) {
            check_memory_health();
        }
        
        // Check overall system health
        system_health_t health = system_monitor_check_health();
        sys_mon.stats.health_status = health;

        // Only trigger recovery for sustained critical health
        static uint32_t critical_count = 0;
        if (health >= SYSTEM_HEALTH_CRITICAL) {
            critical_count++;
            ESP_LOGW(TAG, "Critical system health detected (count: %u)", critical_count);

            // Only trigger recovery after multiple consecutive critical readings
            if (critical_count > 3 && sys_mon.config.enable_auto_recovery) {
                ESP_LOGW(TAG, "Sustained critical health, triggering recovery");
                system_monitor_trigger_recovery("Sustained critical health status");
            }
        } else {
            critical_count = 0;  // Reset counter if health improves
        }
        
        // Sleep until next monitoring cycle
        vTaskDelayUntil(&last_wake_time, pdMS_TO_TICKS(sys_mon.config.monitor_interval_ms));
    }
}

static void check_task_health(void) {
    if (!sys_mon.tasks) return;
    
    uint32_t current_time = xTaskGetTickCount();
    
    if (xSemaphoreTake(sys_mon.mutex, pdMS_TO_TICKS(100)) == pdTRUE) {
        for (uint32_t i = 0; i < sys_mon.config.max_monitored_tasks; i++) {
            if (sys_mon.tasks[i].handle && sys_mon.tasks[i].enabled) {
                uint32_t elapsed = (current_time - sys_mon.tasks[i].last_heartbeat) * portTICK_PERIOD_MS;
                
                if (elapsed > sys_mon.tasks[i].timeout_ms) {
                    sys_mon.tasks[i].timeout_count++;
                    ESP_LOGW(TAG, "Task '%s' timeout detected (elapsed: %u ms, timeout: %u ms, count: %u)",
                             sys_mon.tasks[i].name, elapsed, sys_mon.tasks[i].timeout_ms, 
                             sys_mon.tasks[i].timeout_count);
                    
                    // Reset heartbeat to avoid spam
                    sys_mon.tasks[i].last_heartbeat = current_time;
                    
                    // If too many timeouts, consider the task dead
                    if (sys_mon.tasks[i].timeout_count > 3) {
                        ESP_LOGE(TAG, "Task '%s' appears to be dead", sys_mon.tasks[i].name);
                    }
                }
            }
        }
        xSemaphoreGive(sys_mon.mutex);
    }
}

static void check_memory_health(void) {
    // Check for memory leaks
    uint32_t leaks = mem_check_leaks();
    if (leaks > 0) {
        ESP_LOGW(TAG, "Memory leaks detected: %u", leaks);
    }
    
    // Check for memory corruption
    if (mem_check_corruption()) {
        ESP_LOGE(TAG, "Memory corruption detected!");
    }
    
    // Check heap usage
    multi_heap_info_t heap_info;
    heap_caps_get_info(&heap_info, MALLOC_CAP_INTERNAL);
    
    if (heap_info.total_free_bytes < 10240) {  // Less than 10KB free
        ESP_LOGW(TAG, "Low internal memory: %zu bytes free", heap_info.total_free_bytes);
    }
}

static void update_system_stats(void) {
    uint32_t current_time = xTaskGetTickCount();
    sys_mon.stats.uptime_seconds = (current_time - sys_mon.start_time) * portTICK_PERIOD_MS / 1000;
    
    // Update heap statistics
    multi_heap_info_t heap_info;
    heap_caps_get_info(&heap_info, MALLOC_CAP_INTERNAL);
    sys_mon.stats.free_heap_internal = heap_info.total_free_bytes;
    sys_mon.stats.min_free_heap_internal = heap_info.minimum_free_bytes;
    
    heap_caps_get_info(&heap_info, MALLOC_CAP_SPIRAM);
    sys_mon.stats.free_heap_psram = heap_info.total_free_bytes;
    sys_mon.stats.min_free_heap_psram = heap_info.minimum_free_bytes;
    
    // Update task count
    sys_mon.stats.task_count = uxTaskGetNumberOfTasks();
}

system_health_t system_monitor_check_health(void) {
    system_health_t health = SYSTEM_HEALTH_GOOD;

    // Check memory health - more lenient thresholds
    if (sys_mon.stats.free_heap_internal < 2048) {  // Less than 2KB - critical
        health = SYSTEM_HEALTH_CRITICAL;
        ESP_LOGW(TAG, "Critical: Internal memory very low: %u bytes", sys_mon.stats.free_heap_internal);
    } else if (sys_mon.stats.free_heap_internal < 5120) {  // Less than 5KB - warning
        if (health < SYSTEM_HEALTH_WARNING) {
            health = SYSTEM_HEALTH_WARNING;
        }
        ESP_LOGD(TAG, "Warning: Internal memory low: %u bytes", sys_mon.stats.free_heap_internal);
    }

    // Check for task timeouts - more lenient
    uint32_t critical_timeouts = 0;
    if (sys_mon.tasks && xSemaphoreTake(sys_mon.mutex, pdMS_TO_TICKS(10)) == pdTRUE) {
        for (uint32_t i = 0; i < sys_mon.config.max_monitored_tasks; i++) {
            if (sys_mon.tasks[i].handle && sys_mon.tasks[i].timeout_count > 10) {  // Increased threshold
                critical_timeouts++;
                ESP_LOGW(TAG, "Task '%s' has %u timeouts", sys_mon.tasks[i].name, sys_mon.tasks[i].timeout_count);
            } else if (sys_mon.tasks[i].handle && sys_mon.tasks[i].timeout_count > 5) {
                if (health < SYSTEM_HEALTH_WARNING) {
                    health = SYSTEM_HEALTH_WARNING;
                }
            }
        }
        xSemaphoreGive(sys_mon.mutex);
    }

    // Only trigger critical if multiple tasks are failing
    if (critical_timeouts > 2) {
        health = SYSTEM_HEALTH_CRITICAL;
        ESP_LOGW(TAG, "Multiple tasks failing: %u", critical_timeouts);
    }

    return health;
}

esp_err_t system_monitor_get_stats(system_stats_t* stats) {
    if (!sys_mon.initialized || !stats) {
        return ESP_ERR_INVALID_ARG;
    }
    
    update_system_stats();
    memcpy(stats, &sys_mon.stats, sizeof(system_stats_t));
    return ESP_OK;
}

void system_monitor_print_report(void) {
    if (!sys_mon.initialized) {
        ESP_LOGE(TAG, "System monitor not initialized");
        return;
    }
    
    update_system_stats();
    
    ESP_LOGI(TAG, "=== System Health Report ===");
    ESP_LOGI(TAG, "Uptime: %u seconds", sys_mon.stats.uptime_seconds);
    ESP_LOGI(TAG, "Health: %s", 
             sys_mon.stats.health_status == SYSTEM_HEALTH_GOOD ? "GOOD" :
             sys_mon.stats.health_status == SYSTEM_HEALTH_WARNING ? "WARNING" :
             sys_mon.stats.health_status == SYSTEM_HEALTH_CRITICAL ? "CRITICAL" : "FAILURE");
    ESP_LOGI(TAG, "Tasks: %u", sys_mon.stats.task_count);
    ESP_LOGI(TAG, "Internal heap: %u bytes free (min: %u)", 
             sys_mon.stats.free_heap_internal, sys_mon.stats.min_free_heap_internal);
    ESP_LOGI(TAG, "PSRAM heap: %u bytes free (min: %u)", 
             sys_mon.stats.free_heap_psram, sys_mon.stats.min_free_heap_psram);
    
    // Print memory manager report
    mem_print_report();
}

void system_monitor_set_auto_recovery(bool enable) {
    sys_mon.config.enable_auto_recovery = enable;
    ESP_LOGI(TAG, "Auto recovery %s", enable ? "enabled" : "disabled");
}

void system_monitor_trigger_recovery(const char* reason) {
    ESP_LOGW(TAG, "System recovery triggered: %s", reason ? reason : "Unknown");
    
    // Print final report
    system_monitor_print_report();
    
    // Attempt graceful recovery first
    ESP_LOGW(TAG, "Attempting graceful recovery...");
    
    // Force garbage collection
    // Clean up resources
    
    // If graceful recovery fails, restart
    ESP_LOGE(TAG, "Graceful recovery failed, restarting system...");
    esp_restart();
}
