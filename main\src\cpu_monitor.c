#include "cpu_monitor.h"
#include "esp_log.h"
#include "esp_system.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/semphr.h"
#include <string.h>

static const char* TAG = "CPU_MON";

// Global CPU monitor state
static struct {
    cpu_monitor_config_t config;
    TaskHandle_t monitor_task_handle;
    SemaphoreHandle_t mutex;
    bool initialized;
    bool running;
    cpu_stats_t stats;
    uint32_t last_total_runtime;
    uint32_t last_idle_runtime_core0;
    uint32_t last_idle_runtime_core1;
    uint32_t lvgl_start_time;
    uint32_t lvgl_max_delay;
    bool lvgl_in_progress;
} cpu_mon = {0};

// Forward declarations
static void cpu_monitor_task(void* pvParameters);
static void update_cpu_stats(void);
static void check_task_blocking(void);
static void print_task_stats(void);

esp_err_t cpu_monitor_init(const cpu_monitor_config_t* config) {
    if (cpu_mon.initialized) {
        ESP_LOGW(TAG, "CPU monitor already initialized");
        return ESP_OK;
    }

    if (!config) {
        ESP_LOGE(TAG, "Invalid configuration");
        return ESP_ERR_INVALID_ARG;
    }

    // Copy configuration
    memcpy(&cpu_mon.config, config, sizeof(cpu_monitor_config_t));

    // Set defaults if not specified
    if (cpu_mon.config.monitor_interval_ms == 0) {
        cpu_mon.config.monitor_interval_ms = 1000;
    }
    if (cpu_mon.config.cpu_threshold_percent == 0) {
        cpu_mon.config.cpu_threshold_percent = 90;
    }
    if (cpu_mon.config.blocking_threshold_ms == 0) {
        cpu_mon.config.blocking_threshold_ms = 500;
    }

    // Create mutex
    cpu_mon.mutex = xSemaphoreCreateMutex();
    if (!cpu_mon.mutex) {
        ESP_LOGE(TAG, "Failed to create mutex");
        return ESP_ERR_NO_MEM;
    }

    // Initialize statistics
    memset(&cpu_mon.stats, 0, sizeof(cpu_stats_t));
    
    cpu_mon.initialized = true;
    ESP_LOGI(TAG, "CPU monitor initialized (interval: %u ms, CPU threshold: %u%%, blocking threshold: %u ms)",
             cpu_mon.config.monitor_interval_ms, 
             cpu_mon.config.cpu_threshold_percent,
             cpu_mon.config.blocking_threshold_ms);

    return ESP_OK;
}

esp_err_t cpu_monitor_start(void) {
    if (!cpu_mon.initialized) {
        ESP_LOGE(TAG, "CPU monitor not initialized");
        return ESP_ERR_INVALID_STATE;
    }

    if (cpu_mon.running) {
        ESP_LOGW(TAG, "CPU monitor already running");
        return ESP_OK;
    }

    // Create monitor task
    BaseType_t ret = xTaskCreatePinnedToCore(
        cpu_monitor_task,
        "cpu_monitor",
        4096,
        NULL,
        configMAX_PRIORITIES - 1,  // Highest priority
        &cpu_mon.monitor_task_handle,
        1  // Pin to core 1
    );

    if (ret != pdPASS) {
        ESP_LOGE(TAG, "Failed to create CPU monitor task");
        return ESP_FAIL;
    }

    cpu_mon.running = true;
    ESP_LOGI(TAG, "CPU monitor started");
    return ESP_OK;
}

esp_err_t cpu_monitor_stop(void) {
    if (!cpu_mon.running) {
        return ESP_OK;
    }

    cpu_mon.running = false;
    
    if (cpu_mon.monitor_task_handle) {
        vTaskDelete(cpu_mon.monitor_task_handle);
        cpu_mon.monitor_task_handle = NULL;
    }

    ESP_LOGI(TAG, "CPU monitor stopped");
    return ESP_OK;
}

void cpu_monitor_lvgl_start(void) {
    if (cpu_mon.config.enable_lvgl_monitoring) {
        cpu_mon.lvgl_start_time = xTaskGetTickCount();
        cpu_mon.lvgl_in_progress = true;
    }
}

void cpu_monitor_lvgl_end(void) {
    if (cpu_mon.config.enable_lvgl_monitoring && cpu_mon.lvgl_in_progress) {
        uint32_t elapsed = (xTaskGetTickCount() - cpu_mon.lvgl_start_time) * portTICK_PERIOD_MS;
        if (elapsed > cpu_mon.lvgl_max_delay) {
            cpu_mon.lvgl_max_delay = elapsed;
        }
        
        if (elapsed > cpu_mon.config.blocking_threshold_ms) {
            cpu_mon.stats.lvgl_blocking = true;
            ESP_LOGW(TAG, "LVGL blocking detected: %u ms", elapsed);
        }
        
        cpu_mon.lvgl_in_progress = false;
    }
}

static void cpu_monitor_task(void* pvParameters) {
    ESP_LOGI(TAG, "CPU monitor task started");
    
    TickType_t last_wake_time = xTaskGetTickCount();
    
    while (cpu_mon.running) {
        // Update CPU statistics
        update_cpu_stats();
        
        // Check for task blocking
        if (cpu_mon.config.enable_blocking_detection) {
            check_task_blocking();
        }
        
        // Print report every monitoring interval
        cpu_monitor_print_report();
        
        // Sleep until next monitoring cycle
        vTaskDelayUntil(&last_wake_time, pdMS_TO_TICKS(cpu_mon.config.monitor_interval_ms));
    }
    
    vTaskDelete(NULL);
}

static void update_cpu_stats(void) {
    if (xSemaphoreTake(cpu_mon.mutex, pdMS_TO_TICKS(100)) == pdTRUE) {
        // Update basic stats
        cpu_mon.stats.task_count = uxTaskGetNumberOfTasks();
        cpu_mon.stats.lvgl_max_delay_ms = cpu_mon.lvgl_max_delay;
        
        // Reset LVGL blocking flag (will be set again if blocking occurs)
        cpu_mon.stats.lvgl_blocking = false;
        
        xSemaphoreGive(cpu_mon.mutex);
    }
}

static void check_task_blocking(void) {
    // Check if LVGL is currently blocking
    if (cpu_mon.lvgl_in_progress) {
        uint32_t elapsed = (xTaskGetTickCount() - cpu_mon.lvgl_start_time) * portTICK_PERIOD_MS;
        if (elapsed > cpu_mon.config.blocking_threshold_ms) {
            ESP_LOGW(TAG, "LVGL currently blocking for %u ms", elapsed);
        }
    }
}

void cpu_monitor_print_report(void) {
    if (xSemaphoreTake(cpu_mon.mutex, pdMS_TO_TICKS(10)) == pdTRUE) {
        ESP_LOGI(TAG, "=== CPU Monitor Report ===");
        ESP_LOGI(TAG, "Tasks: %u", cpu_mon.stats.task_count);
        ESP_LOGI(TAG, "LVGL Max Delay: %u ms", cpu_mon.stats.lvgl_max_delay_ms);
        ESP_LOGI(TAG, "LVGL Blocking: %s", cpu_mon.stats.lvgl_blocking ? "YES" : "NO");

        // Memory information - PSRAM focused
        uint32_t free_heap = esp_get_free_heap_size();
        uint32_t min_free_heap = esp_get_minimum_free_heap_size();
        uint32_t free_internal = heap_caps_get_free_size(MALLOC_CAP_INTERNAL);
        uint32_t free_psram = heap_caps_get_free_size(MALLOC_CAP_SPIRAM);
        uint32_t total_psram = heap_caps_get_total_size(MALLOC_CAP_SPIRAM);
        uint32_t used_psram = total_psram - free_psram;

        ESP_LOGI(TAG, "Memory - Total: %u, Min: %u, Internal: %u", free_heap, min_free_heap, free_internal);
        ESP_LOGI(TAG, "PSRAM - Free: %u, Used: %u, Total: %u (%.1f%% used)",
                 free_psram, used_psram, total_psram,
                 total_psram > 0 ? (float)used_psram * 100.0f / total_psram : 0.0f);

        // Print screen change statistics
        extern esp_err_t screen_monitor_get_stats(uint32_t* total_changes, uint32_t* failed_changes,
                                                  uint32_t* max_duration_ms, uint32_t* avg_duration_ms);
        uint32_t total_changes, failed_changes, max_duration, avg_duration;
        if (screen_monitor_get_stats(&total_changes, &failed_changes, &max_duration, &avg_duration) == ESP_OK) {
            ESP_LOGI(TAG, "Screen Changes: %u total, %u failed, max: %u ms, avg: %u ms",
                     total_changes, failed_changes, max_duration, avg_duration);
        }

        // Print task information
        print_task_stats();

        // Reset max delay counter
        cpu_mon.lvgl_max_delay = 0;

        xSemaphoreGive(cpu_mon.mutex);
    }
}

static void print_task_stats(void) {
#if (configUSE_TRACE_FACILITY == 1)
    UBaseType_t task_count = uxTaskGetNumberOfTasks();
    TaskStatus_t* task_status_array = pvPortMalloc(task_count * sizeof(TaskStatus_t));

    if (task_status_array != NULL) {
        UBaseType_t actual_count = uxTaskGetSystemState(task_status_array, task_count, NULL);

        ESP_LOGI(TAG, "Task Status (Name | State | Priority | Stack Free | Stack Warning):");
        for (UBaseType_t i = 0; i < actual_count; i++) {
            const char* state_name;
            switch (task_status_array[i].eCurrentState) {
                case eRunning:   state_name = "RUN"; break;
                case eReady:     state_name = "RDY"; break;
                case eBlocked:   state_name = "BLK"; break;
                case eSuspended: state_name = "SUS"; break;
                case eDeleted:   state_name = "DEL"; break;
                default:         state_name = "UNK"; break;
            }

            // Check for low stack space (less than 512 bytes free)
            const char* stack_warning = "";
            if (task_status_array[i].usStackHighWaterMark < 512) {
                stack_warning = " ⚠️LOW";
            } else if (task_status_array[i].usStackHighWaterMark < 1024) {
                stack_warning = " ⚠️";
            }

            ESP_LOGI(TAG, "  %-12s | %s | %2u | %4u%s",
                     task_status_array[i].pcTaskName,
                     state_name,
                     task_status_array[i].uxCurrentPriority,
                     task_status_array[i].usStackHighWaterMark,
                     stack_warning);

            // Special warning for critical tasks
            if (task_status_array[i].usStackHighWaterMark < 256) {
                ESP_LOGW(TAG, "CRITICAL: Task '%s' has only %u bytes stack free!",
                         task_status_array[i].pcTaskName,
                         task_status_array[i].usStackHighWaterMark);
            }
        }

        vPortFree(task_status_array);
    }
#else
    ESP_LOGI(TAG, "Task stats not available (CONFIG_FREERTOS_USE_TRACE_FACILITY not enabled)");
#endif
}

esp_err_t cpu_monitor_get_stats(cpu_stats_t* stats) {
    if (!stats || !cpu_mon.initialized) {
        return ESP_ERR_INVALID_ARG;
    }

    if (xSemaphoreTake(cpu_mon.mutex, pdMS_TO_TICKS(100)) == pdTRUE) {
        memcpy(stats, &cpu_mon.stats, sizeof(cpu_stats_t));
        xSemaphoreGive(cpu_mon.mutex);
        return ESP_OK;
    }

    return ESP_ERR_TIMEOUT;
}
