#include "app_face.hpp"

#include <list>

#include "esp_log.h"
#include "esp_camera.h"

#include "nvs_flash.h"
#include "nvs.h"

#include "dl_image.hpp"
#include "fb_gfx.h"

#include "who_ai_utils.hpp"

#include "lvgl.h"
#include "ui/ui.h"
#include "esp_lvgl_port.h"

#include "bsp/modbus_params.h"

#include "bsp/ameter_s3.h"

static const char TAG[] = "App/Face";

#pragma pack(push, 1)
typedef struct
{
    uint8_t valid;
    uint8_t faceid;
    uint8_t lv;
    char name[12];
} user_table_t;
#pragma pack(pop)

extern int32_t board_init;
extern uint32_t cur_screen;
extern uint32_t last_screen;
extern user_table_t user_table[10];
extern uint32_t cur_user;
extern uint32_t login_user;
extern uint32_t valid_users;
extern nvs_handle_t nvs_handle_modbus;
extern int32_t log_index;
extern int32_t log_full;
extern struct tm time3;
extern AppCamera *camera_p;

#define RGB565_MASK_RED 0xF800
#define RGB565_MASK_GREEN 0x07E0
#define RGB565_MASK_BLUE 0x001F


#define FRAME_DELAY_NUM 10



extern "C" {

typedef struct {
    lv_img_header_t header; /**< A header describing the basics of the image*/
    uint32_t data_size;     /**< Size of the image in bytes*/
    uint8_t * data;   /**< Pointer to the data of the image*/
} lv_img_dsc_tt;

uint8_t* camera_data;

lv_img_dsc_tt camera_img;

}


static void rgb_print(camera_fb_t *fb, uint32_t color, const char *str)
{
    fb_gfx_print(fb, (fb->width - (strlen(str) * 14)) / 2, 50, color, str);
}

static void rgb_print_title(camera_fb_t *fb, uint32_t color, const char *str)
{
    fb_gfx_print(fb, (fb->width - (strlen(str) * 14)) / 2, 90, color, str);
}

static int rgb_printf(camera_fb_t *fb, uint32_t color, const char *format, ...)
{
    char loc_buf[64];
    char *temp = loc_buf;
    int len;
    va_list arg;
    va_list copy;
    va_start(arg, format);
    va_copy(copy, arg);
    len = vsnprintf(loc_buf, sizeof(loc_buf), format, arg);
    va_end(copy);
    if (len >= sizeof(loc_buf))
    {
        temp = (char *)malloc(len + 1);
        if (temp == NULL)
        {
            return 0;
        }
    }
    vsnprintf(temp, len + 1, format, arg);
    va_end(arg);
    rgb_print(fb, color, temp);
    if (len > 64)
    {
        free(temp);
    }
    return len;
}



AppFace::~AppFace()
{
	ESP_LOGW(TAG, "AppFace is killed........");
    delete this->recognizer;
}

void AppFace::update()
{
    // Parse key

            //this->state = FACE_IDLE;

            this->state = FACE_RECOGNIZE;

            //this->state = FACE_ENROLL;

            //this->state = FACE_DELETE;



    ESP_LOGD(TAG, "Human face recognition state = %d", this->state);
}

static void task(AppFace *self)
{
    ESP_LOGI(TAG, "Start Face ID");
    camera_fb_t *frame = nullptr;

    camera_img.header.always_zero = 0;
    camera_img.header.w = 240;  // Will be updated with actual frame size
    camera_img.header.h = 240;  // Will be updated with actual frame size
    camera_img.data_size = 115200;  // Will be updated with actual frame size
    camera_img.header.cf = LV_IMG_CF_TRUE_COLOR;  //LV_IMG_CF_RAW  LV_IMG_CF_TRUE_COLOR_ALPHA


    ESP_LOGW(TAG, "Face ID task created.................state=%d",self->state);



    self->state_previous = FACE_IDLE;
    // Optimized initialization: reduce initial frame count for faster startup
    int i=3; // Reduced from 20 to 3 frames for much faster startup
    ESP_LOGI(TAG, "Face recognition initializing with %d frames for fast startup", i);
    while(0<i--){
    	if (xQueueReceive(self->queue_i, &frame, 100)){ // Add timeout to prevent hanging
    		// Update camera image descriptor with actual frame dimensions
    		camera_img.header.w = frame->width;
    		camera_img.header.h = frame->height;
    		camera_img.data_size = frame->len;
    		camera_img.data = (uint8_t *)frame->buf;

    		ESP_LOGD(TAG, "Camera frame: %dx%d, size: %d", frame->width, frame->height, frame->len);

    		while(!bsp_display_lock(1000)){bsp_display_unlock();}
    		if( cur_screen==711 ||  cur_screen==72 ){
    			lv_img_set_src(ui_camera_img1, &camera_img);
    		}
    		else{
				lv_img_set_src(ui_camera_img2, &camera_img);
			}
    		bsp_display_unlock();
    		esp_camera_fb_return(frame);
    	} else {
    		ESP_LOGW(TAG, "Timeout waiting for camera frame during initialization");
    		break; // Exit if no frames available
    	}
    	// Remove unnecessary delay for faster startup
    }
    ESP_LOGI(TAG, "Face recognition initialization completed, starting main loop");

    //while (!self->stopped)
    while (1)
    {
        //delete face by face id
        if (self->state == FACE_DELETE )
        {
        	self->recognizer->delete_id(user_table[cur_user].faceid,true);
        	ESP_LOGI(TAG, "Delete ID: %d cur_user=%d", user_table[cur_user].faceid, cur_user);
        	//ESP_LOGI(TAG, "%d IDs left", self->recognizer->get_enrolled_id_num());
        	user_table[cur_user].valid=0;
        	user_table[cur_user].lv=0;
        	user_table[cur_user].faceid=0;
        	strcpy(user_table[cur_user].name,"0");
        	valid_users--;
        	//self->state = FACE_IDLE;
        	//self->state_previous = FACE_DELETE;
        	nvs_set_u8(nvs_handle_modbus, nvs_key("valid",cur_user), 0);
        	nvs_set_u8(nvs_handle_modbus, nvs_key("lv",cur_user), 0);
        	nvs_set_u8(nvs_handle_modbus, nvs_key("faceid",cur_user), 0);
        	nvs_commit(nvs_handle_modbus);

        	vTaskDelay(10);

            self->state = FACE_IDLE;
            self->state_previous = FACE_DELETE;

        	//if(bsp_display_lock(100)){
        		//_ui_flag_modify(ui_PL_ID_del_confrim, LV_OBJ_FLAG_HIDDEN, _UI_MODIFY_FLAG_ADD);
        		//lv_label_set_text(ui_comp_get_child(ui_PL_del_ID, 5*cur_user+5 ),"N/A");
        		//lv_label_set_text(ui_comp_get_child(ui_PL_del_ID, 5*cur_user+3 ),"N/A");
        		//vTaskDelay(10);
        		//bsp_display_unlock();
        	//}
        	//_ui_flag_modify(ui_Btn_Del_Cancel, LV_OBJ_FLAG_HIDDEN, _UI_MODIFY_FLAG_ADD);
        	//_ui_screen_change(&ui_SC_del_ID, LV_SCR_LOAD_ANIM_FADE_ON, 100, 0, &ui_SC_del_ID_screen_init);
        	//vTaskDelete(NULL);
        	cur_screen = 773;
        }

        if (self->queue_i == nullptr){
    		vTaskDelay(20);
    		continue;
    	}

    	if(self->stopped){
    		vTaskDelay(20);
    		if (xQueueReceive(self->queue_i, &frame, portMAX_DELAY)){
    			//ESP_LOGI(TAG, "face to queue_i........not null");
    			esp_camera_fb_return(frame);
    		}
    		continue;
    	}



        if (xQueueReceive(self->queue_i, &frame, portMAX_DELAY))
        {
            if (self->switch_on)
            {
                std::list<dl::detect::result_t> &detect_candidates = self->detector.infer((uint16_t *)frame->buf, {(int)frame->height, (int)frame->width, 3});
                // Yield CPU to allow LVGL and other tasks to run
                vTaskDelay(1);
                std::list<dl::detect::result_t> &detect_results = self->detector2.infer((uint16_t *)frame->buf, {(int)frame->height, (int)frame->width, 3}, detect_candidates);
                // Yield CPU after intensive processing
                vTaskDelay(1);

                //if (detect_results.size())
                //{
                    //print_detection_result(detect_results);
                //    draw_detection_result((uint16_t *)frame->buf, frame->height, frame->width, detect_results);
                //}

                if (self->state)
                {
                    if (detect_results.size() == 1)
                    {
                    	self->recognize_result = self->recognizer->recognize((uint16_t *)frame->buf, {(int)frame->height, (int)frame->width, 3}, detect_results.front().keypoint);
                    	// Only log when there's a meaningful result to avoid log flooding
                    	if (self->recognize_result.id > 0) {
                    		ESP_LOGD(TAG, "Match ID: %d", self->recognize_result.id);
                    	}
                        if (self->state == FACE_ENROLL && self->recognize_result.id == -1 )
                        {
							cur_screen = 121;
                            self->recognizer->enroll_id((uint16_t *)frame->buf, {(int)frame->height, (int)frame->width, 3}, detect_results.front().keypoint, "", true);
                            ESP_LOGI(TAG, "Enroll ID %d cur_user %d", self->recognizer->get_enrolled_ids().back().id, cur_user);
                            self->state = FACE_IDLE;
                            self->state_previous = FACE_ENROLL;
                            self->frame_count = FRAME_DELAY_NUM;
                            user_table[cur_user].valid=1;
                            user_table[cur_user].lv=1;
                            user_table[cur_user].faceid=self->recognizer->get_enrolled_ids().back().id;
                            ////////////////////////////////////////////////
                            vTaskDelay(20);
                            while(!bsp_display_lock(1000)){bsp_display_unlock();}
                            lv_label_set_text_fmt(ui_comp_get_child(ui_PL_del_ID1, 5*cur_user+5 ),"%d",user_table[cur_user].lv);
                            lv_label_set_text_fmt(ui_comp_get_child(ui_PL_del_ID1, 5*cur_user+3 ),"%d(%d)",cur_user,user_table[cur_user].faceid);
                            //_ui_screen_change(&ui_SC_1_2, LV_SCR_LOAD_ANIM_FADE_ON, 2200, 1200, &ui_SC_1_2_screen_init);
                            _ui_screen_change(&ui_SC_ID_EDIT, LV_SCR_LOAD_ANIM_FADE_ON, 2500, 1000, &ui_SC_ID_EDIT_screen_init);
                            bsp_display_unlock();
                            vTaskDelay(10);
                           
                            //self->stopped=1;
                            continue;


                            //lv_label_set_text(ui_comp_get_child(ui_Face_ID_PL_Reg, 5*cur_user+3 ), "Wiser");
                        }
                        if (self->state == FACE_ENROLL && self->recognize_result.id > 0 ){
							for(int i=0; i<10; i++){
                        		if( user_table[i].valid == 1 ){
                        			if( user_table[i].faceid == self->recognize_result.id ){
										ESP_LOGI(TAG, "Match user %d ID %d", i, self->recognize_result.id);
										i=999;
										break;
									}
								}
							}
							if(i!=999){
								ESP_LOGI(TAG, "Unknown esp who ID %d need to delete?", i, self->recognize_result.id);
							}
							
						}
                        else if (self->state == FACE_RECOGNIZE)
                        {
                            //self->recognize_result = self->recognizer->recognize((uint16_t *)frame->buf, {(int)frame->height, (int)frame->width, 3}, detect_results.front().keypoint);
                            // print_detection_result(detect_results);
                            //ESP_LOGI(TAG, "Similarity: %f", self->recognize_result.similarity);
                        	self->state_previous = FACE_RECOGNIZE;
                        	self->frame_count = FRAME_DELAY_NUM;
                        	if (self->recognize_result.id > 0){
                        		//ESP_LOGI(TAG, "Match ID: %d .  ID Matched!!", self->recognize_result.id);
                        		for(int i=0; i<10; i++){
                        			if( user_table[i].valid == 1 ){
                        				if( user_table[i].faceid == self->recognize_result.id ){

                        					ESP_LOGI(TAG, "Match ID: %d user=%d lv=%d", self->recognize_result.id, i, user_table[i].lv);
                        					cur_user = i;
                        					login_user = cur_user;
                        					self->state = FACE_IDLE;
                        					vTaskDelay(20);
                        					//while(!bsp_display_lock(1000)){bsp_display_unlock();}
                        					//bsp_display_unlock();
											///////////////////////////////////////////////////////////////////////////////////////
											ESP_LOGI(TAG, "Save nvs keys user %d log log_index=%d", login_user, log_index);
                        					nvs_set_u16(nvs_handle_modbus, nvs_key("h",(log_index*14)+100), login_user); //id
                        					nvs_set_u16(nvs_handle_modbus, nvs_key("h",(log_index*14)+104), time3.tm_year+1900);
                        					nvs_set_u16(nvs_handle_modbus, nvs_key("h",(log_index*14)+105), time3.tm_mon+1);
                        					nvs_set_u16(nvs_handle_modbus, nvs_key("h",(log_index*14)+106), time3.tm_mday);
                        					nvs_set_u16(nvs_handle_modbus, nvs_key("h",(log_index*14)+107), time3.tm_hour);
                        					nvs_set_u16(nvs_handle_modbus, nvs_key("h",(log_index*14)+108), time3.tm_min);
                        					*(&holding_reg_params_slave.holding_data100+(log_index*14)) = login_user;
                        					*(&holding_reg_params_slave.holding_data104+(log_index*14)) = time3.tm_year+1900;
                        					*(&holding_reg_params_slave.holding_data105+(log_index*14)) = time3.tm_mon+1;
                        					*(&holding_reg_params_slave.holding_data106+(log_index*14)) = time3.tm_mday;
                        					*(&holding_reg_params_slave.holding_data107+(log_index*14)) = time3.tm_hour;
                        					*(&holding_reg_params_slave.holding_data108+(log_index*14)) = time3.tm_min;
                        					log_index++;
                        					if(log_index==10){
                        						log_index=0;
                        					}
                        					if(log_full<9)
                        						log_full=log_index;

                        					nvs_set_i32(nvs_handle_modbus, "log_index", log_index);
                        					vTaskDelay(5);
                        					nvs_set_i32(nvs_handle_modbus, "log_full", log_full);
                        					vTaskDelay(5);
                        					nvs_commit(nvs_handle_modbus);
                        					ESP_LOGI(TAG, "nvs keys comitted. log_index=%d",log_index);
                        					//////////////////////////////////////////////////
                        					vTaskDelay(100);
                        					cur_screen = 701;
                        					//self->stopped=1;
                        					continue;
                        				}
                        			}
                        		}//for(int i=0; i<10; i++){



                        	}
                            else{
                                //ESP_LOGI(TAG, "Match ID: %d", self->recognize_result.id);

                            }//if (self->recognize_result.id > 0){
                        }
                    }
                    //else{
                    //	rgb_print(frame, RGB565_MASK_RED, "Ameter Face ID");  //.\n Please show your face\n at the center center.
                    //}

                    if (self->state == FACE_DELETE ){
						
                        vTaskDelay(10);
                        //self->recognizer->delete_id(true);
                        //ESP_LOGI(TAG, "Delete ID: %d", self->recognize_result.id);
                        //ESP_LOGI(TAG, "%d IDs left", self->recognizer->get_enrolled_id_num());

                        self->recognizer->delete_id(user_table[cur_user].faceid,true);
                        ESP_LOGI(TAG, "Delete ID: %d", user_table[cur_user].faceid);
                        //ESP_LOGI(TAG, "%d IDs left", self->recognizer->get_enrolled_id_num());
                        self->state = FACE_IDLE;
                        self->state_previous = FACE_DELETE;
                        self->frame_count = FRAME_DELAY_NUM;
                        cur_screen = 773;
                    }

                    //self->state_previous = self->state;
                    //self->state = FACE_IDLE;
                    //self->frame_count = FRAME_DELAY_NUM;
                }
                else{
					// No face detected or multiple faces detected
					// Only log occasionally to avoid log flooding
					static uint32_t idle_log_counter = 0;
					idle_log_counter++;
					if (idle_log_counter % 100 == 0) { // Log every 100 frames (~5 seconds at 20fps)
						ESP_LOGD(TAG, "FACE_IDLE - waiting for single face detection (counter: %u)", idle_log_counter);
					}
				}

                //self->state_previous = FACE_RECOGNIZE;

                // Write result on several frames of image
                //ESP_LOGI(TAG, "frame_count=%d state_previous=%d...............", self->frame_count, self->state_previous);
                //rgb_print(frame, RGB565_MASK_RED, "who ?");
                if (self->frame_count)
                {
                    switch (self->state_previous)
                    {
                    case FACE_DELETE:
                        rgb_printf(frame, RGB565_MASK_RED, "%d IDs left", self->recognizer->get_enrolled_id_num());
                        break;

                    case FACE_RECOGNIZE:
                    	//ESP_LOGI(TAG, "Match ID: %d ...............", self->recognize_result.id);
                        if ( login_user != 99 ){
                        	rgb_printf(frame, RGB565_MASK_GREEN, "ID %d Login", cur_user);
                        	//rgb_printf(frame, RGB565_MASK_GREEN, "ID %d", self->recognize_result.id);

                            //if(self->frame_count == 1){
                            	//cur_screen = 3;
                            	//_ui_screen_change(&ui_Screen3, LV_SCR_LOAD_ANIM_MOVE_BOTTOM, 300, 0, &ui_Screen3_screen_init);
                            //}
                        }
                        else{
                            rgb_print(frame, RGB565_MASK_RED, "No Matching ID!");
                            if (self->recognize_result.id > 0){
                            	self->recognizer->delete_id(self->recognize_result.id,true);
                            	ESP_LOGI(TAG, "Delete unknown ID: %d", self->recognize_result.id);
                            }
                        }
                        break;

                    case FACE_ENROLL:
                    	rgb_printf(frame, RGB565_MASK_BLUE, "Enroll: ID %d", cur_user);
                        //rgb_printf(frame, RGB565_MASK_BLUE, "Enroll: ID %d", self->recognizer->get_enrolled_ids().back().id);
                        break;

                    default:
                        break;
                    }
                    //self->state_previous = FACE_IDLE;
                    self->frame_count--;
                }
                else{
                	switch (self->state)
                	{
                	case FACE_RECOGNIZE:
                		//ESP_LOGI(TAG, "Match ID: %d ...............", self->recognize_result.id);
                		if ( login_user != 99 ){
                			rgb_printf(frame, RGB565_MASK_GREEN, "ID %d", self->recognize_result.id);
                		}
                		else
                			rgb_print(frame, RGB565_MASK_RED, "No Matching ID!");
                		break;

                	case FACE_ENROLL:
                		rgb_printf(frame, 0x2BDE8F, "Enroll Face ID!");
                		break;

                	default:
                		break;
                	}

                }
            }

            //////////////////////////////////////////////////////////

            //esp_camera_fb_return(frame);

            //rgb_print_title(frame, 0x2BDE8F, "Ameter Face ID Login");  //.\n Please show your face\n at the center center.

            //ESP_LOGI(TAG, "face queue_o..................");
            // Update camera image descriptor with actual frame dimensions
            camera_img.header.w = frame->width;
            camera_img.header.h = frame->height;
            camera_img.data_size = frame->len;
            camera_img.data = (uint8_t *)frame->buf;

            // Only log frame info occasionally to avoid log flooding
            static uint32_t frame_log_counter = 0;
            frame_log_counter++;
            if (frame_log_counter % 200 == 0) { // Log every 200 frames (~10 seconds at 20fps)
                ESP_LOGD(TAG, "Camera frame: %dx%d, size: %d (counter: %u)", frame->width, frame->height, frame->len, frame_log_counter);
            }

            while(!bsp_display_lock(1000)){bsp_display_unlock();}
          	if( cur_screen==711 ||  cur_screen==72 ){
				lv_img_set_src(ui_camera_img1, &camera_img);
				//ESP_LOGW(TAG, "ui_camera_img1 update");
			}
			else{
				lv_img_set_src(ui_camera_img2, &camera_img);
				//ESP_LOGW(TAG, "ui_camera_img2 update");
			}
            
            bsp_display_unlock();

            vTaskDelay(1); // Reduced delay for faster frame updates
            esp_camera_fb_return(frame);


            //if (self->queue_o){
                //xQueueSend(self->queue_o, &frame, portMAX_DELAY);

            	//ESP_LOGI(TAG, "face queue_o..................");
            	//camera_img.data = (uint8_t *)frame->buf;
            	//lv_img_set_src(ui_Image112, &camera_img);
            	//vTaskDelay(1);
            	//esp_camera_fb_return(frame);

                //esp_camera_fb_return(frame);

            //}
            //else{

                //self->callback(frame);
            //}
        }
    }
    vTaskDelay(7);
    ESP_LOGW(TAG, "Face ID Stopped......................................");

    // Clear task handle before deleting
    if (self->face_task_handle != NULL) {
        self->face_task_handle = NULL;
    }
    vTaskDelete(NULL);

}

void AppFace::run()
{
	if (face_task_handle == NULL) {
		// Create task only if it doesn't exist
		ESP_LOGI(TAG, "Creating face recognition task");
		xTaskCreatePinnedToCore((TaskFunction_t)task, TAG, 4 * 1024, this, 3, &face_task_handle, 0);
	}
	stopped = false;
	ESP_LOGW(TAG, "Face ID task running................state=%d",this->state);
	vTaskDelay(10); // Reduced delay for faster startup
}

void AppFace::stop()
{
	stopped = true;
	ESP_LOGW(TAG, "Face ID task stopping................state=%d",this->state);

	// Give some time for task to stop gracefully
	if (face_task_handle != NULL) {
		vTaskDelay(100); // Wait for task to process stop signal
		// Task will delete itself when stopped
		face_task_handle = NULL;
	}
}



void AppFace::del_user()
{
	this->recognizer->delete_id(user_table[cur_user].faceid,true);
	user_table[cur_user].valid=0;
    user_table[cur_user].lv=0;
    user_table[cur_user].faceid=0;
    strcpy(user_table[cur_user].name,"0");
    valid_users--;
	ESP_LOGW(TAG, "deleting user...........cur_user=%d",cur_user);
}

AppFace::AppFace(QueueHandle_t queue_i,
                 void (*callback)(camera_fb_t *)) : Frame(queue_i, nullptr, callback),
                                                    detector(0.3F, 0.3F, 10, 0.3F),
                                                    detector2(0.4F, 0.3F, 10),
                                                    state(FACE_RECOGNIZE),
                                                    switch_on(true),
													stopped(true),
													face_task_handle(NULL)
{
#if CONFIG_MFN_V1
#if CONFIG_S8
    this->recognizer = new FaceRecognition112V1S8();
#elif CONFIG_S16
    this->recognizer = new FaceRecognition112V1S16();
#endif
#endif

    this->recognizer->set_partition(ESP_PARTITION_TYPE_DATA, ESP_PARTITION_SUBTYPE_ANY, "fr");
    this->recognizer->set_ids_from_flash();

    // Don't create task in constructor - create only when needed via run()
    ESP_LOGI(TAG, "AppFace initialized, task will be created when run() is called");
}
