// This file was generated by SquareLine Studio
// SquareLine Studio version: SquareLine Studio 1.4.2
// LVGL version: 8.3.6
// Project name: A_Meter_GUI_20240305

#ifndef _A_METER_GUI_20240305_UI_H
#define _A_METER_GUI_20240305_UI_H

#ifdef __cplusplus
extern "C" {
#endif

#include "lvgl.h"

#include "ui_helpers.h"
#include "components/ui_comp.h"
#include "components/ui_comp_hook.h"
#include "ui_events.h"

// SCREEN: ui_SC_LOGO
void ui_SC_LOGO_screen_init(void);
void ui_event_SC_LOGO(lv_event_t * e);
extern lv_obj_t * ui_SC_LOGO;
extern lv_obj_t * ui_Lab_ID;
extern lv_obj_t * ui_Lab_Ver;
extern lv_obj_t * ui_Image2;
extern lv_obj_t * ui_Panel1;
extern lv_obj_t * ui_Label17;
extern lv_obj_t * ui_Label2;
// SCREEN: ui_SC_first_login
void ui_SC_first_login_screen_init(void);
extern lv_obj_t * ui_SC_first_login;
extern lv_obj_t * ui_Label1;
extern lv_obj_t * ui_TextArea1;
extern lv_obj_t * ui_Roller1;
void ui_event_BTN_Rig1(lv_event_t * e);
extern lv_obj_t * ui_BTN_Rig1;
void ui_event_BTN_Lif1(lv_event_t * e);
extern lv_obj_t * ui_BTN_Lif1;
// SCREEN: ui_SC_Time_set
void ui_SC_Time_set_screen_init(void);
void ui_event_SC_Time_set(lv_event_t * e);
extern lv_obj_t * ui_SC_Time_set;
extern lv_obj_t * ui_Label26;
extern lv_obj_t * ui_PL_set_date_and_time;
extern lv_obj_t * ui_Roller_set_Year;
extern lv_obj_t * ui_Roller_set_month;
extern lv_obj_t * ui_Roller_set_Day;
extern lv_obj_t * ui_Roller_set_Hour;
extern lv_obj_t * ui_Roller_set_minute;
void ui_event_BTN_set_time_conform(lv_event_t * e);
extern lv_obj_t * ui_BTN_set_time_conform;
extern lv_obj_t * ui_Label27;
void ui_event_BTN_set_time_cancel(lv_event_t * e);
extern lv_obj_t * ui_BTN_set_time_cancel;
extern lv_obj_t * ui_Label60;
// SCREEN: ui_SC_ModBus_set
void ui_SC_ModBus_set_screen_init(void);
extern lv_obj_t * ui_SC_ModBus_set;
extern lv_obj_t * ui_Label19;
extern lv_obj_t * ui_Label_baud;
void ui_event_BTN_ModBus_Master(lv_event_t * e);
extern lv_obj_t * ui_BTN_ModBus_Master;
extern lv_obj_t * ui_Label20;
void ui_event_BTN_ModBus_Slave(lv_event_t * e);
extern lv_obj_t * ui_BTN_ModBus_Slave;
extern lv_obj_t * ui_Label21;
// SCREEN: ui_SC_Face_Rec
void ui_SC_Face_Rec_screen_init(void);
void ui_event_SC_Face_Rec(lv_event_t * e);
extern lv_obj_t * ui_SC_Face_Rec;
extern lv_obj_t * ui_camera_img1;
void ui_event_BTN_exit1(lv_event_t * e);
extern lv_obj_t * ui_BTN_exit1;
extern lv_obj_t * ui_Label28;
void ui_event_BTN_Enroll(lv_event_t * e);
extern lv_obj_t * ui_BTN_Enroll;
extern lv_obj_t * ui_Label57;
extern lv_obj_t * ui_Image1;
// SCREEN: ui_SC_1_1
void ui_SC_1_1_screen_init(void);
void ui_event_SC_1_1(lv_event_t * e);
extern lv_obj_t * ui_SC_1_1;
extern lv_obj_t * ui_Image4;
extern lv_obj_t * ui_Image5;
extern lv_obj_t * ui_Image6;
extern lv_obj_t * ui_Image8;
extern lv_obj_t * ui_Image9;
extern lv_obj_t * ui_Image10;
extern lv_obj_t * ui_Image11;
void ui_event_BTN_login(lv_event_t * e);
extern lv_obj_t * ui_BTN_login;
extern lv_obj_t * ui_Label3;
extern lv_obj_t * ui_Image165;
extern lv_obj_t * ui_Image166;
// SCREEN: ui_SC_2_1
void ui_SC_2_1_screen_init(void);
void ui_event_SC_2_1(lv_event_t * e);
extern lv_obj_t * ui_SC_2_1;
extern lv_obj_t * ui_Image21;
extern lv_obj_t * ui_Image22;
extern lv_obj_t * ui_Image23;
extern lv_obj_t * ui_Image24;
extern lv_obj_t * ui_Image25;
extern lv_obj_t * ui_Image26;
extern lv_obj_t * ui_Image27;
extern lv_obj_t * ui_Image28;
extern lv_obj_t * ui_Image29;
extern lv_obj_t * ui_Image30;
extern lv_obj_t * ui_Image31;
extern lv_obj_t * ui_Image32;
extern lv_obj_t * ui_Label5;
extern lv_obj_t * ui_Label6;
extern lv_obj_t * ui_Lab_Tool_No;
extern lv_obj_t * ui_Lab_Standby_tool_No;
extern lv_obj_t * ui_Image42;
extern lv_obj_t * ui_Image175;
// SCREEN: ui_SC_2_2
void ui_SC_2_2_screen_init(void);
void ui_event_SC_2_2(lv_event_t * e);
extern lv_obj_t * ui_SC_2_2;
extern lv_obj_t * ui_Image12;
extern lv_obj_t * ui_Image33;
extern lv_obj_t * ui_Image34;
extern lv_obj_t * ui_Image35;
extern lv_obj_t * ui_Image36;
extern lv_obj_t * ui_Image37;
extern lv_obj_t * ui_Image38;
extern lv_obj_t * ui_Image39;
extern lv_obj_t * ui_Image40;
extern lv_obj_t * ui_Image41;
extern lv_obj_t * ui_Label7;
extern lv_obj_t * ui_Label8;
extern lv_obj_t * ui_Lab_Tool_No1;
extern lv_obj_t * ui_Lab_Standby_tool_No1;
extern lv_obj_t * ui_Lab_wheel;
extern lv_obj_t * ui_sc3_1_dash_img;
extern lv_obj_t * ui_sc3_1_pointer_img;
extern lv_obj_t * ui_Image176;
extern lv_obj_t * ui_Image177;
// SCREEN: ui_SC_2_3
void ui_SC_2_3_screen_init(void);
void ui_event_SC_2_3(lv_event_t * e);
extern lv_obj_t * ui_SC_2_3;
extern lv_obj_t * ui_Image44;
extern lv_obj_t * ui_Image45;
extern lv_obj_t * ui_Image46;
extern lv_obj_t * ui_Image47;
extern lv_obj_t * ui_Image48;
extern lv_obj_t * ui_Image49;
extern lv_obj_t * ui_Image50;
extern lv_obj_t * ui_Image51;
extern lv_obj_t * ui_Image52;
extern lv_obj_t * ui_Image53;
extern lv_obj_t * ui_Label9;
extern lv_obj_t * ui_Label10;
extern lv_obj_t * ui_Lab_Tool_No2;
extern lv_obj_t * ui_Lab_Standby_tool_No2;
extern lv_obj_t * ui_Lab_RPM;
extern lv_obj_t * ui_Label11;
extern lv_obj_t * ui_sc3_2_dash_img;
extern lv_obj_t * ui_sc3_2_pointer_img;
extern lv_obj_t * ui_Image178;
extern lv_obj_t * ui_Image179;
// SCREEN: ui_SC_2_4
void ui_SC_2_4_screen_init(void);
void ui_event_SC_2_4(lv_event_t * e);
extern lv_obj_t * ui_SC_2_4;
extern lv_obj_t * ui_Image56;
extern lv_obj_t * ui_Image57;
extern lv_obj_t * ui_Image58;
extern lv_obj_t * ui_Image59;
extern lv_obj_t * ui_Image60;
extern lv_obj_t * ui_Image61;
extern lv_obj_t * ui_Image62;
extern lv_obj_t * ui_Image63;
extern lv_obj_t * ui_Image64;
extern lv_obj_t * ui_Image65;
extern lv_obj_t * ui_Label12;
extern lv_obj_t * ui_Lab_wheel1;
extern lv_obj_t * ui_sc3_3_dash_img;
extern lv_obj_t * ui_sc3_3_pointer_img;
extern lv_obj_t * ui_Image180;
extern lv_obj_t * ui_Image181;
// SCREEN: ui_SC_2_5
void ui_SC_2_5_screen_init(void);
void ui_event_SC_2_5(lv_event_t * e);
extern lv_obj_t * ui_SC_2_5;
extern lv_obj_t * ui_Image68;
extern lv_obj_t * ui_Image69;
extern lv_obj_t * ui_Image70;
extern lv_obj_t * ui_Image71;
extern lv_obj_t * ui_Image72;
extern lv_obj_t * ui_Image73;
extern lv_obj_t * ui_Image74;
extern lv_obj_t * ui_Image75;
extern lv_obj_t * ui_Image76;
extern lv_obj_t * ui_Image77;
extern lv_obj_t * ui_Label13;
extern lv_obj_t * ui_Lab_RPM1;
extern lv_obj_t * ui_Label14;
extern lv_obj_t * ui_sc3_4_dash_img;
extern lv_obj_t * ui_sc3_4_pointer_img;
extern lv_obj_t * ui_Image182;
extern lv_obj_t * ui_Image183;
// SCREEN: ui_SC_2_6
void ui_SC_2_6_screen_init(void);
void ui_event_SC_2_6(lv_event_t * e);
extern lv_obj_t * ui_SC_2_6;
extern lv_obj_t * ui_Image80;
extern lv_obj_t * ui_Image81;
extern lv_obj_t * ui_Image82;
extern lv_obj_t * ui_Image83;
extern lv_obj_t * ui_Image84;
extern lv_obj_t * ui_Image85;
extern lv_obj_t * ui_Image86;
extern lv_obj_t * ui_Image87;
extern lv_obj_t * ui_Image88;
extern lv_obj_t * ui_Image89;
extern lv_obj_t * ui_Label15;
extern lv_obj_t * ui_Lab_Ampere;
extern lv_obj_t * ui_Image90;
extern lv_obj_t * ui_Label16;
extern lv_obj_t * ui_Image184;
extern lv_obj_t * ui_Image185;
// SCREEN: ui_SC_3_1
void ui_SC_3_1_screen_init(void);
void ui_event_SC_3_1(lv_event_t * e);
extern lv_obj_t * ui_SC_3_1;
extern lv_obj_t * ui_Image3;
extern lv_obj_t * ui_Image43;
extern lv_obj_t * ui_Image55;
extern lv_obj_t * ui_Image66;
extern lv_obj_t * ui_Image67;
extern lv_obj_t * ui_Image78;
extern lv_obj_t * ui_Image79;
extern lv_obj_t * ui_Image97;
extern lv_obj_t * ui_X_Asix;
extern lv_obj_t * ui_LAB_GC_ASIX;
void ui_event_BTN_GC_L(lv_event_t * e);
extern lv_obj_t * ui_BTN_GC_L;
extern lv_obj_t * ui_Image186;
extern lv_obj_t * ui_Image187;
void ui_event_BTN_GC_R(lv_event_t * e);
extern lv_obj_t * ui_BTN_GC_R;
extern lv_obj_t * ui_PL_GC_Alarm;
extern lv_obj_t * ui_Label41;
void ui_event_BTN_AlarmRst(lv_event_t * e);
extern lv_obj_t * ui_BTN_AlarmRst;
extern lv_obj_t * ui_Label42;
extern lv_obj_t * ui_PL_AlarmRst_PWD;
extern lv_obj_t * ui_Label49;
extern lv_obj_t * ui_TextArea4;
void ui_event_BTN_Lif5(lv_event_t * e);
extern lv_obj_t * ui_BTN_Lif5;
void ui_event_BTN_Rig5(lv_event_t * e);
extern lv_obj_t * ui_BTN_Rig5;
extern lv_obj_t * ui_Roller5;
extern lv_obj_t * ui_PL_AlarmRst_Ani;
extern lv_obj_t * ui_Label46;
extern lv_obj_t * ui_Label50;
// SCREEN: ui_SC_3_3
void ui_SC_3_3_screen_init(void);
void ui_event_SC_3_3(lv_event_t * e);
extern lv_obj_t * ui_SC_3_3;
extern lv_obj_t * ui_Image121;
extern lv_obj_t * ui_Image123;
extern lv_obj_t * ui_Image125;
extern lv_obj_t * ui_Image126;
extern lv_obj_t * ui_Image127;
extern lv_obj_t * ui_Image128;
extern lv_obj_t * ui_Image129;
extern lv_obj_t * ui_Image130;
extern lv_obj_t * ui_Panel7;
extern lv_obj_t * ui_Label32;
extern lv_obj_t * ui_Panel9;
extern lv_obj_t * ui_Label33;
extern lv_obj_t * ui_Panel10;
extern lv_obj_t * ui_Label34;
extern lv_obj_t * ui_Panel11;
extern lv_obj_t * ui_Label35;
extern lv_obj_t * ui_PL_X_Cab_info;
extern lv_obj_t * ui_LB_X_bias;
extern lv_obj_t * ui_PL_Y_Cab_info;
extern lv_obj_t * ui_LB_Y_bias;
extern lv_obj_t * ui_PL_Z_Cab_info;
extern lv_obj_t * ui_LB_Z_bias;
void ui_event_BTN_Cab(lv_event_t * e);
extern lv_obj_t * ui_BTN_Cab;
extern lv_obj_t * ui_LB_Cab;
extern lv_obj_t * ui_Image7;
extern lv_obj_t * ui_Image54;
// SCREEN: ui_SC_3_4
void ui_SC_3_4_screen_init(void);
void ui_event_SC_3_4(lv_event_t * e);
extern lv_obj_t * ui_SC_3_4;
extern lv_obj_t * ui_Image20;
extern lv_obj_t * ui_Image133;
extern lv_obj_t * ui_Image135;
extern lv_obj_t * ui_Image136;
extern lv_obj_t * ui_Image137;
extern lv_obj_t * ui_Image138;
extern lv_obj_t * ui_Image139;
extern lv_obj_t * ui_Image140;
extern lv_obj_t * ui_Image200;
extern lv_obj_t * ui_Image201;
extern lv_obj_t * ui_BTN_EvnClear;
extern lv_obj_t * ui_LB_EnvClear;
extern lv_obj_t * ui_Table_Log;
extern lv_obj_t * ui_Table_Title;
extern lv_obj_t * ui_PL_log;
extern lv_obj_t * ui_Label43;
extern lv_obj_t * ui_Label44;
extern lv_obj_t * ui_Label45;
extern lv_obj_t * ui_Lab_Log_Bound;
extern lv_obj_t * ui_Lab_Log_Max;
extern lv_obj_t * ui_Label48;
extern lv_obj_t * ui_Lab_Log_Date;
extern lv_obj_t * ui_Lab_Log_ID;
extern lv_obj_t * ui_Lab_log_Collision;
extern lv_obj_t * ui_Label_46;
extern lv_obj_t * ui_Lab_log_Min;
void ui_event_BTN_Log_Exit(lv_event_t * e);
extern lv_obj_t * ui_BTN_Log_Exit;
extern lv_obj_t * ui_Label47;
void ui_event_BNT_log_page_up(lv_event_t * e);
extern lv_obj_t * ui_BNT_log_page_up;
void ui_event_BNT_log_page_down(lv_event_t * e);
extern lv_obj_t * ui_BNT_log_page_down;
extern lv_obj_t * ui_Panel14;
void ui_event_BTN_EvnClear(lv_event_t * e);
extern lv_obj_t * ui_Label59;

// SCREEN: ui_SC_6_1
void ui_SC_6_1_screen_init(void);
void ui_event_SC_6_1(lv_event_t * e);
extern lv_obj_t * ui_SC_6_1;
extern lv_obj_t * ui_Image159;
extern lv_obj_t * ui_Image160;
extern lv_obj_t * ui_Image161;
extern lv_obj_t * ui_Image162;
extern lv_obj_t * ui_Image163;
extern lv_obj_t * ui_Label51;
extern lv_obj_t * ui_Panel22;
void ui_event_BTN_Set_Time(lv_event_t * e);
extern lv_obj_t * ui_BTN_Set_Time;
extern lv_obj_t * ui_LB_Set_Time;
void ui_event_BTN_Set_ModBusID(lv_event_t * e);
extern lv_obj_t * ui_BTN_Set_ModBusID;
extern lv_obj_t * ui_LB_Set_ModBusID;
void ui_event_BTN_Set_ModBus_Mode(lv_event_t * e);
extern lv_obj_t * ui_BTN_Set_ModBus_Mode;
extern lv_obj_t * ui_LB_Set_ModBus_Mode;
void ui_event_BTN_Set_PWD(lv_event_t * e);
extern lv_obj_t * ui_BTN_Set_PWD;
extern lv_obj_t * ui_LB_Set_PWD;
extern lv_obj_t * ui_Panel25;
extern lv_obj_t * ui_Admin_PWD;
void ui_event_BTN_Lif3(lv_event_t * e);
extern lv_obj_t * ui_BTN_Lif3;
extern lv_obj_t * ui_Roller3;
void ui_event_BTN_Rig3(lv_event_t * e);
extern lv_obj_t * ui_BTN_Rig3;
extern lv_obj_t * ui_Image147;
extern lv_obj_t * ui_Image148;
// SCREEN: ui_SC_set_ModBus_ID
void ui_SC_set_ModBus_ID_screen_init(void);
extern lv_obj_t * ui_SC_set_ModBus_ID;
extern lv_obj_t * ui_Label54;
extern lv_obj_t * ui_PL_set_date_and_time2;
extern lv_obj_t * ui_ModBusIDx100;
extern lv_obj_t * ui_ModBusIDx10;
extern lv_obj_t * ui_ModBusIDx1;
void ui_event_BTN_set_ModBus_OK(lv_event_t * e);
extern lv_obj_t * ui_BTN_set_ModBus_OK;
extern lv_obj_t * ui_Label55;
void ui_event_BTN_set_ID_cancel(lv_event_t * e);
extern lv_obj_t * ui_BTN_set_ID_cancel;
extern lv_obj_t * ui_Label65;
// SCREEN: ui_SC_set_PWD
void ui_SC_set_PWD_screen_init(void);
extern lv_obj_t * ui_SC_set_PWD;
extern lv_obj_t * ui_Label56;
extern lv_obj_t * ui_TextArea2;
extern lv_obj_t * ui_Roller2;
void ui_event_BTN_Rig2(lv_event_t * e);
extern lv_obj_t * ui_BTN_Rig2;
void ui_event_BTN_Lif2(lv_event_t * e);
extern lv_obj_t * ui_BTN_Lif2;
void ui_event_BTN_set_PWD_OK(lv_event_t * e);
extern lv_obj_t * ui_BTN_set_PWD_OK;
extern lv_obj_t * ui_Label58;
void ui_event_BTN_set_PWD_cancel(lv_event_t * e);
extern lv_obj_t * ui_BTN_set_PWD_cancel;
extern lv_obj_t * ui_Label64;
// Custom keyboard for SC_set_PWD
extern lv_obj_t * ui_CustomKeyboard_PWD;
void ui_event_CustomKeyboard_PWD(lv_event_t * e);
// SCREEN: ui_SC_1_2
void ui_SC_1_2_screen_init(void);
void ui_event_SC_1_2(lv_event_t * e);
extern lv_obj_t * ui_SC_1_2;
extern lv_obj_t * ui_Panel15;
extern lv_obj_t * ui_Label4;
extern lv_obj_t * ui_Panel16;
extern lv_obj_t * ui_Label52;
extern lv_obj_t * ui_PL_del_ID1;
void ui_event_PL_del_ID1_PL_Face_ID_0_Face_ID_0(lv_event_t * e);
void ui_event_PL_del_ID1_PL_Face_ID_0_Face_ID__LV_0(lv_event_t * e);
void ui_event_PL_del_ID1_PL_Face_ID_1_Face_ID_1(lv_event_t * e);
void ui_event_PL_del_ID1_PL_Face_ID_1_Face_ID__LV_1(lv_event_t * e);
void ui_event_PL_del_ID1_PL_Face_ID_2_Face_ID_2(lv_event_t * e);
void ui_event_PL_del_ID1_PL_Face_ID_2_Face_ID__LV_2(lv_event_t * e);
void ui_event_PL_del_ID1_PL_Face_ID_3_Face_ID_3(lv_event_t * e);
void ui_event_PL_del_ID1_PL_Face_ID_3_Face_ID__LV_3(lv_event_t * e);
void ui_event_PL_del_ID1_PL_Face_ID_4_Face_ID_4(lv_event_t * e);
void ui_event_PL_del_ID1_PL_Face_ID_4_Face_ID__LV_4(lv_event_t * e);
void ui_event_PL_del_ID1_PL_Face_ID_5_Face_ID_5(lv_event_t * e);
void ui_event_PL_del_ID1_PL_Face_ID_5_Face_ID__LV_5(lv_event_t * e);
void ui_event_PL_del_ID1_PL_Face_ID_6_Face_ID_6(lv_event_t * e);
void ui_event_PL_del_ID1_PL_Face_ID_6_Face_ID__LV_6(lv_event_t * e);


extern lv_obj_t * ui_LV_confirm2;
extern lv_obj_t * ui_Roller_LV2;
void ui_event_LV_Cofirm_OK2(lv_event_t * e);
extern lv_obj_t * ui_LV_Cofirm_OK2;
extern lv_obj_t * ui_Lab_LV_Con2;
extern lv_obj_t * ui_Image168;
extern lv_obj_t * ui_Image169;
extern lv_obj_t * ui_Image170;
extern lv_obj_t * ui_Image171;
extern lv_obj_t * ui_Image167;
extern lv_obj_t * ui_Image172;
extern lv_obj_t * ui_Image173;
extern lv_obj_t * ui_Image174;
extern lv_obj_t * ui_PL_Admin_PWD;
extern lv_obj_t * ui_Label66;
extern lv_obj_t * ui_TextArea_Login_PWD;
void ui_event_BTN_Lif6(lv_event_t * e);
extern lv_obj_t * ui_BTN_Lif6;
extern lv_obj_t * ui_Roller6;
void ui_event_BTN_Rig6(lv_event_t * e);
extern lv_obj_t * ui_BTN_Rig6;
void ui_event_BTN_login_PWD_OK2(lv_event_t * e);
extern lv_obj_t * ui_BTN_login_PWD_OK2;
extern lv_obj_t * ui_Label67;
void ui_event_BTN_set_PWD_cancel2(lv_event_t * e);
extern lv_obj_t * ui_BTN_set_PWD_cancel2;
extern lv_obj_t * ui_Label68;
// SCREEN: ui_SC_3_2
void ui_SC_3_2_screen_init(void);
void ui_event_SC_3_2(lv_event_t * e);
extern lv_obj_t * ui_SC_3_2;
extern lv_obj_t * ui_Image99;
extern lv_obj_t * ui_Image103;
extern lv_obj_t * ui_Image104;
extern lv_obj_t * ui_Image105;
extern lv_obj_t * ui_Image106;
extern lv_obj_t * ui_Image107;
extern lv_obj_t * ui_Image108;
extern lv_obj_t * ui_Image109;
extern lv_obj_t * ui_LAB_GC_ASIX1;
extern lv_obj_t * ui_Set_X_bound1;
extern lv_obj_t * ui_Panel2;
extern lv_obj_t * ui_Label18;
void ui_event_PL_X_Up_bound1(lv_event_t * e);
extern lv_obj_t * ui_PL_X_Up_bound1;
extern lv_obj_t * ui_X_UP_bon_Value1;
extern lv_obj_t * ui_Panel3;
extern lv_obj_t * ui_Label22;
void ui_event_PL_X_low_bound1(lv_event_t * e);
extern lv_obj_t * ui_PL_X_low_bound1;
extern lv_obj_t * ui_X_low_bon_Value1;
extern lv_obj_t * ui_Panel4;
extern lv_obj_t * ui_Label23;
extern lv_obj_t * ui_Panel_1;
extern lv_obj_t * ui_Label24;
extern lv_obj_t * ui_Image110;
extern lv_obj_t * ui_Image111;
void ui_event_BTN_GC_L1(lv_event_t * e);
extern lv_obj_t * ui_BTN_GC_L1;
void ui_event_BTN_GC_R1(lv_event_t * e);
extern lv_obj_t * ui_BTN_GC_R1;
extern lv_obj_t * ui_PL_X_Value_OK1;
extern lv_obj_t * ui_Panel5;
extern lv_obj_t * ui_Set_X_bon_x2;
extern lv_obj_t * ui_Set_X_bon_x3;
extern lv_obj_t * ui_Set_X_bon_x4;
extern lv_obj_t * ui_Set_X_bon_x5;
void ui_event_BTN_X_bon_Value_OK1(lv_event_t * e);
extern lv_obj_t * ui_BTN_X_bon_Value_OK1;
extern lv_obj_t * ui_LB_Cab3;
extern lv_obj_t * ui_Image110;
extern lv_obj_t * ui_Image111;
void ui_event_BTN_GC_L1(lv_event_t * e);
extern lv_obj_t * ui_BTN_GC_L1;
void ui_event_BTN_GC_R1(lv_event_t * e);
extern lv_obj_t * ui_BTN_GC_R1;
// SCREEN: ui_SC_4_1
void ui_SC_4_1_screen_init(void);
void ui_event_SC_4_1(lv_event_t * e);
extern lv_obj_t * ui_SC_4_1;
extern lv_obj_t * ui_Image101;
extern lv_obj_t * ui_Image102;
extern lv_obj_t * ui_Image114;
extern lv_obj_t * ui_Image115;
extern lv_obj_t * ui_Image116;
extern lv_obj_t * ui_Image117;
extern lv_obj_t * ui_Image122;
extern lv_obj_t * ui_Image124;
extern lv_obj_t * ui_Panel6;
extern lv_obj_t * ui_Label25;
extern lv_obj_t * ui_Label29;
extern lv_obj_t * ui_LBS_TOOL_SET;
extern lv_obj_t * ui_Panel8;
extern lv_obj_t * ui_RO_TOOL_SET1_X10000;
extern lv_obj_t * ui_RO_TOOL_SET1_X1000;
extern lv_obj_t * ui_RO_TOOL_SET1_X100;
extern lv_obj_t * ui_RO_TOOL_SET1_X10;
extern lv_obj_t * ui_RO_TOOL_SET1_X1;
extern lv_obj_t * ui_Label30;
extern lv_obj_t * ui_LAB_TOOL_NO;
extern lv_obj_t * ui_BTN_TOOL_SET1;
void ui_event_BTN_TOOL_SET1(lv_event_t * e);
extern lv_obj_t * ui_Label31;
// SCREEN: ui_SC_4_2
void ui_SC_4_2_screen_init(void);
void ui_event_SC_4_2(lv_event_t * e);
extern lv_obj_t * ui_SC_4_2;
extern lv_obj_t * ui_Image98;
extern lv_obj_t * ui_Image100;
extern lv_obj_t * ui_Image112;
extern lv_obj_t * ui_Image113;
extern lv_obj_t * ui_Image118;
extern lv_obj_t * ui_Image119;
extern lv_obj_t * ui_Image120;
extern lv_obj_t * ui_Image131;
extern lv_obj_t * ui_Panel12;
extern lv_obj_t * ui_Label36;
extern lv_obj_t * ui_Label37;
extern lv_obj_t * ui_LBS_TOOL_SET1;
extern lv_obj_t * ui_Panel13;
extern lv_obj_t * ui_RO_TOOL_SET2_X10000;
extern lv_obj_t * ui_RO_TOOL_SET2_X1000;
extern lv_obj_t * ui_RO_TOOL_SET2_X100;
extern lv_obj_t * ui_RO_TOOL_SET2_X10;
extern lv_obj_t * ui_RO_TOOL_SET2_X1;
extern lv_obj_t * ui_BTN_TOOL_SET2;
extern lv_obj_t * ui_Label39;
void ui_event_BTN_TOOL_SET2(lv_event_t * e);
// SCREEN: ui_SC_5_1
void ui_SC_5_1_screen_init(void);
void ui_event_SC_5_1(lv_event_t * e);
extern lv_obj_t * ui_SC_5_1;
extern lv_obj_t * ui_Image134;
extern lv_obj_t * ui_Image141;
extern lv_obj_t * ui_Image142;
extern lv_obj_t * ui_Image143;
extern lv_obj_t * ui_Image144;
extern lv_obj_t * ui_Image145;
extern lv_obj_t * ui_Image146;
extern lv_obj_t * ui_Label38;
extern lv_obj_t * ui_Image132;
extern lv_obj_t * ui_Label40;
extern lv_obj_t * ui_LAB_exp_date;
extern lv_obj_t * ui_img_lic_ok;
extern lv_obj_t * ui_img_lic_exp;
extern lv_obj_t * ui_Image149;
extern lv_obj_t * ui_Image150;
void ui_event_BTN_Lic_renew(lv_event_t * e);
extern lv_obj_t * ui_BTN_Lic_renew;
extern lv_obj_t * ui_Label74;
extern lv_obj_t * ui_img_dongle_err;
// SCREEN: ui_SC_ID_EDIT
void ui_SC_ID_EDIT_screen_init(void);
extern lv_obj_t * ui_SC_ID_EDIT;
extern lv_obj_t * ui_Label59;
extern lv_obj_t * ui_Label53;
extern lv_obj_t * ui_Keyboard1;
extern lv_obj_t * ui_TA_ID_Name;
extern lv_obj_t * ui_TA_LV;
void ui_event_BTN_Edit_Name(lv_event_t * e);
extern lv_obj_t * ui_BTN_Edit_Name;
void ui_event_BTN_Edit_Level(lv_event_t * e);
extern lv_obj_t * ui_BTN_Edit_Level;
void ui_event_BTN_edit_save(lv_event_t * e);
extern lv_obj_t * ui_BTN_edit_save;
extern lv_obj_t * ui_Label61;
void ui_event_BTN_exit3(lv_event_t * e);
extern lv_obj_t * ui_BTN_exit3;
extern lv_obj_t * ui_Label62;
void ui_event_BTN_edit_NL(lv_event_t * e);
extern lv_obj_t * ui_BTN_edit_NL;
extern lv_obj_t * ui_Label73;
extern lv_obj_t * ui_Select_Edit_NoL;
void ui_event_BTN_Edit_Name(lv_event_t * e);
extern lv_obj_t * ui_BTN_Edit_Name;
extern lv_obj_t * ui_Label76;
void ui_event_BTN_Edit_LV(lv_event_t * e);
extern lv_obj_t * ui_BTN_Edit_LV;
extern lv_obj_t * ui_Label77;
extern lv_obj_t * ui_Label78;
// CUSTOM VARIABLES

// SCREEN: ui_SC_Face_Login
void ui_SC_Face_Login_screen_init(void);
void ui_event_SC_Face_Login(lv_event_t * e);
extern lv_obj_t * ui_SC_Face_Login;
extern lv_obj_t * ui_camera_img2;
void ui_event_BTN_exit2(lv_event_t * e);
extern lv_obj_t * ui_BTN_exit2;
extern lv_obj_t * ui_Label63;
extern lv_obj_t * ui_Image13;
// SCREEN: ui_SC_Key_input
void ui_SC_Key_input_screen_init(void);
extern lv_obj_t * ui_SC_Key_input;
extern lv_obj_t * ui_Label68;
extern lv_obj_t * ui_Keyboard2;
extern lv_obj_t * ui_TA_Lic_code;
void ui_event_BTN_Lic_save(lv_event_t * e);
extern lv_obj_t * ui_BTN_Lic_save;
extern lv_obj_t * ui_Label71;
void ui_event_BTN_exit4(lv_event_t * e);
extern lv_obj_t * ui_BTN_exit4;
extern lv_obj_t * ui_Label72;
extern lv_obj_t * ui_Label70;
extern lv_obj_t * ui____initial_actions0;

extern lv_img_dsc_t * ui_img_background;


LV_IMG_DECLARE(ui__temporary_image);
LV_IMG_DECLARE(ui_img_p1_png);    // assets/p1.png
LV_IMG_DECLARE(ui_img_logo_png);    // assets/logo.png
LV_IMG_DECLARE(ui_img_fusiontech_png);    // assets/fusiontech.png
LV_IMG_DECLARE(ui_img_right_arrow_png);    // assets/Right Arrow.png
LV_IMG_DECLARE(ui_img_right_arrow_1_png);    // assets/Right Arrow_1.png
LV_IMG_DECLARE(ui_img_lift_arrow_png);    // assets/Lift Arrow.png
LV_IMG_DECLARE(ui_img_lift_arrow_1_png);    // assets/Lift Arrow_1.png
LV_IMG_DECLARE(ui_img_user_140_png);    // assets/user_140.png
LV_IMG_DECLARE(ui_img_bu25_png);    // assets/bu25.png
LV_IMG_DECLARE(ui_img_b02_10_png);    // assets/b02_10.png
LV_IMG_DECLARE(ui_img_b01_20_png);    // assets/b01_20.png
LV_IMG_DECLARE(ui_img_tool1_80_png);    // assets/tool1_80.png
LV_IMG_DECLARE(ui_img_tool2_80_png);    // assets/tool2_80.png
LV_IMG_DECLARE(ui_img_load_80_nopin_png);    // assets/load_80_nopin.png
LV_IMG_DECLARE(ui_img_load_80_pin_png);    // assets/load_80_pin.png
LV_IMG_DECLARE(ui_img_222780303);    // assets/rpm_80_nopin&font.png
LV_IMG_DECLARE(ui_img_rpm_80_pin_png);    // assets/rpm_80_pin.png
LV_IMG_DECLARE(ui_img_load_110_nopin_png);    // assets/load_110_nopin.png
LV_IMG_DECLARE(ui_img_rpm_110_pin_png);    // assets/rpm_110_pin.png
LV_IMG_DECLARE(ui_img_1367759103);    // assets/rpm_110_nopin&font.png
LV_IMG_DECLARE(ui_img_tool1_110_png);    // assets/tool1_110.png
LV_IMG_DECLARE(ui_img_b06_30_png);    // assets/b06_30.png
LV_IMG_DECLARE(ui_img_b07_30_png);    // assets/b07_30.png
LV_IMG_DECLARE(ui_img_b04_30_png);    // assets/b04_30.png
LV_IMG_DECLARE(ui_img_b05_30_png);    // assets/b05_30.png
LV_IMG_DECLARE(ui_img_i19_png);    // assets/i19.png
LV_IMG_DECLARE(ui_img_137911575);    // assets/licence-ok.png
LV_IMG_DECLARE(ui_img_i17_90_png);    // assets/i17_90.png
LV_IMG_DECLARE(ui_img_i21_90_png);    // assets/i21_90.png
LV_IMG_DECLARE(ui_img_i19_250120_png);    // assets/i19_250120.png
LV_IMG_DECLARE(ui_img_b03_30_png);    // assets/b03_30.png
LV_IMG_DECLARE(ui_img_b10_14050_png);    // assets/b10_14050.png
LV_IMG_DECLARE(ui_img_load_110_png);    // assets/load_110.png
LV_IMG_DECLARE(ui_img_load_44_png);    // assets/load_44.png
LV_IMG_DECLARE(ui_img_load_80_png);    // assets/load_80.png
LV_IMG_DECLARE(ui_img_p2_png);    // assets/p2.png
LV_IMG_DECLARE(ui_img_p3_png);    // assets/p3.png
LV_IMG_DECLARE(ui_img_p4_png);    // assets/p4.png
LV_IMG_DECLARE(ui_img_rpm_110_png);    // assets/rpm_110.png
LV_IMG_DECLARE(ui_img_rpm_44_png);    // assets/rpm_44.png
LV_IMG_DECLARE(ui_img_rpm_80_png);    // assets/rpm_80.png
LV_IMG_DECLARE(ui_img_tool1_44_png);    // assets/tool1_44.png



LV_FONT_DECLARE(ui_font_WD18);
LV_FONT_DECLARE(ui_font_WD_28);
LV_FONT_DECLARE(ui_font_WD_48);



void ui_init(void);

void lv_msgbox_modbus_error(void);

//void lv_monkey_test(void);

#ifdef __cplusplus
} /*extern "C"*/
#endif

#endif
