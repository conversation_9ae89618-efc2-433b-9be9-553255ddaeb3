#include "screen_monitor.h"
#include "esp_log.h"
#include "esp_heap_caps.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/semphr.h"

static const char* TAG = "SCR_MON";

// Screen monitoring state
static struct {
    bool initialized;
    uint32_t total_changes;
    uint32_t failed_changes;
    uint32_t max_duration_ms;
    uint32_t total_duration_ms;
    uint32_t change_start_time;
    bool change_in_progress;
    int from_screen;
    int to_screen;
    uint32_t free_heap_at_start;
    uint32_t min_free_heap_during_change;
    SemaphoreHandle_t mutex;
} screen_mon = {0};

esp_err_t screen_monitor_init(void) {
    if (screen_mon.initialized) {
        return ESP_OK;
    }

    // Create mutex
    screen_mon.mutex = xSemaphoreCreateMutex();
    if (!screen_mon.mutex) {
        ESP_LOGE(TAG, "Failed to create mutex");
        return ESP_ERR_NO_MEM;
    }

    // Initialize statistics
    screen_mon.total_changes = 0;
    screen_mon.failed_changes = 0;
    screen_mon.max_duration_ms = 0;
    screen_mon.total_duration_ms = 0;
    screen_mon.change_in_progress = false;

    screen_mon.initialized = true;
    ESP_LOGI(TAG, "Screen monitor initialized");
    return ESP_OK;
}

void screen_monitor_change_start(int from_screen, int to_screen) {
    if (!screen_mon.initialized) {
        return;
    }

    if (xSemaphoreTake(screen_mon.mutex, pdMS_TO_TICKS(100)) == pdTRUE) {
        screen_mon.change_start_time = xTaskGetTickCount();
        screen_mon.change_in_progress = true;
        screen_mon.from_screen = from_screen;
        screen_mon.to_screen = to_screen;
        screen_mon.free_heap_at_start = esp_get_free_heap_size();
        screen_mon.min_free_heap_during_change = screen_mon.free_heap_at_start;
        
        ESP_LOGI(TAG, "Screen change start: %d -> %d (Free heap: %u bytes)", 
                 from_screen, to_screen, screen_mon.free_heap_at_start);
        
        xSemaphoreGive(screen_mon.mutex);
    }
}

void screen_monitor_change_end(bool success) {
    if (!screen_mon.initialized || !screen_mon.change_in_progress) {
        return;
    }

    if (xSemaphoreTake(screen_mon.mutex, pdMS_TO_TICKS(100)) == pdTRUE) {
        uint32_t duration = (xTaskGetTickCount() - screen_mon.change_start_time) * portTICK_PERIOD_MS;
        uint32_t free_heap_at_end = esp_get_free_heap_size();
        
        screen_mon.total_changes++;
        screen_mon.total_duration_ms += duration;
        
        if (duration > screen_mon.max_duration_ms) {
            screen_mon.max_duration_ms = duration;
        }
        
        if (!success) {
            screen_mon.failed_changes++;
            ESP_LOGW(TAG, "Screen change FAILED: %d -> %d (Duration: %u ms, Free heap: %u -> %u bytes)",
                     screen_mon.from_screen, screen_mon.to_screen, duration,
                     screen_mon.free_heap_at_start, free_heap_at_end);
        } else {
            ESP_LOGI(TAG, "Screen change success: %d -> %d (Duration: %u ms, Free heap: %u -> %u bytes)",
                     screen_mon.from_screen, screen_mon.to_screen, duration,
                     screen_mon.free_heap_at_start, free_heap_at_end);
        }
        
        // Check for memory leaks
        if (free_heap_at_end < screen_mon.free_heap_at_start - 1024) {  // More than 1KB lost
            ESP_LOGW(TAG, "Possible memory leak detected: %u bytes lost during screen change",
                     screen_mon.free_heap_at_start - free_heap_at_end);
        }
        
        screen_mon.change_in_progress = false;
        xSemaphoreGive(screen_mon.mutex);
    }
}

bool screen_monitor_is_safe_to_change(void) {
    if (!screen_mon.initialized) {
        return true;  // Default to safe if not initialized
    }

    // Check if another screen change is in progress
    if (screen_mon.change_in_progress) {
        ESP_LOGW(TAG, "Screen change already in progress, not safe to start another");
        return false;
    }

    // Check available heap memory
    uint32_t free_heap = esp_get_free_heap_size();
    uint32_t free_internal = heap_caps_get_free_size(MALLOC_CAP_INTERNAL);
    uint32_t free_psram = heap_caps_get_free_size(MALLOC_CAP_SPIRAM);
    
    // Require at least 32KB free internal RAM and 64KB total free memory
    if (free_internal < 32 * 1024) {
        ESP_LOGW(TAG, "Not safe to change screen: low internal RAM (%u bytes)", free_internal);
        return false;
    }
    
    if (free_heap < 64 * 1024) {
        ESP_LOGW(TAG, "Not safe to change screen: low total memory (%u bytes)", free_heap);
        return false;
    }

    // Check task stack space for critical tasks
    TaskHandle_t lvgl_task = xTaskGetHandle("taskLVGL");
    if (lvgl_task) {
        UBaseType_t stack_free = uxTaskGetStackHighWaterMark(lvgl_task);
        if (stack_free < 1024) {  // Less than 1KB free
            ESP_LOGW(TAG, "Not safe to change screen: LVGL task low stack (%u bytes)", stack_free);
            return false;
        }
    }

    TaskHandle_t screen_task = xTaskGetHandle("ScreenChangeEvt");
    if (screen_task) {
        UBaseType_t stack_free = uxTaskGetStackHighWaterMark(screen_task);
        if (stack_free < 1024) {  // Less than 1KB free
            ESP_LOGW(TAG, "Not safe to change screen: ScreenChangeEvt task low stack (%u bytes)", stack_free);
            return false;
        }
    }

    ESP_LOGD(TAG, "Safe to change screen (Free: Internal=%u, PSRAM=%u, Total=%u)", 
             free_internal, free_psram, free_heap);
    return true;
}

esp_err_t screen_monitor_get_stats(uint32_t* total_changes, uint32_t* failed_changes, 
                                   uint32_t* max_duration_ms, uint32_t* avg_duration_ms) {
    if (!screen_mon.initialized || !total_changes || !failed_changes || 
        !max_duration_ms || !avg_duration_ms) {
        return ESP_ERR_INVALID_ARG;
    }

    if (xSemaphoreTake(screen_mon.mutex, pdMS_TO_TICKS(100)) == pdTRUE) {
        *total_changes = screen_mon.total_changes;
        *failed_changes = screen_mon.failed_changes;
        *max_duration_ms = screen_mon.max_duration_ms;
        *avg_duration_ms = screen_mon.total_changes > 0 ? 
                          (screen_mon.total_duration_ms / screen_mon.total_changes) : 0;
        
        xSemaphoreGive(screen_mon.mutex);
        return ESP_OK;
    }

    return ESP_ERR_TIMEOUT;
}
