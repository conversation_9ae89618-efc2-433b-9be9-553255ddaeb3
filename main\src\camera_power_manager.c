#include "camera_power_manager.h"
#include "esp_log.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include <string.h>

static const char* TAG = "CAM_PWR";

// Global camera power manager instance
static camera_power_manager_t cam_pwr_mgr = {0};

// Power state names for logging
static const char* power_state_names[] = {
    "SLEEP", "ACTIVE", "RESETTING"
};

esp_err_t camera_power_manager_init(const camera_power_config_t* config) {
    if (!config || !config->io_expander) {
        ESP_LOGE(TAG, "Invalid configuration");
        return ESP_ERR_INVALID_ARG;
    }

    if (cam_pwr_mgr.initialized) {
        ESP_LOGW(TAG, "Camera power manager already initialized");
        return ESP_OK;
    }

    // Copy configuration
    memcpy(&cam_pwr_mgr.config, config, sizeof(camera_power_config_t));

    // Set default values - optimized for fastest startup
    if (cam_pwr_mgr.config.power_on_delay_ms == 0) {
        cam_pwr_mgr.config.power_on_delay_ms = 10;  // Further reduced for faster response
    }
    if (cam_pwr_mgr.config.reset_delay_ms == 0) {
        cam_pwr_mgr.config.reset_delay_ms = 10;     // Further reduced for faster response
    }
    if (cam_pwr_mgr.config.power_off_delay_ms == 0) {
        cam_pwr_mgr.config.power_off_delay_ms = 5;
    }

    // Initialize state
    cam_pwr_mgr.current_state = CAMERA_POWER_OFF;
    cam_pwr_mgr.last_screen = 0;
    cam_pwr_mgr.current_screen = 0;
    cam_pwr_mgr.camera_needed = false;
    cam_pwr_mgr.power_on_time = 0;
    cam_pwr_mgr.power_off_time = xTaskGetTickCount();

    // Mark as initialized first
    cam_pwr_mgr.initialized = true;

    // Initialize camera pins: PD=High (sleep), Reset=High (not in reset)
    // Camera starts in sleep mode and will be activated only when needed
    ESP_LOGI(TAG, "Setting initial camera state: PD=High (sleep), Reset=High (not in reset)");
    esp_err_t ret = esp_io_expander_set_level(config->io_expander, CAMERA_POWER_DOWN_PIN, 1);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to set initial power down state");
        cam_pwr_mgr.initialized = false;
        return ret;
    }

    ret = esp_io_expander_set_level(config->io_expander, CAMERA_RESET_PIN, 1);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to set initial reset state");
        cam_pwr_mgr.initialized = false;
        return ret;
    }

    // Small delay to ensure pins are set
    vTaskDelay(pdMS_TO_TICKS(10));

    // Set initial state as OFF since camera is in sleep mode
    cam_pwr_mgr.current_state = CAMERA_POWER_OFF;

    ESP_LOGI(TAG, "Camera is ready for hardware initialization");
    ESP_LOGI(TAG, "Camera power manager initialized (auto: %s)",
             config->auto_power_management ? "ON" : "OFF");

    return ESP_OK;
}

esp_err_t camera_power_on(bool force_reset) {
    if (!cam_pwr_mgr.initialized) {
        ESP_LOGE(TAG, "Camera power manager not initialized");
        return ESP_ERR_INVALID_STATE;
    }

    if (cam_pwr_mgr.current_state == CAMERA_POWER_ON && !force_reset) {
        ESP_LOGD(TAG, "Camera already active");
        return ESP_OK;
    }

    ESP_LOGI(TAG, "Activating camera for use (PD=Low)");

    // Set power down pin low to activate camera for use (PD = Low)
    esp_err_t ret = esp_io_expander_set_level(cam_pwr_mgr.config.io_expander,
                                              CAMERA_POWER_DOWN_PIN, 0);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to activate camera");
        return ret;
    }

    // Longer delay for camera to fully wake up and stabilize
    // Camera needs time to exit power down mode and initialize internal circuits
    // Increased delay for better stability after multiple on/off cycles
    vTaskDelay(pdMS_TO_TICKS(150));

    cam_pwr_mgr.current_state = CAMERA_POWER_ON;
    cam_pwr_mgr.power_on_time = xTaskGetTickCount();

    ESP_LOGI(TAG, "Camera woken up successfully");
    return ESP_OK;
}

esp_err_t camera_power_off(void) {
    if (!cam_pwr_mgr.initialized) {
        ESP_LOGE(TAG, "Camera power manager not initialized");
        return ESP_ERR_INVALID_STATE;
    }

    if (cam_pwr_mgr.current_state == CAMERA_POWER_OFF) {
        ESP_LOGD(TAG, "Camera already in sleep mode");
        return ESP_OK;
    }

    ESP_LOGI(TAG, "Putting camera to sleep (PD=High)");

    // Set power down pin high to put camera to sleep
    esp_err_t ret = esp_io_expander_set_level(cam_pwr_mgr.config.io_expander,
                                              CAMERA_POWER_DOWN_PIN, 1);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to put camera to sleep");
        return ret;
    }

    // Longer delay to ensure camera fully enters sleep mode
    // Camera needs time to properly shut down internal circuits
    vTaskDelay(pdMS_TO_TICKS(30));

    cam_pwr_mgr.current_state = CAMERA_POWER_OFF;
    cam_pwr_mgr.power_off_time = xTaskGetTickCount();

    ESP_LOGI(TAG, "Camera put to sleep successfully");
    return ESP_OK;
}

esp_err_t camera_reset(void) {
    if (!cam_pwr_mgr.initialized) {
        ESP_LOGE(TAG, "Camera power manager not initialized");
        return ESP_ERR_INVALID_STATE;
    }

    if (cam_pwr_mgr.current_state == CAMERA_POWER_OFF) {
        ESP_LOGW(TAG, "Cannot reset camera when powered off");
        return ESP_ERR_INVALID_STATE;
    }

    ESP_LOGI(TAG, "Resetting camera");

    cam_pwr_mgr.current_state = CAMERA_POWER_RESETTING;

    // Reset sequence: Low -> High
    esp_err_t ret = esp_io_expander_set_level(cam_pwr_mgr.config.io_expander, 
                                              CAMERA_RESET_PIN, 0);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to assert reset");
        return ret;
    }

    vTaskDelay(pdMS_TO_TICKS(cam_pwr_mgr.config.reset_delay_ms));

    ret = esp_io_expander_set_level(cam_pwr_mgr.config.io_expander, 
                                    CAMERA_RESET_PIN, 1);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to release reset");
        return ret;
    }

    vTaskDelay(pdMS_TO_TICKS(cam_pwr_mgr.config.reset_delay_ms));

    cam_pwr_mgr.current_state = CAMERA_POWER_ON;

    ESP_LOGI(TAG, "Camera reset completed");
    return ESP_OK;
}

bool camera_should_be_powered(uint32_t screen_id) {
    switch (screen_id) {
        case SCREEN_FACE_LOGIN:        // Face login screen
        case SCREEN_FACE_REC_PREP:     // Face recognition preparation
        case SCREEN_FACE_DELETE:       // Face delete screen
        case SCREEN_FACE_REC_ACTIVE:   // Face recognition active
            return true;
        default:
            return false;
    }
}

esp_err_t camera_power_handle_screen_change(uint32_t new_screen, uint32_t old_screen) {
    if (!cam_pwr_mgr.initialized) {
        return ESP_ERR_INVALID_STATE;
    }

    if (!cam_pwr_mgr.config.auto_power_management) {
        ESP_LOGD(TAG, "Auto power management disabled");
        return ESP_OK;
    }

    cam_pwr_mgr.last_screen = old_screen;
    cam_pwr_mgr.current_screen = new_screen;

    bool old_camera_needed = camera_should_be_powered(old_screen);
    bool new_camera_needed = camera_should_be_powered(new_screen);

    ESP_LOGI(TAG, "Screen change: %u -> %u (camera: %s -> %s)",
             old_screen, new_screen,
             old_camera_needed ? "needed" : "not needed",
             new_camera_needed ? "needed" : "not needed");

    if (new_camera_needed && !old_camera_needed) {
        // Camera now needed, check if enough time has passed since last power off
        uint32_t current_time = xTaskGetTickCount();
        uint32_t time_since_power_off = current_time - cam_pwr_mgr.power_off_time;
        uint32_t min_off_time_ms = 200; // Minimum 200ms between power off and power on for better stability

        if (time_since_power_off < pdMS_TO_TICKS(min_off_time_ms)) {
            uint32_t wait_time = pdMS_TO_TICKS(min_off_time_ms) - time_since_power_off;
            ESP_LOGW(TAG, "Camera power debounce: waiting %u ms before power on", pdTICKS_TO_MS(wait_time));
            vTaskDelay(wait_time);
        }

        ESP_LOGI(TAG, "Camera now needed, powering on");
        esp_err_t ret = camera_power_on(false);
        return ret;
    } else if (!new_camera_needed && old_camera_needed) {
        // Camera no longer needed, check if enough time has passed since last power on
        uint32_t current_time = xTaskGetTickCount();
        uint32_t time_since_power_on = current_time - cam_pwr_mgr.power_on_time;
        uint32_t min_on_time_ms = 200; // Minimum 200ms between power on and power off for better stability

        if (time_since_power_on < pdMS_TO_TICKS(min_on_time_ms)) {
            uint32_t wait_time = pdMS_TO_TICKS(min_on_time_ms) - time_since_power_on;
            ESP_LOGW(TAG, "Camera power debounce: waiting %u ms before power off", pdTICKS_TO_MS(wait_time));
            vTaskDelay(wait_time);
        }

        ESP_LOGI(TAG, "Camera no longer needed, powering off");
        return camera_power_off();
    }

    // No change needed
    return ESP_OK;
}

camera_power_state_t camera_power_get_state(void) {
    return cam_pwr_mgr.current_state;
}

void camera_power_set_auto_management(bool enable) {
    cam_pwr_mgr.config.auto_power_management = enable;
    ESP_LOGI(TAG, "Auto power management %s", enable ? "enabled" : "disabled");
}

esp_err_t camera_power_get_stats(uint32_t* power_on_time, uint32_t* power_cycles) {
    if (!cam_pwr_mgr.initialized) {
        return ESP_ERR_INVALID_STATE;
    }

    if (power_on_time) {
        if (cam_pwr_mgr.current_state == CAMERA_POWER_ON) {
            uint32_t current_time = xTaskGetTickCount();
            *power_on_time = (current_time - cam_pwr_mgr.power_on_time) * portTICK_PERIOD_MS;
        } else {
            *power_on_time = 0;
        }
    }

    if (power_cycles) {
        // This would need to be tracked separately if needed
        *power_cycles = 0;
    }

    return ESP_OK;
}

void camera_power_print_status(void) {
    if (!cam_pwr_mgr.initialized) {
        ESP_LOGE(TAG, "Camera power manager not initialized");
        return;
    }

    ESP_LOGI(TAG, "=== Camera Power Status ===");
    ESP_LOGI(TAG, "State: %s", power_state_names[cam_pwr_mgr.current_state]);
    ESP_LOGI(TAG, "Auto management: %s", cam_pwr_mgr.config.auto_power_management ? "ON" : "OFF");
    ESP_LOGI(TAG, "Current screen: %u", cam_pwr_mgr.current_screen);
    ESP_LOGI(TAG, "Camera needed: %s", camera_should_be_powered(cam_pwr_mgr.current_screen) ? "YES" : "NO");

    if (cam_pwr_mgr.current_state == CAMERA_POWER_ON) {
        uint32_t uptime = (xTaskGetTickCount() - cam_pwr_mgr.power_on_time) * portTICK_PERIOD_MS;
        ESP_LOGI(TAG, "Power on time: %u ms", uptime);
    }
}

esp_err_t camera_power_boot_init(void) {
    if (!cam_pwr_mgr.initialized) {
        ESP_LOGE(TAG, "Camera power manager not initialized");
        return ESP_ERR_INVALID_STATE;
    }

    ESP_LOGI(TAG, "Starting camera boot initialization sequence");

    // Step 1: Camera Reset Low for 100ms
    ESP_LOGI(TAG, "Step 1: Camera Reset Low for 100ms");
    esp_err_t ret = esp_io_expander_set_level(cam_pwr_mgr.config.io_expander, CAMERA_RESET_PIN, 0);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to set reset low");
        return ret;
    }
    vTaskDelay(pdMS_TO_TICKS(100));

    // Step 2: Camera Reset High
    ESP_LOGI(TAG, "Step 2: Camera Reset High");
    ret = esp_io_expander_set_level(cam_pwr_mgr.config.io_expander, CAMERA_RESET_PIN, 1);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to set reset high");
        return ret;
    }
    vTaskDelay(pdMS_TO_TICKS(50));

    // Step 3: Camera PD pin Low (activate for initialization)
    ESP_LOGI(TAG, "Step 3: Camera PD pin Low (activate for initialization)");
    ret = esp_io_expander_set_level(cam_pwr_mgr.config.io_expander, CAMERA_POWER_DOWN_PIN, 0);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to set PD low");
        return ret;
    }
    vTaskDelay(pdMS_TO_TICKS(50));

    cam_pwr_mgr.current_state = CAMERA_POWER_ON;
    ESP_LOGI(TAG, "Camera boot initialization sequence completed - ready for hardware init");
    return ESP_OK;
}

esp_err_t camera_power_finalize_init(void) {
    if (!cam_pwr_mgr.initialized) {
        ESP_LOGE(TAG, "Camera power manager not initialized");
        return ESP_ERR_INVALID_STATE;
    }

    ESP_LOGI(TAG, "Finalizing camera power initialization");

    // Put camera to sleep mode after hardware initialization is complete
    esp_err_t ret = camera_power_off();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to put camera to sleep mode");
        return ret;
    }

    ESP_LOGI(TAG, "Camera power management ready - camera hardware initialized and in sleep mode");
    return ESP_OK;
}
